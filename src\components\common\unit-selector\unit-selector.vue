<template>
    <div class="dht-unit-selector">
      <fx-popover
        v-if="visibleUnits.length > 1"
        :placement="position"
        :visible-arrow="visibleArrow"
        :popper-class="noPadding ? 'dht-popover-no-padding' : ''"
        width="150"
        :open-delay="50"
        :close-delay="50"
        v-model="visible"
        trigger="hover">
        <span slot="reference">
          {{value ? value.name : ''}}<i class="el-icon-arrow-down"></i>
        </span>
        <ul class="dht-unit-selector-list"
            :style="{
               padding: noPadding ? '12px' : '0'
            }"
            @mouseenter="$emit('enter')"
            @mouseleave="$emit('leave')"
            @mousedown.prevent>
          <li v-for="unit in visibleUnits"
              :key="unit.unit_id"
              class="dht-unit-selector-list-item"
              :class="{checked: unit.unit_id === value.unit_id}"
              @click="onSelect(unit)">
            <span class="dht-unit-selector-list-item-unit">
              <i class="el-icon-check"></i>{{unit.name}}
            </span>
            <span class="dht-unit-selector-list-item-tip" v-if="!unit.is_base">
              {{unit.conversion_ratio + baseUnitName}}
            </span>
          </li>
        </ul>
      </fx-popover>
      <span v-else>{{value ? value.name : ''}}</span>
    </div>
</template>

<script src="./_unit-selector.ts" lang="ts"></script>
<style src="./_unit-selector.less" lang="less"></style>
