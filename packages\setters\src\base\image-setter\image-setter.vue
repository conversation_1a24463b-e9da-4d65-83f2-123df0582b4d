<template>
    <div class="bc-img-setter">
        <fx-popover :useDefZIndex="true" :visible-arrow="false" width="258" trigger="click" class="bc-img-popover"
            popper-class="bc-img-dropdown">
            <div class="bc-img-body" slot="reference">
                <div class="bc-img-body-left">
                    <fx-image v-if="cImageValue.src" class="bc-img" :src="cImageValue.src"></fx-image>
                </div>
                <div class="bc-img-body-main">
                    <span class="bc-img-container-label">{{ $t('图片') }}</span>
                </div>
            </div>
            <div class="bc-img-dropdown-body">
                <div class="bc-setter-field bc-setter-block-field" v-if="showImage && (!cImageValue.src || cImageValue.sourceType !== 'url')">
                    <div class="bc-setter-field-body">
                        <fx-upload
                            class="bc-img-uploader"
                            url="#"
                            :max-size="maxSize"
                            :show-file-list="false"
                            :accept="'.png,.jpg'"
                            :on-change="onUploadChange"
                            :on-exceed="onUploadExceed"
                            :disabled="!sourceType.includes('local')"
                        >
                            <div class="bc-uploader-aera" @click="handleUploadClick">
                                <template v-if="cImageValue.src">
                                    <fx-image class="bc-uploader-img" :src="cImageValue.src"></fx-image>
                                    <i class="el-icon-sort"></i>
                                    <i class="el-icon-delete bc-uploader-delete-icon" @click.stop="handleRemove"></i>
                                </template>
                                <i v-else class="el-icon-plus bc-uploader-updown-icon"></i>
                            </div>
                            <div slot="tip" class="el-upload__tip" v-if="maxSize && maxSizeInMBLabel">
                                {{ maxSizeInMBLabel }}
                            </div>
                        </fx-upload>
                    </div>
                </div>
                <div class="bc-setter-field bc-setter-block-field" v-if="showUrl && (!cImageValue.src || cImageValue.sourceType === 'url')">
                    <div class="bc-setter-field-head">
                        <div class="bc-setter-field-title">
                            {{ $t('beecraft.setters.backgroundImg.url', '图片URL地址') }}
                        </div>
                    </div>
                    <div class="bc-setter-field-body">
                        <fx-input size="mini" :value="cImageValue.src" :maxlength="2000" @change="updateUrl"></fx-input>
                        <div class="bc-setter-field-tip--error" v-if="urlError">
                            {{ $t('beecraft.setters.backgroundImg.url.tip', '请输入合法的图片URL') }}
                        </div>
                    </div>
                </div>
                <div class="bc-setter-field bc-setter-block-field" v-if="showHeight && value.size === 'cover'">
                    <div class="bc-setter-field-head">
                        <div class="bc-setter-field-title">
                            {{ $t('beecraft.setters.backgroundImg.height', '图片高度') }}
                        </div>
                    </div>
                    <div class="bc-setter-field-body">
                        <LengthUnitSetter :value="value.height" :units="heightUnits" :maxlength="2000"
                            @change="changeProps($event, 'height')"></LengthUnitSetter>
                    </div>
                </div>
                <div class="bc-setter-field bc-setter-block-field" v-if="showAdaptaion">
                    <div class="bc-setter-field-head">
                        <div class="bc-setter-field-title">
                            {{ $t('beecraft.setters.backgroundImg.size.type', '图片适配方式') }}
                        </div>
                    </div>
                    <div class="bc-setter-field-body">
                        <fx-select width="100%" :placeholder="$t('请选择')" size="mini" v-model="value.size" :options="options"
                            @change="handleAdaptaionChange">
                        </fx-select>
                    </div>
                </div>
                <div class="bc-setter-field bc-setter-block-field" v-if="showAlignType">
                    <div class="bc-setter-field-head">
                        <div class="bc-setter-field-title">
                            {{ $t('beecraft.setters.backgroundImg.alignType', '对齐方式') }}
                        </div>
                    </div>
                    <div class="bc-setter-field-body">
                        <AlignmentScale v-model="value.position" @change="changeProps($event, 'position')"></AlignmentScale>
                    </div>
                </div>
                <div class="bc-setter-field bc-setter-block-field" v-if="showOpacity">
                    <div class="bc-setter-field-head">
                        <div class="bc-setter-field-title">
                            {{ $t('beecraft.setters.backgroundImg.opacity', '图片透明度') }}
                        </div>
                    </div>
                    <div class="bc-setter-field-body">
                        <fx-input
                            ref="opacityInput"
                            :value="value.opacity ? value.opacity * 100 : '100'"
                            size="small"
                            @change="updateOpacity"
                            type="number"
                        >
                        <i slot="suffix" class="percent-suffix">%</i>
                    </fx-input>
                    </div>
                </div>
            </div>
        </fx-popover>
        <SelectResourceDialog
            :visible.sync="selectResourceDialogVisible"
            type="linkApp"
            :editData="cImageValue"
            v-bind="cmsArgs"
            :workSpaceApiName="imageWorkSpaceApiName"
            :on-select="handleResourceSelect"
            :z-index="dCmsDialogZindex"
            @close="selectResourceDialogVisible = false;"
        />
    </div>
</template>
<script>
import AlignmentScale from './alignment-scale.vue';
import LengthUnitSetter from '../length-unit-setter/length-unit-setter.vue';
let imageWorkSpaceApiName = '';
export default {
    components: {
        AlignmentScale,
        LengthUnitSetter,
        SelectResourceDialog: () => Fx.getBizComponent('paasbiz', 'UipaasCmsSelectResourceDialog').then(res => res())
    },
    props: {
        value: {
            type: Object,
            default: () => ({
                // opacity: 1,
                // position: 'center center',
                // size: 'cover',
                // height: '350px',
                // url: '',
                // image: '',
            })
        },
        prefix: {
            type: String,
            default: ''
        },
        // 图片高度单位
        heightUnits: {
            type: Array,
            default: () => ['px']
        },
        // 是否显示图片上传
        showImage: {
            type: Boolean,
            default: true
        },
        // 是否显示图片url设置项
        showUrl: {
            type: Boolean,
            default: true
        },
        // 是否显示图片高度设置项
        showHeight: {
            type: Boolean,
            default: true
        },
        // 是否显示图片适配方式设置项
        showAdaptaion: {
            type: Boolean,
            default: true
        },
        // 是否显示图片对齐方式设置项
        showAlignType: {
            type: Boolean,
            default: true
        },
        // 是否显示图片透明度设置项
        showOpacity: {
            type: Boolean,
            default: false
        },
        // 获取图片源文件
        getSourceFile: {
            type: Function,
            default: true
        },
        // 最大尺寸
        maxSize: {
            type: Number,
            default: 1536
        },
        // 原生上传
        sourceType: {
            type: Array,
            default: () => ['local']
        },
        // 资源类型
        cmsArgs: {
            type: Object,
            default: () => ({
                workSpaceApiName: '',
                apiName: ''
            })
        }
    },
    data() {
        return {
            options: [],
            urlError: false,
            selectResourceDialogVisible: false,
            imageSrcFromCms: '', //不希望cms返回的url保存到组件描述中，因此需要单独保存
            imageWorkSpaceApiName: imageWorkSpaceApiName,
            dCmsDialogZindex: FxUI.Utils.getPopupZIndex() + 1
        };
    },
    computed: {
        maxSizeInMBLabel() {
            const { sourceType } = this;
            if (sourceType.length === 1 && sourceType[0] === 'cms') {
                return '';
            }

            const maxSizeInMB = this.maxSize > 0 ? (this.maxSize / 1024).toFixed(1) : '0';
            return $t(
                'beecraft.setters.backgroundImg.size',
                { size: maxSizeInMB },
                '仅支持jpg、png格式；大小不超过{{size}}M',
            );
        },
        cImageValue() {
            const { image } = this.value;
            if(!image) {
                return {
                    sourceType: '',
                    src: ''
                }
            }
            if(typeof image === 'string') {
                if(image.startsWith('data:')) {
                    // base64的方式
                    return {
                        sourceType: 'local',
                        src: image
                    }
                }else if(image.indexOf('C_') > -1) {
                    // 资源文件
                    return {
                        sourceType: 'local',
                        src: image
                    }
                }else {
                    // url的方式，不一定是http
                    return {
                        sourceType: 'url',
                        src: image
                    }
                }
            }else {
                if (image.type === 'cms') {
                    if (!this.imageSrcFromCms) {
                        this._getImageSrcFromCms(image.value);
                        return {
                            sourceType: '',
                            src: ''
                        };
                    }
                    return {
                        sourceType: 'cms',
                        src: this.imageSrcFromCms
                    }
                }else if (image.type === 'imagePath') {
                    return {
                        sourceType: 'local',
                        src: image.value
                    }
                }
            }
        }
    },
    created() {
        this.options = [
            {
                label: $t('beecraft.setters.backgroundImg.contain', {}, '等比缩放'),
                value: 'contain',
            },
            {
                label: $t('beecraft.setters.backgroundImg.cover', {}, '填充容器'),
                value: 'cover',
            },
        ];
    },

    methods: {
        changeProps: (function () {
            let delay = undefined;
            let changeData = undefined;

            return function (value, key) {
                const { prefix } = this;
                if (!changeData) {
                    changeData = {
                        ...this.value,
                    }
                }
                changeData[prefix ? `${prefix}${key.charAt(0).toUpperCase()}${key.slice(1)}` : key] = value;
                if (delay) {
                    clearTimeout(delay);
                }
                delay = setTimeout(() => {
                    this.$emit('change', changeData, key);
                    changeData = undefined;
                    delay = undefined;
                }, 0);
            }
        })(),
        onUploadChange(file) {
            const { sourceType } = this;

            if(sourceType.includes('local')) {
                if (this.getSourceFile) {
                    const rawFile = file.raw;
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const base64data = e.target.result;
                        this.changeProps('center center', 'position');
                        this.changeProps({
                            type: 'imagePath',
                            value: base64data,
                            file
                        }, 'image');
                    };
                    reader.readAsDataURL(rawFile);
                }else {
                    if (Fx) {
                        let upLoader = Fx.file.upload(file, {
                            autoAliyun: false,
                            uploadByStreamParams: {
                                url: '/FSC/EM/File/UploadByForm'
                            },
                            progress(data) {

                            },
                            success: (data) => {
                                this.changeProps('center center', 'position');
                                this.changeProps({ path: data.filePath }, 'image');
                            },
                            fail() {

                            }
                        });
                    }
                }
            }
        },
        onUploadExceed(file, fileList, msg) {
            console.log(file, fileList, msg);
            if (msg.msg) {
                this.$message({
                    message: msg.msg,
                    type: 'error'
                })
            }
        },
        handleAdaptaionChange(value) {
            if (value === 'cover') {
                this.changeProps('350px', 'height');
            } else {
                this.changeProps(undefined, 'height');
            }
            // this.changeProps('center center', 'position');
            this.changeProps(value, 'size');
        },

        // 处理url
        updateUrl(url) {
            // 校验url是否合法
            // if (url && !/^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/.test(url)) {
            //     this.urlError = true;
            //     return;
            // }
            // this.urlError = false;
            // this.changeProps(url, 'url');
            this.changeProps(url, 'image');
        },

        // 处理透明度
        updateOpacity(value) {
            let newValue = value;
            if (!newValue) {
                newValue = 100;
            }else if (+newValue < 0) {
                newValue = 0;
            } else if (+newValue > 100) {
                newValue = 100;
            }
            if(newValue !== value) {
                this.$refs.opacityInput.setCusInputValue(newValue);
            }
            this.changeProps(newValue / 100, 'opacity');
        },

        // 处理CMS资源选择
        handleResourceSelect(data) {
            data = Array.isArray(data) ? data[0] : data;
            this.imageSrcFromCms = data.url;
            this.changeProps({
                type: 'cms',
                value: data.apiName,
                defaultValue: data.url,
            }, 'image');
            imageWorkSpaceApiName = data.workSpaceApiName;
        },

        // 处理上传点击
        handleUploadClick(e) {
            if(this.sourceType.includes('cms')) {
                e.stopPropagation();
                e.preventDefault();
                this.selectResourceDialogVisible = true;
                this.dCmsDialogZindex = FxUI.Utils.getPopupZIndex() + 1;
            }
        },

        // 处理删除
        handleRemove() {
            this.imageSrcFromCms = '';
            this.changeProps('', 'image');
        },

        _getImageSrcFromCms(apiName) {
            Fx.getBizAction('paasbiz', 'callFetch', {
                name: 'fetchCMSFileInfo',
                data: {
                    apiNameList: [apiName]
                }
            }).then(res => {
                if(res.Result.StatusCode === 0) {
                    const resourceInfo = res.Value.resourceInfos.find(item => item.apiName === apiName);
                    if(resourceInfo) {
                        this.imageSrcFromCms = resourceInfo.url;
                        this.imageWorkSpaceApiName = imageWorkSpaceApiName = resourceInfo.workSpaceApiName;
                    }
                }
            });
        }
    },
};
</script>

<style lang="less" scoped>
.bc-img-popover {
    width: 100%;

    .bc-img-body {
        display: flex;
        align-items: center;
        width: 100%;
        min-height: 28px;
        max-height: 48px;
        border: 1px solid var(--color-neutrals07, #c1c5ce);
        border-radius: 4px;
        cursor: pointer;

        &:focus {
            border: 1px solid var(--color-primary06, #ff8000);

            .bc-img-body-left {
                border: 1px solid var(--color-primary06, #ff8000);
            }
        }

        .bc-img-body-left {
            width: 20px;
            height: 20px;
            border: 1px solid var(--color-neutrals07, #c1c5ce);
            margin-left: 4px;

            &:empty {
                background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==);
                background-position: center;
            }

            .bc-img {
                width: 20px;
                height: 20px;
            }
        }

        .bc-img-body-main {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .bc-img-container-label {
                margin-left: 4px;
            }
        }
    }
}

.bc-img-dropdown {
    .bc-img-dropdown-body {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .bc-img-uploader {
            min-height: 88px;

            .bc-uploader-aera {
                position: relative;
                width: 258px;
                height: 68px;
                background: var(--color-neutrals02, #fafafa);

                .bc-uploader-img {
                    width: 258px;
                    height: 68px;
                }

                .bc-uploader-delete-icon {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    font-size: 16px;
                    top: 0;
                    left: 0;
                    line-height: 68px;
                    background-color: #212b3699;
                    color: var(--color-neutrals01, #ffffff);
                }

                .bc-uploader-updown-icon {
                    position: absolute;
                    font-size: 16px;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    color: #171313;
                }
            }
        }

        .image-height-suffix {
            width: 20px;
        }

        /deep/.el-input-group__append {
            background-color: #fff;
        }
    }
}
.bc-setter-field-tip--error {
    color: var(--color-danger06);
}
.percent-suffix {
    display: flex;
    align-items: center;
    height: 100%;
    margin-right: 4px;
}
</style>
<style lang="less">
.bc-img-dropdown.el-popper {
    margin-top: 0;
}

.bc-img-dropdown {
    padding: 12px;
    // z-index: 2001!important;
    .bc-img-dropdown-body {
        .bc-img-uploader {
            .el-upload {
                width: 100%;
                border: 1px dashed var(--color-neutrals05);
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }

            .el-upload:hover,
            .el-upload:focus {
                border-color: var(--color-primary05, #ff9b29);
            }

            .el-upload:active {
                border-color: var(--color-primary07, #d96500);
            }

            .el-upload__tip {
                margin-top: -1px;
            }
        }
    }
}
</style>
