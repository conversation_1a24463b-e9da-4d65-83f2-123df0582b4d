const path = require('path');
const webpack = require('webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const webpackMerge = require('webpack-merge');
const webpackBaseConfig = require('./webpack.base.config.js');
const utils = require('./util.js');

const {
  webpackConfig,
  proxy_target,
  devDirectory = 'dev',
  projectName,
} = require('../project.config.js');

module.exports = (env, argv) => {
  argv.mode = 'development';

  const webpack_config = utils.result({ webpackConfig }, 'webpackConfig', [env, argv]);
  webpack_config.entry = {
    dht: './src/dht',
  };
  return webpackMerge(
    webpackBaseConfig,
    {
      mode: 'development',
      devtool: 'inline-source-map',
      output: {
        path: path.resolve(__dirname, `../${devDirectory}`),
        publicPath: `html/${projectName}/`,
      },
      module: {
        rules: [
          {
            test: /\.less$/,
            use: [
              'vue-style-loader',
              'css-loader',
              'postcss-loader',
              'less-loader',
              {
                loader: 'style-resources-loader',
                options: {
                  patterns: [
                    path.resolve(__dirname, '../src/less/variables.less'),
                  ],
                  injector: (source, resources) => {
                    return `${resources.map(({ content }) => content).join('\n')}\n${source}`;
                  },
                },
              },
            ],
            exclude: /node_modules/,
          },
          {
            test: /\.css$/,
            use: ['vue-style-loader', 'css-loader', 'postcss-loader'],
            // exclude: /node_modules/
          },
          {
            test: /\.(gif|jpg|png|svg)\??.*$/,
            loader: 'url-loader',
            options: {
              limit: 8192,
              name: 'images/[name].[ext]',
            },
          },
        ],
      },
      devServer: {
        port: 8080,
        host: 'localhost',
        inline: true,
        https: {
          spdy: {
            protocols: ['http/1.1'],
          },
        },
        hotOnly: true,
        openPage: '/XV/Home',
        disableHostCheck: true, // That solved `Invalid Host header` error
        headers: {
          'Access-Control-Allow-Origin': '*',
        },
        proxy: {
          '**': {
            target: proxy_target,
            secure: false,
            changeOrigin: false,
            withCredentials: true,
            // bypass: function b(req, res, proxyOptions) {
            //   console.log(req.url, res, proxyOptions);
            //   if (req.url.indexOf('hot-update') >= 0) {
            //     return req.url.replace('/XV/Home', '');
            //   }
            // },
          },
        },
      },
      plugins: [
        new CopyWebpackPlugin([
          {
            from: path.resolve(__dirname, '../src/assets'),
            to: path.resolve(__dirname, '../dev/assets'),
          },
        ]),
        new webpack.DefinePlugin({
          'process.env.NODE_ENV': JSON.stringify('development'),
        }),
      ],
    },
    webpack_config,
  );
};
