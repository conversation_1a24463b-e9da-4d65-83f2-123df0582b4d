<template>
  <div class="base-setting">
    <!-- 基本信息设置 -->
    <div class="setting-section">
      <div class="dht-name dht-title" @click="toggleSection('basicInfo')">
        <i :class="sectionStates.basicInfo ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"></i>
        基本信息
      </div>
      <div class="dht-content" v-show="sectionStates.basicInfo">
        <div class="form-item">
          <label class="form-label">组件名称</label>
          <fx-input
            v-model="basicInfo.componentName"
            placeholder="请输入组件名称"
            size="small"
            @change="onBasicInfoChange"
          />
        </div>
        <div class="form-item">
          <fx-checkbox
            v-model="basicInfo.showName"
            @change="onBasicInfoChange"
          >
            是否显示名称
          </fx-checkbox>
        </div>
      </div>
    </div>

    <!-- 筛选设置 -->
    <div class="setting-section">
      <div class="dht-name dht-title" @click="toggleSection('filter')">
        <i :class="sectionStates.filter ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"></i>
        筛选
      </div>
      <div class="dht-content" v-show="sectionStates.filter">
        <div class="form-item">
          <label class="form-label">筛选字段</label>
          <field-selector
            v-model="filterInfo.fields"
            placeholder="请选择筛选字段"
            dialog-title="选择筛选字段"
            :selector-options="filterFieldsSelectorOptions"
            :max-display-count="5"
            @change="onFilterFieldsChange"
          />
        </div>

        <!-- 筛选布局 -->
        <div class="form-item">
          <label class="form-label">筛选布局</label>
            <div class="filter-layout-options dht-types">
              <div
                class="dht-cells"
                :class="{ 'dht-selected': filterInfo.filterLayout === '1' }"
                @click="onFilterLayoutChangeTab('1')"
              >
                <div class="dht-box">水平</div>
              </div>
              <div
                class="dht-cells"
                :class="{ 'dht-selected': filterInfo.filterLayout === '2' }"
                @click="onFilterLayoutChangeTab('2')"
              >
                <div class="dht-box">垂直</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 排序设置 -->
    <div class="setting-section">
      <div class="dht-name dht-title" @click="toggleSection('sort')">
        <i :class="sectionStates.sort ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"></i>
        排序
      </div>
      <div class="dht-content" v-show="sectionStates.sort">
        <div class="form-item">
          <label class="form-label">排序字段</label>
          <field-selector
            v-model="sortInfo.fields"
            placeholder="请选择排序字段"
            dialog-title="选择排序字段"
            :selector-options="sortFieldsSelectorOptions"
            :max-display-count="5"
            @change="onSortFieldsChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { dhtBizModel } from '@/components/web/utils/model';
import { SAVE_KEYS } from './const';
import FieldSelector from '@/components/common/fieldselect/index.vue';

export default {
  name: 'BaseSetting',
  inject: ['useInternalNode'],

  components: {
    FieldSelector
  },

  props: {
    allFields: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      // 折叠状态控制
      sectionStates: {
        basicInfo: true,
        filter: true,
        sort: true
      },

      // 基本信息设置
      basicInfo: {
        componentName: '',
        showName: true
      },

      // 筛选设置
      filterInfo: {
        fields: [],
        filterLayout: '1' // 默认单列
      },

      // 排序设置
      sortInfo: {
        fields: []
      }
    }
  },

  computed: {
    // 筛选字段选择器选项
    filterFieldsSelectorOptions() {
      const fieldData = [];
      Object.keys(this.allFields).forEach(key => {
        const field = this.allFields[key];
        if (['text', 'object_reference', 'picklist', 'date', 'datetime', 'number', 'currency'].includes(field.type)) {
          fieldData.push({
            id: field.api_name,
            name: field.label || field.label_r
          });
        }
      });

      return {
        tabs: [{
          id: 'fields',
          title: '筛选字段',
          data: fieldData
        }],
        enableThreeColumns: true,
        enableDragSort: true
      };
    },

    // 排序字段选择器选项
    sortFieldsSelectorOptions() {
      const fieldData = [];
      Object.keys(this.allFields).forEach(key => {
        const field = this.allFields[key];
        if (['text', 'number', 'currency', 'date', 'datetime'].includes(field.type)) {
          fieldData.push({
            id: field.api_name,
            name: field.label || field.label_r
          });
        }
      });
      return {
        tabs: [{
          id: 'fields',
          title: '排序字段',
          data: fieldData
        }],
        enableThreeColumns: true,
        enableDragSort: true
      };
    },
  },

  methods: {
    init(cardMainInfo) {
      this.initSettingData(cardMainInfo);
    },

    // 初始化设置数据
    initSettingData(cardMainInfo) {
      // 从card_main_info中获取基本信息
      if (cardMainInfo) {
        this.basicInfo.componentName = cardMainInfo.component_name || '';
        this.basicInfo.showName = cardMainInfo.show_name !== false;
      }

      // 从useInternalNode获取筛选和排序信息
      const { filter_fields, sort_fields, filter_layout } = this.useInternalNode(node => {
        return node.data;
      });

      if (filter_fields) {
        this.filterInfo.filterLayout = filter_layout || '1';
        // 初始化筛选字段
        if (filter_fields && filter_fields.length > 0) {
          this.filterInfo.fields = filter_fields.map(fieldId => {
            let name = fieldId
            const field = this.allFields[fieldId];
            if(field) {
              name = field.label || field.label_r
            }
            return { id: fieldId, name,  tabId: 'fields' }
          });       
        }
      }

      if (sort_fields) {
        // 初始化排序字段
        if (sort_fields && sort_fields.length > 0) {
          this.sortInfo.fields = sort_fields.map(fieldId => {
            let name = fieldId
            const field = this.allFields[fieldId];            
            if(field) {
              name = field.label || field.label_r
            }
            return { id: fieldId, name,  tabId: 'fields' }
          });
        }
      }


    },

    // 切换折叠状态
    toggleSection(sectionName) {
      this.sectionStates[sectionName] = !this.sectionStates[sectionName];
    },

    // 基本信息变更处理
    onBasicInfoChange() {
      this.updateBasicInfo();
    },

    // 筛选字段变更处理
    onFilterFieldsChange(selectedFields) {
      this.filterInfo.fields = selectedFields || [];
      this.updateFilterInfo();
    },

    // 筛选布局变更处理
    onFilterLayoutChangeTab(val) {
      this.filterInfo.filterLayout = val;
      this.updateFilterInfo();
    },

    // 排序字段变更处理
    onSortFieldsChange(selectedFields) {
      this.sortInfo.fields = selectedFields || [];
      this.updateSortInfo();
    },

    // 排序信息变更处理
    onSortInfoChange() {
      this.updateSortInfo();
    },

    // 更新基本信息
    updateBasicInfo() {
      const { actions } = this.useInternalNode();
      actions.setCustom((data) => {
        if (!data.card_main_info) {
          data.card_main_info = {};
        }
        data.card_main_info.component_name = this.basicInfo.componentName;
        data.card_main_info.show_name = this.basicInfo.showName;
      });
    },

    // 更新筛选信息
    updateFilterInfo() {
      const filter_fields = this.filterInfo.fields.map(item => item.id);
      const filter_layout = this.filterInfo.filterLayout;

      this.updateProps('filter_fields', filter_fields);
      this.updateProps('filter_layout', filter_layout);
    },

    // 更新排序信息
    updateSortInfo() {
      const sort_fields = this.sortInfo.fields.map(item => item.id);
      this.updateProps('sort_fields', sortInfo);
    },

    resetVal() {
      // 重置所有设置
      this.basicInfo = {
        componentName: '',
        showName: true
      };
      this.filterInfo = {
        fields: [],
        filterLayout: '1'
      };
      this.sortInfo = {
        fields: []
      };

      this.updateBasicInfo();
      this.updateFilterInfo();
      this.updateSortInfo();
    },

    updateProps(key, val) {
      const { actions } = this.useInternalNode();
      actions.setCustom((data) => {
        data[key] = val;
      });
    }
  }
}
</script>

<style lang="less" scoped>
.base-setting {
  width: 100%;

  .setting-section {
    margin-bottom: 0;
    border-bottom: 1px solid #E0E0E0;
    padding-bottom: 16px;
    margin-bottom: 16px;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }

    .dht-name {
      font-size: 16px;
      color: #181C25;
      cursor: pointer;
      user-select: none;
      display: flex;
      align-items: center;
      padding: 8px 0;

      &:hover {
        color: #409eff;
      }

      i {
        margin-right: 8px;
        font-size: 14px;
        transition: transform 0.2s ease;
      }
    }

    .dht-content {
      padding: 16px 0 0 0;

      .form-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .form-label {
          width: 100%;
          margin-right: 0;
          margin-bottom: 8px;
          font-size: 14px;
          color: #333;
          text-align: left;
          flex-shrink: 0;
        }

        .fx-input {
          flex: none;
          max-width: 100%;
        }

        .fx-select {
          flex: none;
          max-width: 100%;
        }

        .field-select-input-wrapper {
          flex: none;
          width: 100%;
        }

        .fx-checkbox {
          margin-left: 0;
        }
      }

      .filter-layout-options.dht-types {
        width: 100%;
        border-radius: 6px;
        background: var(--color-neutrals03, #F2F3F5);
        padding: 4px;
        display: flex;
        gap: 0;         
          
        .dht-cells {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          border-radius: 4px;
          transition: background 0.2s, color 0.2s;
          .dht-box {
            width: 100%;
            text-align: center;
            font-size: 13px;
            line-height: 18px;
            padding: 6px 0;
            border-radius: 4px;
            background: transparent;
            color: #3a3d48;
            transition: background 0.2s, color 0.2s, font-weight 0.2s;
          }
          &.dht-selected .dht-box {
            background: #fff;
            color: #181c25;
            font-weight: 500;
            box-shadow: none;
          }         
        }
      }
    }
  }
}

/* 字段选择器样式调整 */
.field-select-input-wrapper {
  .field-select-input {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 8px 12px;
    min-height: 32px;
    background-color: #fff;
    cursor: pointer;
    transition: border-color 0.2s ease;

    &:hover {
      border-color: #c0c4cc;
    }

    .input-display {
      .placeholder {
        color: #c0c4cc;
        font-size: 14px;
      }

      .selected-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        align-items: center;

        .fx-tag {
          margin: 0;
        }

        .more-count {
          color: #909399;
          font-size: 12px;
          cursor: pointer;
        }

        .add-btn {
          margin-left: 4px;
          padding: 0 4px;
          height: 20px;
          line-height: 18px;
        }
      }
    }
  }
}
</style>
