<script lang="ts">
import adapterModule from '../mixins/index.vue';

const baseDirName = 'crm-modules/page';
const Component: VueComponent = {
  name: 'PortalObjectList',

  mixins: [adapterModule],

  computed: {
    module() {
      return [this.component, this.subComponent]
        .filter((item) => !!item)
        .join('/');
    },
  },

  methods: {
    getObjectApiName() {
      if (/\/XV\/(Cross|ui)\/(portal|Portal)$/.test(window.location.pathname)) {
        const hash = window.location.hash;
        if (/#?\/portal\/depend\/dht-api-(.*?)$/.test(hash)) {
          const path = RegExp.$1;
          const array = path.split('-');
          const objectApiName = array[0];
          return objectApiName;
        }
        return '';
      }
      return this.$query('apiName');
    },
    specialRoute() {
      let apiName = this.getObjectApiName('apiName');
      const specialRoutes = window.CRM.control.specialRoutes || [];
      const index = apiName.indexOf('?');
      if (index !== -1) {
        apiName = apiName.substring(0, index);
      }
      if (apiName && specialRoutes.includes(apiName)) {
        return apiName.toLowerCase();
      }
      return '';
    },

    getComponent() {
      // 特殊路由， 参考crm control.route
      const specialRoute = this.specialRoute();
      if (specialRoute) {
        return `${baseDirName}/${specialRoute}/${specialRoute}.js`;
      }

      return `${baseDirName}/page.js`;
    },

    getComponentOptions() {
      return {
        use: true,
        dirname: this.source,
      };
    },
  },
};

export default Component;
</script>
