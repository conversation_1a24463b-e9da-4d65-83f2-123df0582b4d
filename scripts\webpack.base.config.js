/**
 * 公共配置
 */
// require('./verifyCommit');

const path = require('path');
const webpack = require('webpack');
const { VueLoaderPlugin } = require('vue-loader');
const RuntimePublicPathPlugin = require('webpack-runtime-public-path-plugin');
const { projectName } = require('../project.config.js');

function resolve(dir) {
  return path.join(__dirname, '..', dir);
}

module.exports = {
  mode: 'development',
  context: path.resolve(__dirname, '../'),
  output: {
    filename: '[name].js',
    chunkFilename: 'chunk-[name].js',
    libraryTarget: 'commonjs2',
    library: projectName,
  },
  module: {
    rules: [
      {
        test: /\.vue$/,
        loader: 'vue-loader',
        exclude: /node_modules/,
      },
      {
        test: /\.js$/,
        loader: 'babel-loader',
        exclude: /node_modules\/(?!(debug)\/).*/,
        options: {
          presets: ['@babel/preset-env'],
        },
      },
      {
        test: /\.tsx?$/,
        loader: 'ts-loader',
        exclude: /node_modules/,
        options: {
          appendTsSuffixTo: [/\.vue$/],
        },
      },
      {
        test: /\.(woff|eot|ttf)\??.*$/,
        loader: 'file-loader',
        options: {
          name: 'fonts/[name].[hash:8].[ext]',
        },
      },
    ],
  },
  resolve: {
    extensions: ['.js', '.vue', '.ts', '.tsx'],
    alias: {
      vue$: 'vue/dist/vue.esm.js',
      '@': resolve('src'),
    },
  },
  plugins: [
    // 优化网络层协议包的引入
    new webpack.NormalModuleReplacementPlugin(
      /(.*)\.HTTP_ENV(\.*)/,
      (resource) => {
        resource.request = resource.request.replace(/\.HTTP_ENV/, '.fs');
      },
    ),
    new RuntimePublicPathPlugin({
      runtimePublicPath: `Fx.ROOT_PATH + '/${projectName}-dist/'`,
    }),
    new webpack.DefinePlugin({
      'process.env.CONTAINER': JSON.stringify('fs'),
      seajsRequire: 'require',
    }),
    new VueLoaderPlugin(),
  ],
};
