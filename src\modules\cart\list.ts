import debug from 'debug';
import { LifeHooks } from '../../base/hooks';
import { getComponent } from '../../components';
import { CartActionMap } from './list-action';

const log = debug('@nsail:debugger');

/**
 * 扩展列表模块，实现购物车列表逻辑
 * @param List Backbone版本列表模块类
 */
export const ext = _.extend(LifeHooks(), {
  events: {
    'mouseenter .j-dht-tag-promotion': '_onPromotionTagEnter',
    'mouseleave .j-dht-tag-promotion': '_onPromotionTagLeave',
  },
  _onPromotionTagEnter(e: Event) {
    const $target = $(e.target);
    const productId = $target.data('pid');
    const coordinate = $target.offset();
    $dht.togglePromotionPopover(true, {
      productId,
      top: coordinate.top + 16, // 16是促销图标的宽度
      left: coordinate.left,
    });
  },
  _onPromotionTagLeave(e: Event) {
    $dht.togglePromotionPopover(false);
  },
  options: {
    // showPage: false,
    cellEdit: false,
    doStatic: true, // 走框架的数据请求，不走标准列表页
  },
  /**
   * 根据当前参数判断是获取购物车的service还是再次购买的service
   */
  getCartService() {
    return !this.copyOrderId
      ? $dht.services.cart
      : $dht.services.cartmock;
  },
  /**
   * 重新配置属性，这里主要是把`layoutButtons`去掉，页面不再显示“新建”按钮
   *
   * @see crm-modules/components/objecttable/objecttable
   * @param attrs
   * @override
   */
  beforeSetAttributes(attrs: Record<string, any>) {
    if (attrs.layoutButtons && attrs.layoutButtons.length > 0) {
      attrs.layoutButtons = attrs.layoutButtons.filter((item: any) => {
        return item.api_name !== 'Add_button_default';
      });
    }
    return attrs;
  },
  /**
   * 获取给表格使用的检索配置
   */
  getTerm() {
    const terms = this.$super().getTerm.apply(this);
    // HACK 不需要显示场景的情况下，要设置这个标志位，否则会多出一条竖线
    terms.pos = 'T';
    return terms;
  },
  /**
   * 获取给表格使用的配置选项
   */
  getOptions() {
    const options = this.$super().getOptions.apply(this);
    options.url = '/EM6HDHT/API/v1/object/ShoppingCartObj/controller/List';
    options.showTermBatch = false; // 不显示表格检索区：场景，筛选，列设置等
    if (options.filterColumns) {
      // HACK: 目前UIPaaS还不支持在场景里设置过滤字段，这里只保留产品作为过滤条件
      options.filterColumns = options.filterColumns.filter((item: ObjectDataMap) => {
        return item.api_name === 'product_id';
      });
    }
    options.sizeType = 'md'; // 屏蔽掉表格模式，因为输入框存在的原因，不能使用`sm`模式
    options.showSize = false; // 不显示分页栏的紧凑模式设置
    return this.parseTableOptions(options);
  },
  /**
   * 获取给表格使用的列配置
   */
  getColumns() {
    const columns = this.$super().getColumns.apply(this);
    // 在表头插入促销的图标列。原则上改列是可以通过`options.moreCustomColumns`来配置的，
    // 但是因为涉及到是否开启促销的判断，是否放在这里做动态处理
    if ($dht.config.promotion.isEnable) {
      columns.unshift({
        data: null,
        width: 48,
        // fixed: true,
        // fixedIndex: 2,
        title: $t('促销'),
        render(data: any, type: 'column', full: ObjectDataMap) {
          if (full.is_giveaway__v) {
            return '';
          }
          const promotions = $dht.services.promotion.getPromotionGroupBySkuId(full.product_id);
          if (promotions && promotions.length > 0) {
            return `<span class="dht-tag dht-tag-primary j-dht-tag-promotion" data-pid="${full.product_id}">${$t('促销')}</span>`;
          }
          return '';
        },
      });
    }

    columns.forEach((column: any) => {
      // 关闭所有列的排序
      column.isOrderBy = false;
      if (column.api_name === 'quantity') {
        // 只有非赠品可编辑数量，赠品不可编辑数量
        column.beforeEdit = (curRowData: ObjectDataMap) => {
          if (!curRowData) return false;
          return !curRowData.is_giveaway__v;
        };
      } else if (column.api_name === 'promotion_id') {
        // 促销字段不使用查找关联的交互，使用的是多选类型，多选值由后续计算
        column.type = 'select_one';
        column.dataType = 'select_one';
        column.render_type = 'select_one';
        // 这是在编辑`select_one`字段前唯一的一次修改选择项的机会了
        column.beforeEdit = (curRowData: ObjectDataMap, col: any) => {
          if (!curRowData) return false;
          if (curRowData.is_giveaway__v) return false;
          const matchedPrmotions = curRowData.promotion_matched;
          if (!matchedPrmotions || matchedPrmotions.length <= 0) {
            col.options = [];
            return false;
          }
          col.options = matchedPrmotions.map((matchedPromotion: any) => {
            return {
              name: matchedPromotion.promotion_rule_id__r,
              value: matchedPromotion.promotion_id,
            };
          });
          return true;
        };
      } else if (column.api_name === 'unit') {
        // 开启多单位时，单位选项从购物车产品信息上的多单位列表获取
        if ($dht.config.multiUnit.isEnable) {
          column.beforeEdit = (curRowData: ObjectDataMap, col: any) => {
            if (!curRowData) return false;
            const product = curRowData.product_id__o || {};
            const units = product._units || [];
            const isHasCommonUnit = units.some((unit:any) => unit.is_common);
            console.log('isHasCommonUnit', isHasCommonUnit);
            if (!product.is_multiple_unit || isHasCommonUnit || units.length <= 1) {
              col.options = [];
              return false;
            }
            col.options = product._units.map((unit: any) => {
              return {
                name: unit.unit_id__r,
                value: unit.unit_id,
              };
            });
            return true;
          };
        }
      } else if (column.api_name === 'picture') {
        // column.lastThTrueWidth = 50;
        // column.render = (data: any, type: 'column', rowData: ObjectDataMap, index: number) => {
        //   if (!rowData || !rowData.picture || rowData.picture.length <= 0) {
        // eslint-disable-next-line max-len
        //     return '<img class="dht-patch-dt-img" src="//a9.fspage.com/open/cdn/img/sail.default.png">';
        //   }
        //   const imageData: {
        //     ext: string;
        //     filename: string;
        //     path: string;
        //     size: string;
        //   } = rowData.picture[0];
        // eslint-disable-next-line max-len
        //   const imgSrc = `//img.${getDomain()}/image/o/${imageData.path}/32*0/webp/FSAID_11490c84`;
        //   return `<img class="dht-patch-dt-img" data-path="${imageData.path}" src="${imgSrc}">`;
        // };
      }
    });

    return columns;
  },
  /**
   * 格式化表格配置
   * @param opts
   * @param table
   */
  parseTableOptions(opts: any, table: any | null): any {
    return _.extend({}, opts, {
      // isMyObject: true,
      // autoHeight: true,
      // maxHeight: $(window).height() - 275,
      // showMultiple: true,
      columns: this.parseTableColumns(opts.columns, opts, table),
      // showMoreBtn: false,
      // showFilerBtn: false,
      // doStatic: true,
      // showPage: false,
      // openStart: true,
      // zIndex: this.get('zIndex'),
      // noCalculate: true,
      // isOrderBy_allColumn: true,
      // searchTerm: null,
      // showRequiredTip: true,
      // sizeType: 'md',
      // noDataStyle: 'small',
      // beforeEditFn: function (opts, next) {
      //   return me.beforeEditTableCellHandle(opts, table, next);
      // },
      // beforeEditFill: function (opts, next) {
      //   return me.beforeEditFillHandle(opts, table, next);
      // },
      // isPreObjBatchEdit: /Obj$/i.test(me.masterApiname) && /Obj$/i.test(opts.apiname)
    });
  },
  parseTableColumns(columns: any, opts: any, table: any) {
    return _.map(columns, (column: any) => {
      return this.parseTableColumnHook(column, table);
    });
  },
  parseTableColumnHook(column: any, table: any) {
    const editableColumns = [
      'quantity',
      'promotion_id',
      'remark',
    ];
    if ($dht.config.multiUnit.isEnable) {
      // 多单位开启时，单位是可以编辑的；但是其选项列表是要从多单位对象列表解析，不能使用描述的`options`
      editableColumns.push('unit');
    }
    column.isEdit = editableColumns.indexOf(column.api_name) >= 0 || /__c$/.test(column.api_name);
    column.isFilter = false;
    column.noSupportBatchEdit = true; // 不允许批量编辑
    return column;
  },
  /**
   * 解析列表数据
   * @param obj 后台返回的列表数据结构
   */
  parseData(res: any) {
    const obj: {
      totalCount: number;
      data: ObjectDataMap[];
    } = this.$super().parseData.call(this, res);

    if (obj.data && obj.data.length > 0) {
      // 解析促销数据并决定是否添加“换赠品”按钮
      obj.data.forEach((item) => {
        if (item.is_giveaway__v) return;
        item.operate = item.operate || [];
        const removeButton: CustomButton = {
          action: 'button_remove__c',
          action_type: 'custom',
          actions: [],
          api_name: 'button_remove__c',
          describe_api_name: 'ShoppingCartObj',
          label: $t('删除'),
          url: '',
        };
        item.operate.push(removeButton);
        // 如果适配到促销并且有多赠品，则显示切换赠品按钮
        if (item.promotion_id__p) {
          const { all_gifts = [], new_gifts = [] } = item.promotion_id__p;
          if (all_gifts.length > new_gifts.length) {
            // 全部赠品大于选择赠品时，说明是可以选择的
            const changeGiftButton: CustomButton = {
              action: 'button_changegift__c',
              action_type: 'custom',
              actions: [],
              api_name: 'button_changegift__c',
              describe_api_name: 'ShoppingCartObj',
              label: $t('换赠品'),
              url: '',
            };
            item.operate.push(changeGiftButton);
          }
        }
      });
    }
    return obj;
  },
  /**
   * 刷新表格，数据将从`@sail/core`缓存里读取
   */
  refreshTable(filters: any[]) {
    const list = this
      .getCartService()
      .getRenderableCartList(filters, { flattenGifts: true });
    this._list = list;
    this.parseData({
      dataList: list,
      buttonInfo: {
        buttons: [],
        buttonMap: {},
      },
    });
    this.doStaticData(list);
    const checkedCartIds = list.filter((item: ObjectDataMap) => {
      return item.is_settled;
    });
    this.table.setCheckedRow('_id', checkedCartIds);

    const giftCartIds = list.filter((item: ObjectDataMap) => {
      return item.is_giveaway__v;
    });
    this.table.setCheckboxDisabled('_id', giftCartIds);
  },
  /**
   * 自定义按钮点击处理
   *
   * @param opts 后台返回的button配置信息
   * @param data 当前行的数据
   * @override
   */
  _operateHandle(opts: CustomButton, data: ObjectDataMap) {
    log('CartItem action: %s', opts.action, data);
    const action = CartActionMap[opts.action] || FxUI.Utils.noop;
    action.call(this, data);
  },
  /**
   * 表格主体渲染完毕
   *
   * @see crm-modules/components/objecttable/objecttable.js
   */
  initComplete() {
    const $vue = this.$$vue;
    if ($vue.footer) {
      $vue.footer.$destroy();
      $vue.footer = null;
    }
    getComponent('CartFooter').then((Ctor: any) => {
      if (Ctor) {
        const wrapper = document.createElement('div');
        this.table.$el.append(wrapper);

        $vue.footer = new Ctor();
        $vue.footer.$mount(wrapper);
        $vue.footer.$on('order-create', (e: any) => {
          log('Event order-create: %o', e);

          const carts = this.getCheckedData();
          if (!carts || carts.length <= 0) {
            Fx.util.error($t('请至少选择一件商品结算'));
            return;
          }

          // let storeCarts = this.getCartService().store.getCartList();
          // storeCarts = storeCarts.filter((storeCart: any) => storeCart.is_settled);

          this.getCartService().batchMap2OrderProductList(carts).then((mdData: any[]) => {
            // 开启了cpq，需要给数据加上rowId，底层依赖
            if ($dht.config.bom.isEnable) {
              CRM.util.addRowId(mdData);
            }
            _.each(mdData, (mdItem: any) => {
              // 需要填充业务类型，才能显示到订单产品上
              mdItem.record_type = mdItem.record_type || 'default__c';
              // `is_giveaway`是单选值，这里用`__v`来取布尔值
              if (mdItem.is_giveaway__v) {
                mdItem._icon = { type: 'fill', label: '赠', title: '商品赠品' };
              }
              if ($dht.config.bom.isEnable) {
                mdItem.prod_pkg_key = mdItem.rowId;
              }
            });
            window.$dht.createOrder({
              apiname: 'SalesOrderObj',
              displayName: $t('销售订单'),
              source: 'cart',
              showDetail: true,
              mdData: { SalesOrderProductObj: mdData },
              success: (action: string, data: any, dataId: string, details: any[]) => {
                console.log(action, data, dataId, details);
                if (action !== 'add') return Promise.resolve();
                const ids = _.map(details, (cart: any) => cart.cartId);
                return this.getCartService()
                  .batchRemove(ids)
                  .then(() => {
                    this.refreshTable();
                  });
              },
            });
          });
        });
      }
    });

    const hash = window.location.hash.split('?');
    const params = window.FS.util.querystring.parse(hash[1]);
    if (params) {
      this.copyOrderId = params.orderId;
    }

    // 直接在这里获取数据了
    this.table.showLoading();
    const tableParams = this.table.getParam();
    const options: any = {
      needMultiUnit: true,
      needPromotion: true,
      needStock: true,
      search_query_info: {},
    };
    if (tableParams.SortField) {
      options.search_query_info.orders = [{
        fieldName: tableParams.SortField,
        isAsc: tableParams.SortType === 1,
      }];
    }
    if (tableParams.pageSizeOption) {
      options.pageSizeOption = tableParams.pageSizeOption;
    }
    const p = this.copyOrderId
      ? this.getCartService().getBuyAgainDetail(this.copyOrderId)
      : this.getCartService().getCartListAndDescribeLayout(options);
    p.then((list: any[]) => {
      log('Get car list: %o', list);
      this.refreshTable();
    }).catch((err: any) => {
      console.error(err);
    }).finally(() => {
      this.table.hideLoading();
    });
  },
  /**
   * 复选框点击回调
   */
  checkboxclickHandle(
    checked: boolean,
    $target: any,
    checkedLen: number,
    isSingleChecked: boolean,
    isAllCheckbox: boolean,
  ) {
    // const disableCheckboxLen = this.table.$('.main .checkbox-item-disabled').length;
    // const checkedList = this.table.getCheckedData() || [];

    const cellData = this.table.getRowData($target.closest('.tr'));
    const tableCurData = this.table.getCurData();
    const giftCartIds = isAllCheckbox
      ? tableCurData.filter((item: BaseModel) => item.is_giveaway__v)
      : tableCurData.filter((item: BaseModel) => item.belong_id === cellData._id);
    const updateCartIds = isAllCheckbox ? '*' : [cellData._id];

    let updateValue: boolean = checked;
    if (isAllCheckbox) {
      // TODO 在CRMTable里回调的时候，因为内部是不计算Disabled状态的赠品行数据，所以导致表格头的`checkbox`
      // 永远不会是返回全选的状态，也即参数`checked`是一个错误的值（因为赠品行数据被disable掉不会算做选中），
      // 所以这里打了个补丁，在这里通过CRMTable的选中数据列表和缓存数据列表的长度是否一致来判断是否是全选
      const checkedList = (this.table.getCheckedData() || []).filter((item: ObjectDataMap) => {
        return !item.is_giveaway__v;
      });
      const cachedList = this
        .getCartService()
        .getRenderableCartList(null, { flattenGifts: true })
        .filter((item: ObjectDataMap) => !item.is_giveaway__v);
      updateValue = checkedList.length === cachedList.length;
    }

    // 赠品数据需要自己处理
    if (updateValue) {
      this.table.setCheckedRow('_id', giftCartIds);
    } else {
      this.table.setUncheckedRow('_id', giftCartIds);
    }

    this.getCartService()
      .batchUpdateSettled(updateCartIds, updateValue)
      .then((res: any) => {
        // 这里不会对其他字段产生影响，所以不需要刷新表格了
        log('%o', res);
      });

    // if (isAllCheckbox) {
    //   // 赠品数据需要自己处理
    //   const giftCartIds = this.table.getCurData()
    //     .filter((item: BaseModel) => item.is_giveaway__v);
    //   if (checked) {
    //     this.table.setCheckedRow('_id', giftCartIds);
    //   } else {
    //     this.table.setUncheckedRow('_id', giftCartIds);
    //   }
    //   this.getCartService().batchUpdateSettled('*', checked).then((res: any) => {
    //     // 这里不会对其他字段产生影响，所以不需要刷新表格了
    //     log('%o', res);
    //   });
    //   return;
    // }

    // const cellData = this.table.getRowData($target.closest('.tr'));
    // const giftCartIds = this.table.getCurData().filter(
    //   (item: BaseModel) => item.belong_id === cellData._id,
    // );
    // if (checked) {
    //   this.table.setCheckedRow('_id', giftCartIds);
    // } else {
    //   this.table.setUncheckedRow('_id', giftCartIds);
    // }
    // this.getCartService()
    //   .batchUpdateSettled([cellData._id], checked)
    //   .then((res: any) => {
    //     // 这里不会对其他字段产生影响，所以不需要刷新表格了
    //     log('%o', res);
    //   });
  },
  /**
   * 处理购物车列表请求
   * @param params 请求参数
   */
  staticParamChangeHandle(params: any) {
    log(params);
    let searchQueryInfo = null;
    try {
      searchQueryInfo = JSON.parse(params.search_query_info);
    } catch (e) {
      console.warn(e);
    }
    if (searchQueryInfo) {
      this.refreshTable(searchQueryInfo.filters);
    }
  },
  /**
   * 单元格数据发生变化回调
   */
  // eslint-disable-next-line max-len
  cellChangeHandle(data: string, column: any, type: any, options: any, ignoreValidationRule?: boolean) {
    log(data, column, type, options);
    const cellData = options.cellData;
    let promise = null;
    if (column.api_name === 'promotion_id') {
      CRM.util.waiting($t('计算中'));
      promise = this.getCartService()
        .updatePromotionId(cellData._id, data)
        .then((res: any) => {
          if (res) {
            this.refreshTable();
          }
        });
    } else if (column.api_name === 'quantity') {
      CRM.util.waiting($t('计算中'));
      promise = this.getCartService()
        // eslint-disable-next-line max-len
        .updateQuantityAndUnit(cellData._id, { quantity: Number(data), unit: cellData.unit }, ignoreValidationRule)
        .then((res: any) => {
          if (res) {
            if ($dht.config.promotion.isEnable) {
              // 如果没有开启促销的话，其实是不需要刷新表格的，因为更新的数据不会对其他字段产生影响（除了金额）
              this.refreshTable();
            } else if ($dht.config.multiUnit.isEnable) {
              // 如果没有开启多单位的话，同样不需要刷新表格；反之，只需要更新当前行数据即可，不需要更新整个表；
              this.refreshTable();
            }
          }
        })
        .catch((err: any) => {
          if (err && err.isValidationRuleError) {
            const result = err.result || {};
            if (result.nonBlockMessages && result.nonBlockMessages.length > 0) {
              // TODO 暂时先不支持非阻断式校验规则，有实际需求再打开吧
              $dht.handleValidationNonBlockMessage(result.nonBlockMessages)
                .then(() => {
                  console.log('继续提交吧');
                  this.cellChangeHandle(data, column, type, options, true);
                })
                .catch((error: any) => {
                  console.log(`第${error}步取消了`);
                  // 非阻断式校验规则生效，且不再继续强制提交，要把值还原成原来的值
                  const rowSign = this.table.getRowSign(options.$tr);
                  this.table.setCellsValBySign(rowSign, options.oldData, column);
                });
            }
            if (result.blockMessages && result.blockMessages.length > 0) {
              $dht.handleValidationBlockMessage(result.blockMessages).then(() => {
                // 阻断式校验规则生效，要把值还原成原来的值
                const rowSign = this.table.getRowSign(options.$tr);
                this.table.setCellsValBySign(rowSign, options.oldData, column);
              });
            }
          }
        });
    } else if (column.api_name === 'unit') {
      CRM.util.waiting($t('计算中'));
      promise = this.getCartService()
        .updateQuantityAndUnit(cellData._id, { quantity: Number(cellData.quantity), unit: data })
        .then((res: any) => {
          if (res) {
            if ($dht.config.promotion.isEnable) {
              // 如果没有开启促销的话，其实是不需要刷新表格的，因为更新的数据不会对其他字段产生影响（除了金额）
              this.refreshTable();
            } else if ($dht.config.multiUnit.isEnable) {
              // 如果没有开启多单位的话，同样不需要刷新表格；反之，只需要更新当前行数据即可，不需要更新整个表；
              this.refreshTable();
            }
          }
        });
    } else if (column.api_name === 'remark') {
      CRM.util.waiting($t('计算中'));
      promise = this.getCartService()
        .updateFields(cellData._id, { remark: cellData.remark })
        .then((res: any) => {
          // 这里不会对其他字段产生影响，所以不需要刷新表格了
        });
    } else if (/__c$/.test(column.api_name)) {
      CRM.util.waiting($t('计算中'));
      const props: Record<string, any> = {};
      props[column.api_name] = cellData[column.api_name];
      promise = this.getCartService()
        .updateFields(cellData._id, props)
        .then((res: any) => {
          // 这里不会对其他字段产生影响，所以不需要刷新表格了
        });
    }
    if (promise) {
      promise.finally(() => {
        CRM.util.waiting(false);
      });
    }
  },
  // /**
  //  * 表格渲染完毕处理
  //  * @param init 是否是首次渲染完
  //  */
  // renderListCompleteHandle(init: boolean) {
  //   // if (init) {
  //   //   this.table.$el.append('<div class="dht-patch-dt-page">我是底部</div>');
  //   // }
  //   this.$super().renderListCompleteHandle.call(this, init);
  // },
  /**
   * 查看详情
   * @param rowData 当前行数据
   * @param idList 当前表格的ID们
   */
  showDetail(rowData: any, idList: any[]) {
    log('%o', rowData);
  },
});
