// @vue/component
const reg = /^(-?\d+)(\.\d*)?$/;

export default {
  name: 'FkInputNumber',

  components: {},

  mixins: [],

  props: {
    max: {
      type: Number,
      default: Infinity,
    },
    min: {
      type: Number,
      default: -Infinity,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    step: {
      type: Number,
      default: 1,
    },
    value: {
      type: [Number, String],
      default: '',
    },
    precision: {
      type: [Number, null],
      default: null,
      validator: (val: any) => val == null || val >= 0,
    },
  },

  data() {
    return {
      innerValue: '',
      isFocus: false,
    };
  },

  computed: {
    upDisabled(): boolean {
      const me = this as any;
      return +me.innerValue >= me.max;
    },

    downDisabled() {
      const me = this as any;
      return +me.innerValue <= me.min;
    },
  },

  watch: {
    value: {
      handler(newValue: number | string) {
        const me = this as any;
        me.innerValue = `${newValue}`;
      },
      immediate: true,
    },
    innerValue(newValue: string) {
      const me = this as any;
      let testValue = newValue;
      if (reg.test(testValue)) {
        let isChange = false;
        if (typeof me.precision === 'number') {
          const arr = testValue.split('.');
          if (arr.length === 2) {
            if (me.precision === 0) {
              // 不允许有小数
              testValue = arr[0];
              isChange = true;
            } else if (arr[1].length > me.precision) {
              arr[1] = arr[1].substring(0, me.precision);
              isChange = true;
            }
            testValue = arr.join('.');
          }
        }

        if (isChange) {
          me.innerValue = testValue;
        } else if (testValue.charAt(testValue.length - 1) !== '.') {
          const fixValue = me.fixedValue(testValue);
          if (+testValue === fixValue) {
            me.$emit('input', fixValue);
          } else {
            me.innerValue = `${fixValue}`;
          }
        }
      } else if (testValue === '') {
        me.$emit('input', testValue);
      } else if (testValue !== '-') {
        me.innerValue = `${me.value}`;
      } else {
        // 什么也不做
      }
    },
  },

  created() {},

  methods: {
    onfocus($event: MouseEvent) {
      const me = this as any;
      me.isFocus = true;
      me.$emit('focus', $event);
    },

    onblur($event: MouseEvent) {
      const me = this as any;
      me.isFocus = false;
      me.$emit('blur', $event);
    },

    focus() {
      const me = this as any;
      if (me.$refs.fkInputNumber) {
        me.$refs.fkInputNumber.focus();
      }
    },

    blur() {
      const me = this as any;
      if (me.$refs.fkInputNumber) {
        me.$refs.fkInputNumber.blur();
      }
    },

    onEnter($event: MouseEvent) {
      const me = this as any;
      me.$emit('enter', $event);
    },

    upClick() {
      const me = this as any;
      if (me.upDisabled) return;
      const val = +me.innerValue + me.step;
      me.innerValue = `${val}`;
      me.$refs.fkInputNumber.focus();
    },

    downClick() {
      const me = this as any;
      if (me.downDisabled) return;
      const val = +me.innerValue - me.step;
      me.innerValue = `${val}`;
      me.$refs.fkInputNumber.focus();
    },

    fixedValue(innerValue: string) {
      const me = this as any;
      let val = +innerValue;
      if (val > me.max) {
        val = me.max;
      } else if (val < me.min) {
        val = me.min;
      }
      return val;
    },
  },
};
