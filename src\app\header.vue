<template>
  <div class="portal-header">
    <div class="portal-header-main">
      <component :is="content"></component>
    </div>
    <!-- <div class="portal-header-user">
      <img class="user-avatar" :src="userAvatar" @error="updateAvatar" />
      <fx-dropdown class="user-switch-tpls"
        trigger="click"
        :hide-on-click="true"
        @command="switchTpls"
      >
        <i class="el-icon-caret-bottom switch-tpls-icon"></i>
        <fx-dropdown-menu slot="dropdown">
          <fx-dropdown-item v-for="tpl in portalTpls" :command="tpl.templeId" :key="tpl.templeId">
            {{ tpl.templateName }}
          </fx-dropdown-item>
        </fx-dropdown-menu>
      </fx-dropdown>
    </div> -->
  </div>
</template>

<script lang="ts">
const Component: VueComponent = {
  data() {
    return {
      content: null,
      enterpriseName: '',
      userAvatar: '',
    };
  },
  computed: {
    portalTpls() {
      if (!window.$dht || !window.$dht.envProvider) {
        return [];
      }
      const portalEnv = window.$dht.envProvider;
      return portalEnv.get('portalTpls');
    },
  },
  created() {
    const currentUser = Fx.contacts.getCurrentEmployee();
    this.enterpriseName = currentUser.workCircleEnterpriseName;
    this.userAvatar = `${currentUser.profileImage}&ea=${currentUser.enterpriseAccount}`;
    this.updateContent();
    // window.addEventListener('router.change', (event) => {
    //   this.updateContent((event as any).detail);
    // });
  },

  methods: {
    getContentByEnv(): Promise<any> {
      return import(
        /* webpackChunkName: "common-header" */
        /* webpackMode: "lazy" */
        '../components/header/header.vue'
      );
    },

    updateAvatar(e: Event) {
      if (e.target) {
        (e.target as any).src = 'data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==';
      }
    },

    async updateContent() {
      const c = await this.getContentByEnv();
      (this as VueComponent).content = c.default;
    },

    switchTpls(templeId: string) {
      const portalEnv = window.$dht.envProvider;
      const event = new CustomEvent('tpls.change', {
        detail: {
          tplId: templeId,
        },
      });
      portalEnv!.set('activedTpl', templeId);
      window.dispatchEvent(event);
    },
  },

};

export default Component;
</script>

<style lang="less" scoped>
.portal-header {
  position: fixed;
  display: flex;
  top: 0;
  right: 0;
  left: 64px;
  line-height: 56px;
  padding: 0 24px;
  justify-content: space-between;
  height: 56px;
  z-index: 2;
  background: #fff;
  box-shadow: 0 2px 4px rgba(33, 43, 54, 0.05);
}

.portal-header-main {
  width: 100%;
}

.portal-header-user {
  position: relative;
  width: 32px;
  padding-right: 24px;

  .user-avatar {
    width: 32px;
    height: 32px;
    margin-top: 12px;
    border-radius: 16px;
    background: #cccccc;
  }
  .user-switch-tpls {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
  }
}
</style>
