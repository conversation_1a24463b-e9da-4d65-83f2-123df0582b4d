interface StockInterface {
  accurateNum: number;
  fuzzyDescription: string;
  id: string;
}

interface InventoryConfig {
  isEnable: boolean,
  visibleType: string, // 库存显示方式：1-不显示；2-精确显示；3-模糊显示
  orderCheck: string, // 提交订单时是否校验，目前前端应该没用到
  calculateType: string, // 计算库存方式，1-单一仓库；2-合并仓库
}

export default {
  computed: {
    isShowStock() {
      const { isEnable, visibleType } = window.$dht.config.inventory as InventoryConfig;
      return isEnable && visibleType !== '1';
      // return true;
    },
    isFuzzyStock() {
      return (window.$dht.config.inventory as InventoryConfig).visibleType === '3';
      // return true;
    },
  },

  methods: {
    getStockText(stock: StockInterface): string | number {
      const visibleType = window.$dht.config.inventory.visibleType;
      // const visibleType = '3';
      switch (visibleType) {
        case '2':
          return stock.accurateNum;
        case '3':
          return stock.fuzzyDescription;
        default:
          return '';
      }
    },
  },
};
