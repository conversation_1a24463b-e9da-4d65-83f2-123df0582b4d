import { floatAdd, floatMul } from '@gamma/common';
import { LifeHooks } from '../../../../../base/hooks';
import { getComponent } from '../../../../../components';
import { dimaiMD } from './dimai_md';

const upstreamEa = $.cookie('ERUpstreamEa');
const notSetFieldsReadonly = ['78437', 'xlhnyzb'].includes(upstreamEa); // 客开逻辑：这些企业不设置特殊字段只读

export function mdExtend(MD: BackboneComponent): BackboneComponent {
  const proto = MD.prototype;

  /* eslint no-param-reassign: 0 */
  MD = MD.extend({
    // ****************** 覆写md方法 *****************
    modelEvents: _.extend({}, proto.modelEvents, {
      'change:match_promotion': '_matchPromotionHandle',
    }),

    initialize() {
      proto.initialize.apply(this, arguments);
      // 保存当前表格实例
      this.cartTable = null;
    },

    renderAfter() {
      proto.renderAfter && proto.renderAfter.apply(this, arguments);
      this.initFooter();
    },

    renderTableComplete(table:any) {
      proto.renderTableComplete.apply(this, arguments);
      this.cartTable = table;
      this.setFooterData(table);
    },

    // 渲染表格右上角操作按钮
    renderRightHandleBtn(table: any) {
      if (this.isCart()) {
        table.__btnList = []; // 购物车模式不需要选产品操作等
      }
      table.__btnList = _.filter(table.__btnList, (item: any) => {
        return item.action !== 'addOneDataHandle';
      });

      proto.renderRightHandleBtn.apply(this, arguments);
    },

    // 表格操作列的按钮
    // getHandColunmBtns(opts:any, table:any) {
    //   const buttons = proto.getHandColunmBtns.apply(this, arguments);
    //   return buttons;
    // },

    // 改变表格数据会触发该方法
    updateTableByData(data:any, table:any) {
      proto.updateTableByData.apply(this, arguments);
      if (_.isEmpty(data)) return;
      this.setFooterData(table);
    },

    // 在表格上进行删除添加操作时调计算接口的回调函数
    afterDatasToTableCal(res:any, table:any, isAdd:any) {
      proto.afterDatasToTableCal.apply(this, arguments);
      !isAdd && this.setFooterData(table); // 删除时，需要主动调用这个方法
    },

    /**
     * @override
     * 价格政策变化后赠品可能变化，需要改变赠品选择状态
     */
    afterPolicyProcess() {
      proto.afterPolicyProcess.apply(this, arguments);
      if (this.cartTable) {
        this.setFooterData(this.cartTable);
      }
    },

    /**
     * 单元格编辑之前，常用单位不允许编辑
     * @param opts
     * @param table
     * @param next
     */
    beforeEditTableCellHandle(opts: any, table: any, next: any) {
      const apiName = opts.column.api_name;
      const isCommonUnit = opts.data.is_common_unit;
      if ((apiName === 'actual_unit' || apiName === 'other_unit') && isCommonUnit) {
        next(false);
      } else {
        proto.beforeEditTableCellHandle(opts, table, next);
      }
    },

    /**
     * @desc 增加字段解析的钩子
     */
    parseTableColumns(columns:any, opts:any, table:any) {
      const myColumns = proto.parseTableColumns.apply(this, arguments);

      // 不可编辑字段：价格、销售价格、折扣、小计、是否赠品、价目表id、价目表明细id, 产品id
      const disableFields: Record<string, boolean> = {
        product_price: true,
        sales_price: true,
        discount: true,
        subtotal: true,
        is_giveaway: true,
        price_book_id: true,
        price_book_product_id: true,
        product_id: true,
      };
      // Currency字段需要添加币种符号
      const currencyFields: Record<string, boolean> = {
        product_price: true,
        sales_price: true,
        subtotal: true,
        price_book_price: true,
      };
      _.each(myColumns, (column: any) => {
        if (disableFields[column.api_name] && !notSetFieldsReadonly) {
          column.isEdit = false;
        }
        if (currencyFields[column.api_name]) {
          column.render = (data: any) => {
            return $dht.config.currency.currencyFlag + data;
          };
        }
      });
      return myColumns;
    },

    getExtraPickParam(field: any, table: any) {
      const data = proto.getExtraPickParam.apply(this, arguments);
      const objectData: any = {
        object_describe_api_name: 'AvailableRangeObj',
      };
      // 如果有设置可售范围参数，则放入入参
      const availableRangeFilterData = $dht.getMainFilterData();
      if (!_.isEmpty(availableRangeFilterData)) {
        objectData[availableRangeFilterData.apiname] = availableRangeFilterData.value;
      }
      objectData.record_type = this.getData('record_type'); // 表单模式下单用当前选的业务类型，不用其他地方设置的业务类型
      const extraData = {
        categoryRequestExtra: {
          object_data: objectData,
        },
      };
      _.extend(data, extraData);
      return data;
    },

    // ======================================= 私有方法 ==============================================
    isCart() { // 是否是购物车模式
      return this.get('source') === 'cart';
    },

    initFooter() {
      if ($dht.config.sail.isHidePrice) return;

      const $vue = this.$$vue;
      if ($vue.footer) {
        _.map($vue.footer, (comp: any) => {
          comp.$destroy();
        });
        $vue.footer = null;
      }
      getComponent('AddSummary').then((Ctor: any) => {
        $vue.footer = {};
        if (Ctor) {
          const wrapper = document.createElement('div');
          const $targets = this.$childs.find('.md-layout'); // 有n个业务类型 ( n >= 0 )

          _.each($targets, (target: any) => {
            target.append(wrapper);
            const recordtype = $(target).data('recordtype');

            $vue.footer[recordtype] = new Ctor();
            $vue.footer[recordtype].$mount(wrapper);
          });
        }
      });
    },

    setFooterData(table:any) {
      const data = this.get('data');
      const allList = this.getTableCurData(table);
      const $footer = this.$$vue.footer && this.$$vue.footer[table.recordtype];

      if (!$footer) return;

      let quantity = 0;
      let kind = 0;
      const exist: Record<string, boolean> = {};
      _.each(allList, (item:any) => {
        quantity = floatAdd(quantity, item.quantity);
        if (!exist[item.product_id]) {
          exist[item.product_id] = true;
          kind++;
        }
      });

      $footer.updateSummary({
        isMultiRecordType: _.size(this.$$vue.footer) > 1,
        kind, // 种类
        quantity, // 数量
        orderAmount: data.order_amount, // 订单实付金额
        productAmount: data.product_amount,
        policyDynamicAmount: data.policy_dynamic_amount || 0,
      });
    },
    _matchPromotionHandle(e: any, options: any) {
      const $footer = this.$$vue.footer;
      const data = options.data;
      if (!data) return;
      _.map($footer, (comp: any) => {
        comp.orderAmount = floatMul(data.product_amount, data.discount / 100).toFixed(2);
      });
    },
  });

  // 帝迈客开
  if (upstreamEa === '733767_sandbox' || upstreamEa === 'dymind') {
    MD = MD.extend(dimaiMD(proto));
  }

  MD = MD.extend(LifeHooks());

  return MD;
}
