<template>
  <div class="dht-carts-entrance" id="dht-carts-entrance">
    <div class="carts-btnwrap">
      <span class="carts-entrance-icon"><i class="el-icon-shopping-cart-1"></i></span>
      <span class="carts-entrance-name">{{ $t('购物车') }}</span>
      <span v-if="cartCount > 0" class="carts-entrance-badge">{{ cartCount }}</span>
    </div>
    <cart-preview ref="cartPreview" class="carts-wrapper" @updatecartlist="handleUpdateCart" />
  </div>
</template>

<script lang="ts">
import CartPreview from './cart-preview.vue';

export default {
  name: 'DhtCartEntrance',
  components: {
    CartPreview,
  },
  data() {
    return {
      cartCount: 0,
    };
  },
  methods: {
    handleUpdateCart(list: any[]) {
      this.cartCount = list.length;
    },
  },
} as VueComponent;
</script>

<style scoped>
.dht-carts-entrance {
  position: relative;
  font-size: 12px;
  color: #3B415C;
}

.dht-carts-entrance .carts-btnwrap {
  z-index: 13;
  position: relative;
  height: 30px;
  line-height: 30px;
  padding: 0 15px;
  border-radius: 4px;
  border: 1px solid #F0F4FC;
  background-color: #F0F4FC;
}

.dht-carts-entrance .carts-wrapper {
  z-index: 12;
  top: 31px;
  right: 0;
  display: none;
}

.dht-carts-entrance:hover .carts-btnwrap {
  border: 1px solid #ff8000;
  border-bottom: 1px solid #fff;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background-color: #fff;
  color: #ff8000;
}

.dht-carts-entrance:hover .carts-wrapper {
  display: block;
}

.carts-entrance-icon {
  margin-right: 2px;
  font-size: 15px;
}

.carts-entrance-badge {
  position: absolute;
  top: -4px;
  right: 0;
  width: 20px;
  height: 16px;
  line-height: 16px;
  transform: translateX(50%);
  border-radius: 3px;
  background-color: #f43;
  box-shadow: 0px 2px 4px 0px rgba(255, 68, 51, 0.5);
  font-size: 10px;
  text-align: center;
  color: #fff;
}

</style>
