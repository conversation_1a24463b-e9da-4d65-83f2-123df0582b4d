<template>
  <div class="dht-promotion-snippet">
    <div class="promotion-snippet">
      <!-- 订单促销 -->
      <template v-if="promotionDetail.record_type === 'default__c'">
        <div class="promotion-snippet-item">
          <div class="promotion-type">
            <fx-tag size="mini">{{ promotionDetail.type__r }}</fx-tag>
          </div>
          <div class="promotion-rule">{{ promotionDetail.name }}</div>
        </div>
        <div class="promotion-snippet-info">
          <div class="promotion-info-hd">{{ $t('i18n.promotion.type') }}</div>
          <div class="promotion-info-bd">{{ $t('i18n.promotion.order') }}</div>
        </div>
        <div class="promotion-snippet-info">
          <div class="promotion-info-hd">{{ $t('i18n.promotion.rules') }}</div>
          <div class="promotion-info-bd">
            <div v-for="(item, idx) in promotionRules" :key="idx" class="promotion-rule-item">
              <span>{{ item.rule_description }}</span>
              <span v-if="item.gift_method == '2'"
                class="view-btn"
                @click="handleVieGifts(item.id)"
              >&nbsp;{{ $t('i18n.promotion.view_gift') }}</span>
            </div>
          </div>
        </div>
        <div class="promotion-snippet-info">
          <div class="promotion-info-hd">{{ $t('i18n.promotion.time') }}</div>
          <div class="promotion-info-bd">
            {{ formatTime(promotionDetail.start_time) }} ~
            {{ formatTime(promotionDetail.end_time) }}
          </div>
        </div>
        <div class="promotion-snippet-info">
          <div class="promotion-info-hd">{{ $t('i18n.promotion.description') }}</div>
          <div class="promotion-info-bd">{{ promotionDetail.description || '--' }}</div>
        </div>
      </template>
      <!-- 商品促销或组合促销 -->
      <div v-else class="promotion-snippet-item">
        <!-- eslint-disable-next-line max-len -->
        <div class="promotion-type" :class="{'promotion-type-combine': promotionDetail.record_type === 'combine_promotion__c'}">
          <fx-tag size="mini">{{ promotionDetail.type__r }}</fx-tag>
        </div>
        <div v-if="promotionDetail.rule_method === '2' && isPromotionDetailModule"
          class="promotion-rule"
        >
          {{ $t('i18n.promotion.view_rules') }}
        </div>
        <div v-else class="promotion-rule">
          <div v-for="(item, idx) in promotionRules" :key="idx" class="promotion-rule-item">
            <span class="promotion-rule-text" :data-type="item.record_type">
              {{ item.rule_description }}
            </span>
            <!--
            <span v-if="item.gift_method == '2'" class="view-btn" @click="handleVieGifts(item._id)">
              查看赠品
            </span>
            -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'DhtPromotionSnippet',
  props: {
    promotionDetail: {
      type: Object,
      default: () => {},
    },
    promotionRules: {
      type: Array,
      default: () => [],
    },
    promotionGifts: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    /**
     * 是否在促销详情页展示
     */
    isPromotionDetailModule(): boolean {
      // return /^promotion-(product|order|combine)$/.test(this.$route.name);
      return false;
    },
  },
  methods: {
    formatTime(value: number) {
      return CRM.util.moment(value).format('YYYY-MM-DD HH:mm');
    },
    handleVieGifts(promotionRuleId: string) {
      this.$emit('view-gifts', promotionRuleId, this.promotionDetail._id);
    },
  },
} as VueComponent;
</script>

<style lang="css" scoped>
.promotion-snippet {
  padding: 10px;
  background: #fff;
}

.promotion-snippet .view-btn {
  color: #3487e2;
  cursor: pointer;
}

.promotion-rule .promotion-rule-item {
  line-height: 18px;
  margin-bottom: 6px;
}
.promotion-rule .promotion-rule-item:last-child {
  margin-bottom: auto;
}

.promotion-snippet-item,
.promotion-snippet-info {
  display: flex;
  margin-bottom: 6px;
}

.promotion-snippet-item:last-child {
  margin-bottom: auto;
}

.promotion-snippet-item .promotion-type {
  width: 44px;
}

.promotion-snippet-item .promotion-type-combine {
  width: 100px;
}

.promotion-snippet-item .promotion-rule {
  max-width: 80%;
  margin-left: 10px;
}
.promotion-snippet-item .promotion-rule-item {
  color: #666;
}

.promotion-snippet-info .promotion-info-hd {
  color: #999;
}
.promotion-snippet-info .promotion-info-bd {
  width: 80%;
  margin-left: 10px;
}
</style>
