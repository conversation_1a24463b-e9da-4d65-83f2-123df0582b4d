// 获取币种映射
export function getCurrencyMapList() {
  return $dht.getService('meta').getCurrencyMapList();
}

function multiCurrencyStatus() {
  return new Promise((resolve:any) => {
    CRM.util.FHHApi({
      url: '/EM1ANCRM/API/v1/object/currency/service/multi_currency_status',
      data: {},
      success(res: any) {
        if (res.Result.StatusCode === 0) {
          resolve(res.Value.status);
        }
      },
      error() {
        resolve(0);
      },
    });
  });
}

// 获取多币种
export function getCurrency() {
  // TODO: 互联 getUserInfo 币种取消缺省值后，此客开可删除
  const isMnchip = ['mnchip2019', '83050'].includes($.cookie('ERUpstreamEa'));
  if (isMnchip) {
    $dht.config.currency.currencyCode = '';
    $dht.config.currency.currencyFlag = '';
    return Promise.resolve('');
  }

  return new Promise((resolve, reject) => {
    Fx.async(['app-apperlogin/app.js'], (mod: any) => {
      const p1 = mod.getUserInfo();
      const p2 = getCurrencyMapList();
      const p3 = multiCurrencyStatus();
      Promise.all([p1, p2, p3]).then((result: Array<any>) => {
        const currencyCode = result[0].data.currencyCode;
        const currencyMap = result[1];
        const isOpenMultiCurrency = result[2] === 1;
        $dht.config.currency.currencyCode = isOpenMultiCurrency ? currencyCode : '';
        $dht.config.currency.currencyFlag = isOpenMultiCurrency ? currencyMap[currencyCode] : '';
        resolve('');
      }).catch(() => {
        reject();
      });
    });
  });
}
