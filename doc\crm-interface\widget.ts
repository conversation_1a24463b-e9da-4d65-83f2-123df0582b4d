/**
 * 基础组件传入的参数
 */
interface BaseWidgetOptions {
  // multiple：多选，single: 单选
  multiple?: 'single' | 'multiple';
  // 是否禁用
  disabled?: boolean;
  // 1 | 2 | 3 (小中大)
  size?: '1' | '2' | '3',
  // 层级
  zIndex?: number;
  // 其他
  [key: string]: any;
}

/**
 * CRM所有widget的基类, 所有设计view层的组件都应由此派生
 */
export interface Widget {
  // 组件options
  options: BaseWidgetOptions;

  /**
   * 初始化回调
   * @param option {BaseWidgetOptions}
   */
  initialize (option: BaseWidgetOptions): void;

  /**
   * 每个组件外部调用的初始化函数
   */
  setup (): Widget;

  /**
   * 渲染函数
   */
  render (): Widget;

  /**
   * 获取组件option中对应的key值
   * @param key {string}: 支持.分割的字符串，key为data.name时 取值为 me.options[data][name]
   */
  get (key: string): any;

  /**
   * 设置组件options的值
   * @param key {string}: 支持.分割的字符串，key为data.name时 设置me.options[data][name]
   * @param value {any}: 值
   */
  set (key: string, value: any): void;

  /**
   * 显示组件
   */
  show (): Widget;

  /**
   * 隐藏组件
   */
  hide (): Widget;

  /**
   * 销毁组件，这会移除所有绑定的事件和dom
   */
  destroy () : void;
}
