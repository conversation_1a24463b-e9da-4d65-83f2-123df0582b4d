<template>
  <div class="dht-product-detail-key-info-setting" >
      <div class="dht-name dht-title"
        @click="toggleShow"
      >
        <!-- 向下剪头 -->
        <i :class="show ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"></i>

        {{ $t('dht.component.web.product_detail_key_info.title', '参数信息') }}
      </div>  

      <div class="dht-content" v-show="show">
        <!-- 组件名称 
        <div class="dht-section">
            <div class="dht-title">
              {{ $t('dht.component.web.product_detail_key_info.name', '组件名称') }}
          </div>   
          <fx-input v-model="name" @change="nameChange"/>                 
        </div>-->

        <!-- 展示样式 -->
        <div class="dht-section">
            <div class="dht-title">
              {{ $t('dht.component.web.product_detail_key_info.show_type', '展示样式') }}
            </div>
            <div class="dht-types">
                <div v-for="(item, index) in showTypes" :key="item.key" @click.stop="showTypeChange(item)"  class="dht-cells" :class="showType == item.val ? 'dht-selected' : ''">
                    <div class="dht-box">
                        <i class="dht-icon" :class="item.icon"></i>
                    </div>
                    <div class="dht-label">{{ item.name }}</div>                    
                </div>
            </div>                     
        </div>

        <!-- 显示字段 -->
        <div class="dht-section" v-if="fieldsOptions.length > 0">
            <div class="dht-title">
              {{ $t('dht.component.web.product_detail_key_info.fields', '显示字段') }}
            </div>
            <field-selector
              v-model="selectedFieldsFormatted"
              :dialog-title="$t('dht.component.web.product_detail_key_info.fields', '显示字段')"
              :selector-options="fieldsSelectorOptions"
              :max-display-count="5"
              @change="fieldsChange"
            />
        </div>

      <!-- end of  dht-content -->
      </div>
  </div>
</template>
<script>
/* 多语如下 */

import { dhtBizModel } from '../../../utils/model';
import FieldSelector from '@/components/common/fieldselect/index.vue';

// 排除可选的字段apiName 作废前生命状态 生命状态
const blackFields = ['life_status_before_invalid', 'life_status', '_id'];

export default {
  inject: ['useInternalNode'],
  components: {        
    FieldSelector
  },
  data() {
      return {   
        show: true,
        name: '',
        showType: '1',
        selectedFields: [], // 存储字段的 value 数组
        fieldsOptions: [],  // [{ value, key, label }]
      };
  },
  computed: {
    showTypes() {
        return [
            /* 单列 */
            {key: 'single', val: '1', name: $t('dht.component.web.product_detail_key_info.single', '单列'), icon: 'fx-icon-tuliweizhi1'},
            /* 双列 */
            {key: 'double', val: '2', name: $t('dht.component.web.product_detail_key_info.double', '双列'), icon: 'fx-icon-list-4'},                
        ]
    },    
    // 适配 field-selector 组件的数据格式
    fieldsSelectorOptions() {
      return {
        tabs: [{
          id: 'fields',
          title: $t('dht.component.web.product_detail_key_info.fields', '显示字段'),
          data: this.fieldsOptions.map(item => ({
            id: item.value,
            name: item.label
          }))
        }]
      };
    },
    // field-selector v-model 适配器
    selectedFieldsFormatted: {
      get() {
        // 返回 [{ id, name, tabId }] 格式
        return this.selectedFields.map(fieldId => {
          const found = this.fieldsOptions.find(f => f.value === fieldId);
          let name = fieldId;
          if(found) {
            name = found.label;
          }
          return { id: fieldId, name, tabId: 'fields' };
        });
      },
      set(val) {
        // 只存储 id
        this.selectedFields = Array.isArray(val) ? val.map(item => item.id) : [];
      }
    }
  },
  methods: {  
    async init() {
      const { name, showType, selectedFields } = this.useInternalNode(node => {
            return node.data;
      });
      this.name = name;
      this.showType = showType;
      this.selectedFields = selectedFields;
      // this.objectContext = objectContext;
      await this.formatFields();
    },  
    toggleShow() {
      this.show = !this.show;
    },
    async formatFields() {
      let result = [];
      const objFields = await dhtBizModel.fetchObjFields(dhtBizModel.mainObjApiName());
      // console.log('objFields:', objFields);
      let item = {}
      for (let key in objFields) {        
        if (objFields.hasOwnProperty(key)) {
          item = objFields[key];
          if(item.define_type !== 'system' &&  //系统字段
            blackFields.indexOf(key) < 0 &&    // 黑名单字段
            (['object_reference', 'text', 'currency', 'number', 'date', 'datetime', 'email', 'phone', 'count'].indexOf(item.type) >= 0) ) { // 文本类型字段
              result.push({
                value: key,
                key: key,
                label: item.label_r || item.label
              })
            }          
        }
      }
      // console.log('fieldsOptions', result);
      this.fieldsOptions = result;
      return result;
    },
    nameChange(val) {
      this.updateProps('name', val);
    },
    showTypeChange(item) {
      let showType = item.val;
      this.updateProps('showType', showType);
    },  
    fieldsChange(val) {
      // val 为 [{ id, name, tabId }]
      this.selectedFields = Array.isArray(val) ? val.map(item => item.id) : [];
      this.updateProps('selectedFields', this.selectedFields);
    },     
    updateProps(key, val) {
      const { actions } = this.useInternalNode();

      actions.setCustom((data) => {
        data[key] = val;
      });
      this.$set(this, key, val);
      // this.data[key] = val;
      // const api_name = this.$attrs.api_name;
      // this.setProps(api_name, (props) => {
      //     props[key] = val;
      // });
    },          
  },
  created() {
      this.init();
  },
  mounted () {
  },
};
</script>
<style lang="less">
.dht-product-detail-key-info-setting {
  display: flex;
  flex-direction: column;
  width: 100%;   

  
  .dht-types {
        display: flex;
        justify-content: center;
        width: 100%;

        .dht-cells {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 50%;
            cursor: pointer;

            &.dht-selected{
                color: var(--color-primary06, #FF8000);
                .dht-box {
                    border: 1px solid var(--color-primary06, #FF8000);
                }
            }
            .dht-icon {
                font-size: 18px;
            }

        }

        .dht-box {
            display: flex;
            width: 100%;
            justify-content: center;
            align-items: center;
            padding: 8px 0;
            border-radius: 2px 0px 0px 2px;
            border: 1px solid #E0E0E0;
        }
    }
}
</style>
