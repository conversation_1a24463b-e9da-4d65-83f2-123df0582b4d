/**
 * 自动导入css
 * Created By Ys on 20210107
 */

function generateImportCssSource(css, projectName = 'nsail') {
  return [
    '(function(){',
    'var s=document.createElement("link");',
    's.rel="stylesheet";',
    `s.href= Fx.ROOT_PATH+"/${projectName}-dist/${css}";`,
    'document.head.appendChild(s)',
    '})();',
  ].join('');
}

class AutoImportCssPlugin {
  constructor(options) {
    this.options = options || {};
  }

  apply(compiler) {
    let cssPath;
    compiler.hooks.webpackManifestPluginAfterEmit.tap(
      'AutoImportCssPlugin',
      (manifest) => {
        // 依赖  webpack-manifest-plugin 输出的格式
        for (const key in manifest) {
          if (/\.css$/.test(manifest[key])) {
            cssPath = manifest[key];
          }
        }
      },
    );
    compiler.hooks.emit.tapAsync(
      'AutoImportCssPlugin',
      (compilation, callback) => {
        const reg = new RegExp(`^${this.options.mainEntry}.(.*).js$`);
        const entryJs = Object.keys(compilation.assets).find((key) => {
          // return /^dht\.(.*)\.js$/.test(key);
          return reg.test(key);
        });
        const source = compilation.assets[entryJs].source();

        compilation.assets[entryJs] = {
          source: () => {
            return generateImportCssSource(cssPath, this.options.projectName) + source;
          },

          size: () => {
            return (generateImportCssSource(cssPath, this.options.projectName) + source).length;
          },
        };

        callback();
      },
    );
  }
}

module.exports = AutoImportCssPlugin;
