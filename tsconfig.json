{
  "compilerOptions": {
    "baseUrl": ".",
    "outDir": "dist",
    "sourceMap": false,
    "target": "es5",
    "module": "esnext",
    "moduleResolution": "node",
    "allowJs": false,
    "strict": true,
    "noUnusedLocals": true,
    "importHelpers": true,
    "experimentalDecorators": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "removeComments": false,
    "allowSyntheticDefaultImports": true,
    "jsx": "preserve",
    "lib": [
      "dom",
      "es5",
      "es2015.promise"
    ],
    "types": [
      "node"
    ]
  },
  "include": [
    "src/**/*",
    "typings/*",
  ],
  "exclude": [
    "node_modules"
  ],
}
