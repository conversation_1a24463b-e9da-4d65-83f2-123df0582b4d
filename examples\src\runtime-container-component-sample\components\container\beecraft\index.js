// import runningHooks from './running-hooks';
export default function () {
    return {
        name: 'dht_web_container_product_list',
        displayName: $t('paas.widget.dht_web_container_product_list.name', '商品列表容器'),
        data: {
            style: {
                justifyContent: 'center',
                alignItems: 'center',
            }
        },
        $$data: {
            // template: {
            //     name: 'dht_web_container_product_list',
            //     children: [       
            //         {
            //             name: 'dht_web_product_list_shop_list',
            //             data: {
                            
            //             }
            //         },             
            //         {
            //             name: 'dht_web_product_list_category_tree',
            //             data: {
                           
            //             }
            //         },
                    
            //     ]
            // },
            isContext: true,
            isCanvas: true,
            contextNode: {
                data: {
                    dhtPageData: {
                        test: 'test123'
                    },
                    dhtContainerApi: {},
                }
            }
        },
        related: {
            attributeSettings: [
                {
                  name: 'SetterField',
                  data: {
                    setter: {
                      component: () => import('./setting.vue')
                    }
                  }
            }],
            previewDisplay: () => import(
                /* webpackChunkName: "dht_web_container_product_list-beecraft-PreviewDisplay" */
                './display.vue'
            )
        },
        rules: {
            canMoveIn: (nodes) => {
                // return !nodes.some(node => node.name !== 'rich_text_widget' && node.name !== 'button');
                return true;
            }
        },
        // hooks: runningHooks.hooks,
    };
}
