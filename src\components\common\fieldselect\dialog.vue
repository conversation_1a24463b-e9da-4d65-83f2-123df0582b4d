<template>
  <fx-dialog
    :visible.sync="visible"
    :title="title"
    width="80%"
    :close-on-click-modal="false"
    class="field-selector-dialog"
  >
    <div class="field-selector-content">
      <!-- 左侧选择区域 -->
      <div class="left-panel">
        <!-- 搜索框 -->
        <div class="search-panel" v-if="showSearch">
          <fx-input
            v-model="searchKeyword"
            placeholder="搜索..."
            prefix-icon="el-icon-search"
            clearable
            size="small"
            @input="onSearchInput"
          />
        </div>

        <!-- Tabs栏 - 只有多个tab时显示 -->
        <fx-tabs
          v-if="displayTabs.length > 1"
          v-model="currentTabId"
          type="border-card"
          stretch
          @tab-click="onTabClick"
        >
          <fx-tab-pane
            v-for="tab in displayTabs"
            :key="tab.id"
            :name="tab.id"
            :label="tab.title"
          >
            <div class="tab-content">
              <list-panel
                :tab-data="tab"
                :search-keyword="searchKeyword"
                :enable-three-columns="enableThreeColumns"
                :selected-items="selectedItems[tab.id] || []"
                @item-click="onItemClick"
                @select-all="onSelectAll"
              />
            </div>
          </fx-tab-pane>
        </fx-tabs>

        <!-- 单tab时直接显示内容 -->
        <div v-else-if="displayTabs.length === 1" class="single-tab-content">
          <list-panel
            :tab-data="displayTabs[0]"
            :search-keyword="searchKeyword"
            :enable-three-columns="enableThreeColumns"
            :selected-items="selectedItems[displayTabs[0].id] || []"
            @item-click="onItemClick"
            @select-all="onSelectAll"
          />
        </div>
      </div>

      <!-- 右侧已选区域 -->
      <div class="right-panel">
        <div class="selected-header">
          <span class="selected-title">当前选定的字段 ({{ selectedCount }}/{{ totalCount }})</span>
          <fx-button
            type="text"
            size="small"
            @click="clearAll"
            v-if="selectedCount > 0"
          >
            清空
          </fx-button>
        </div>

        <fx-scrollbar class="selected-content">
          <div v-if="selectedItemsList.length > 0" class="selected-list">
            <fx-draggable
              v-if="enableDragSort"
              :list="selectedItemsList"
              handle=".drag-handle"
              @end="onDragEnd"
              class="draggable-list"
            >
              <div
                v-for="item in selectedItemsList"
                :key="item.tabId + '-' + item.id"
                class="selected-item"
              >
                <i class="el-icon-menu drag-handle"></i>
                <span class="item-name" :title="item.name">{{ item.name }}</span>
                <i
                  class="fx-icon-delete remove-btn"
                  @click="removeItem(item)"
                ></i>
              </div>
            </fx-draggable>

            <div v-else>
              <div
                v-for="item in selectedItemsList"
                :key="item.tabId + '-' + item.id"
                class="selected-item"
              >
                <span class="item-name" :title="item.name">{{ item.name }}</span>
                <i
                  class="fx-icon-delete remove-btn"
                  @click="removeItem(item)"
                ></i>
              </div>
            </div>
          </div>

          <div v-else class="empty-tip">
            暂无选择的字段
          </div>
        </fx-scrollbar>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <fx-button @click="onCancel">取消</fx-button>
      <fx-button type="primary" @click="onConfirm">确定</fx-button>
    </div>
  </fx-dialog>
</template>

<script>
import ListPanel from './list-panel.vue'

export default {
  name: 'FieldSelect',
  components: {
    ListPanel
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '选择字段'
    },
    tabs: {
      type: Array,
      default: () => []
    },
    enableThreeColumns: {
      type: Boolean,
      default: true
    },
    enableDragSort: {
      type: Boolean,
      default: true
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentTabId: '',
      searchKeyword: '',
      selectedItemsList: []
    }
  },
  computed: {
    displayTabs() {
      return this.tabs.filter(tab => !tab.hidden)
    },
    selectedCount() {
      return this.selectedItemsList.length
    },
    totalCount() {
      let total = 0
      this.tabs.forEach(tab => {
        if (tab.data) {
          total += tab.data.length
        }
      })
      return total
    },
    selectedItems() {
      return this.selectedItemsList.reduce((acc, item) => {
        if (!acc[item.tabId]) acc[item.tabId] = []
        acc[item.tabId].push(item.id)
        return acc
      }, {})
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initData()
      }
    },
    value: {
      handler(newVal) {
        if (Array.isArray(newVal)) {
          this.selectedItemsList = [...newVal]
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    initData() {
      if (this.displayTabs.length > 0) {
        this.currentTabId = this.displayTabs[0].id
      }
      if (Array.isArray(this.value)) {
        this.selectedItemsList = [...this.value]
      }
    },
    onTabClick(tab) {
      this.currentTabId = tab.name
    },
    onSearchInput() {
      // 搜索逻辑由子组件处理
    },
    onItemClick(item, tabId) {
      const idx = this.selectedItemsList.findIndex(i => i.id === item.id && i.tabId === tabId)
      if (idx > -1) {
        this.selectedItemsList.splice(idx, 1)
      } else {
        this.selectedItemsList.push({ ...item, tabId })
      }
      this.$emit('input', [...this.selectedItemsList])
      this.$emit('change', [...this.selectedItemsList])
    },
    onSelectAll(tabId, isSelectAll) {
      const tab = this.tabs.find(t => t.id === tabId)
      if (!tab) return
      this.selectedItemsList = this.selectedItemsList.filter(item => item.tabId !== tabId)
      if (isSelectAll) {
        const addList = tab.data.map(item => ({ ...item, tabId }))
        this.selectedItemsList = [...this.selectedItemsList, ...addList]
      }
      this.$emit('input', [...this.selectedItemsList])
      this.$emit('change', [...this.selectedItemsList])
    },
    removeItem(item) {
      const idx = this.selectedItemsList.findIndex(i => i.id === item.id && i.tabId === item.tabId)
      if (idx > -1) {
        this.selectedItemsList.splice(idx, 1)
        this.$emit('input', [...this.selectedItemsList])
        this.$emit('change', [...this.selectedItemsList])
      }
    },
    clearAll() {
      this.selectedItemsList = []
      this.$emit('input', [])
      this.$emit('change', [])
    },
    onDragEnd(newList) {
      if (!Array.isArray(newList)) {
        newList = [...this.selectedItemsList]
      }
      this.selectedItemsList = [...newList]
      this.$emit('input', [...this.selectedItemsList])
      this.$emit('sort-change', [...this.selectedItemsList])
      this.$emit('change', [...this.selectedItemsList])
    },
    onConfirm() {
      this.$emit('confirm', [...this.selectedItemsList])
    },
    onCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="less" scoped>
.field-selector-dialog {
  /deep/ .el-dialog__body {
    padding: 0;
  }
}

.field-selector-content {
  display: flex;
  height: 500px;
  max-height: 70vh;
}

.left-panel {
  width: 70%;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
}

.right-panel {
  width: 30%;
  display: flex;
  flex-direction: column;
}

.search-panel {
  padding: 12px 16px;
}

.single-tab-content,
.tab-content {
  flex: 1;
  height: 0;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.selected-title {
  font-weight: 500;
  color: #303133;
}

.selected-content {
  flex: 1;
  height: 0;
  padding: 8px;
}

.selected-list {
  .draggable-list {
    min-height: 100%;
  }
}

.selected-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
}

.drag-handle {
  color: #545861;
  margin-right: 8px;
  cursor: move;
  font-size: 14px;

  &:active {
    cursor: grabbing;
  }
}

.item-name {
  flex: 1;
  font-size: 14px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-btn {
  color: #545861;
  cursor: pointer;
  margin-left: 6px;
}

.empty-tip {
  text-align: center;
  color: #909399;
  padding: 40px 20px;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
  padding: 8px 16px;
  border-top: 1px solid #ebeef5;
}

.el-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;

  /deep/ .el-tabs__content {
    flex: 1;
    height: 0;
    padding: 0;
  }

  /deep/ .el-tab-pane {
    height: 100%;
  }
}
</style>