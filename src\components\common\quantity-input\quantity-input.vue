<template>
    <div class="dht-quantity-input">
      <template v-if="!product.is_spec">
        <fk-input-number
          ref="fkInput"
          :min="0"
          :precision="precision"
          @enter="cartAction(product)"
          @blur="cartAction(product)"
          v-model.trim="quantity">
        </fk-input-number>
        <span style="margin-left: 5px;">
          <unit-selector
            :units="product._units"
            :value="product._selectUnit"
            @change="unitChange($event, product)">
          </unit-selector>
        </span>
      </template>
      <a v-else size="mini" @click.prevent="chooseSpec(product)">+ {{ $t('选规格') }}</a>
    </div>
</template>

<script src="./_quantity-input.ts" lang="ts"></script>
<style src="./_quantity-input.less" lang="less"></style>
