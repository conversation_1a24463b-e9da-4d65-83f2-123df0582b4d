<template>
  <div class="dht-cart-footer dht-patch-dt-page">
    <div class="dht-cart-footer__left">
      <fx-checkbox v-model="selectAll" @change="selectAllItem">{{ $t('全选') }}</fx-checkbox>
      <span v-if="invalidCount > 0" class="clear-all-invalid" @click="clearAllInvalid">
        {{ $t('清空失效商品') }}
      </span>
    </div>
    <div class="dht-cart-footer__right">
      <span class="summary" v-html="i18nSummary"></span>
      <fx-button type="primary" size="mini" @click="createOrder">{{ $t('立即结算') }}</fx-button>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'DhtCartFooter',

  data() {
    return {
      selectAll: false,
      invalidCount: 0,
      total: 0,
      amount: 0,
      salesAmount: 0,
    };
  },

  computed: {
    i18nSummary() {
      const data = {
        total: `<span class="summary-total">${this.total}</span>`,
        amount: `<span class="summary-amount">${this.formatMoney(this.amount)}</span>`,
        salesAmount: `<span class="summary-amount">${this.formatMoney(this.salesAmount)}</span>`,
      };
      return $t('已选 {{total}} 种商品，金额总计：{{amount}}，成交金额总计：{{salesAmount}}', { data });
    },
  },

  created() {
    $dht.log('VueComponent cartfooter created');
    this._onUpdateCartSummary = () => {
      $dht.log('Update cart summary event');
      this.updateCartSummary();
    };
    window.$dht.store.on('store:cart.summary.reset', this._onUpdateCartSummary);
  },

  beforeDestroy() {
    window.$dht.store.off('store:cart.summary.reset', this._onUpdateCartSummary);
    this._onUpdateCartSummary = null;
  },

  destroyed() {
    $dht.log('VueComponent cartfooter destroyed');
  },

  methods: {
    selectAllItem(value: boolean) {
      $dht.log('selectAll: %s', value);

      // 模拟表头的全选框点击效果，来达到数据更新闭环
      const e = document.createEvent('MouseEvents');
      e.initEvent('click', true, true);

      const targets = this.$el.parentNode.getElementsByClassName('j-all-checkbox');
      if (targets && targets.length > 0) {
        targets[0].dispatchEvent(e);
      }
    },
    clearAllInvalid() {
      // TODO
    },
    createOrder() {
      this.$emit('order-create');
    },
    updateCartSummary() {
      const summary: any = window.$dht.store.getCartSummary();
      this.total = summary.settled_product_kind;
      this.amount = summary.settled_product_amount;
      this.salesAmount = summary.settled_amount;
      this.invalidCount = summary.invalid_count;

      const list: any[] = window.$dht.store.getCartList() || [];
      this.selectAll = list.length === this.total;
    },
    formatMoney(money: string | number, currency: string): string {
      const $global: any = global;
      const str = $global.CRM.util.formatMoneyForCurrency(money, currency);
      // 主站的代码真是蛋疼，多此一举
      return str.replace('<em>', '').replace('</em>', '');
    },
  },
} as VueComponent;
</script>

<style lang="less">
.dht-patch-dt-page {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
}
.dht-cart-footer {
  display: flex;
  justify-content: space-between;
  height: 32px;
  line-height: 32px;
  padding: 8px 15px;
  border-top: 1px solid #e5e9f2;
  background-color: #fff;

  .clear-all-invalid {
    color: #545861;
    cursor: pointer;
  }

  .summary {
    margin-right: 5px;
    font-size: 13px;
    color: #999;
  }

  .summary-total {
    color: #ff8000;
  }

  .summary-amount {
    font-size: 17px;
    color: #ff8000;
  }
}
</style>
