<script lang="ts">
import 'reflect-metadata';
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';

@Component({
  name: 'CategoryTreeItem'
})
export default class CategoryTreeItem extends Vue {
  @Prop({default: () => ([])}) readonly items!: Array<any>;
  @Prop({default: 1}) readonly level!: number;
  @Prop({default: null}) readonly parentNode!: any;

  isExpanded = false;
  needExpand = false;
  activeItem: any = null;

  get getLevelLabel() {
    const labels: any = {
      1: $t('first.category'), // 一级分类
      2: $t('second.category'), // 二级分类
      3: $t('third.category'), // 三级分类
    }
    return labels[this.level] || `${this.level}${$t('级')}${$t('分类')}`;
  }

  @Watch('items', { deep: true })
  onItemChange() {
    this.$nextTick(() => {
      this.checkNeedExpand();
    });
  }

  mounted() {
    this.checkNeedExpand();
    window.addEventListener('resize', this.checkNeedExpand);
  }

  beforeDestroy() {
    window.removeEventListener('resize', this.checkNeedExpand);
  }

  hasChildren(item: any) {
    return item.children && item.children.length > 0;
  }

  handleClick(item: any) {
    if (item.active) {
      this.$set(item, 'active', false);
      this.activeItem = null;
      if (this.hasChildren(item)) {
        this.clearChildrenSelection(item.children);
      }
      return;
    }

    this.items.forEach(i => {
      if (i !== item && i.active) {
        this.$set(i, 'active', false);
        if (this.hasChildren(i)) {
          this.clearChildrenSelection(i.children);
        }
      }
    });

    this.$set(item, 'active', true);
    this.activeItem = item;

    if (this.hasChildren(item)) {
      this.isExpanded = true;
      const firstChild = item.children[0];
      this.$set(firstChild, 'active', true);

      if (this.hasChildren(firstChild)) {
        this.autoSelectFirstChild(firstChild);
      }
    }

    if (item.id !== '1') {
      this.$emit('select', item, this.level);
    } else {
      if (this.parentNode) {
        this.$emit('select', this.parentNode, this.level - 1);
      } else {
        this.$emit('select', null, this.level);
      }
    }
  }

  autoSelectFirstChild(item: any) {
    if (this.hasChildren(item)) {
      const firstChild = item.children[0];
      this.$set(firstChild, 'active', true);
      this.autoSelectFirstChild(firstChild);
    }
  }

  clearChildrenSelection(children: any[]) {
    if (children) {
      children.forEach(child => {
        this.$set(child, 'active', false);
        if (this.hasChildren(child)) {
          this.clearChildrenSelection(child.children);
        }
      });
    }
  }

  handleChildSelect(item: any, level: number) {
    this.$emit('select', item, level);
  }

  toggleExpand() {
    this.isExpanded = !this.isExpanded;
  }

  checkNeedExpand() {
    this.$nextTick(() => {
      const container = this.$el.querySelector('.items-container');
      if (container) {
        let totalWidth = 0;
        const items = container.querySelectorAll('.level-item');
        // 计算所有子项的实际总宽度（包括内边距和外边距）
        items.forEach((item: any) => {
          const style = window.getComputedStyle(item);
          const width = item.offsetWidth +
            parseInt(style.marginLeft) +
            parseInt(style.marginRight);
          totalWidth += width;
        });

        const containerWidth = container.clientWidth;

        // 总宽度超过容器宽度，就显示展开按钮
        this.needExpand = totalWidth > containerWidth;
      }
    });
  }
}
</script>

<template src="./category-tree-item.html"></template>
<style src="./category-tree-item.less" lang="less" scoped></style>
