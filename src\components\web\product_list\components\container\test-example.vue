<template>
  <div class="test-container">
    <h2>商品列表容器组件测试示例</h2>
    
    <!-- 容器组件 -->
    <div class="container-wrapper">
      <dht-web-container-product-list
        :initial-category-id="initialCategoryId"
        :initial-keyword="initialKeyword"
        :api-config="apiConfig"
        :page-config="pageConfig">
        
        <!-- 使用作用域插槽获取容器数据 -->
        <template #default="{ eventBus, bizPageData, containerMethods }">
          <div class="layout">
            <!-- 左侧：分类树 -->
            <div class="sidebar">
              <dht-web-category-tree
                :event-bus="eventBus"
                :biz-page-data="bizPageData"
                :container-methods="containerMethods">
              </dht-web-category-tree>
            </div>
            
            <!-- 右侧：搜索和列表 -->
            <div class="main-content">
              <!-- 搜索框 -->
              <div class="search-section">
                <dht-web-search-box
                  :event-bus="eventBus"
                  :biz-page-data="bizPageData"
                  :container-methods="containerMethods"
                  placeholder="搜索商品..."
                  :show-history="true"
                  :show-hot-search="true">
                </dht-web-search-box>
              </div>
              
              <!-- 商品列表 -->
              <div class="list-section">
                <dht-web-shop-list
                  :event-bus="eventBus"
                  :biz-page-data="bizPageData"
                  :container-methods="containerMethods"
                  @product-click="handleProductClick"
                  @add-to-cart="handleAddToCart">
                </dht-web-shop-list>
              </div>
            </div>
          </div>
          
          <!-- 调试面板 -->
          <div v-if="showDebugPanel" class="debug-panel">
            <h3>调试信息</h3>
            
            <!-- 当前状态 -->
            <div class="debug-section">
              <h4>当前状态</h4>
              <pre>{{ JSON.stringify(bizPageData, null, 2) }}</pre>
            </div>
            
            <!-- 事件日志 -->
            <div class="debug-section">
              <h4>事件日志</h4>
              <div class="event-log">
                <div 
                  v-for="(log, index) in eventLogs" 
                  :key="index"
                  class="log-item">
                  <span class="log-time">{{ log.time }}</span>
                  <span class="log-event">{{ log.event }}</span>
                  <span class="log-data">{{ JSON.stringify(log.data) }}</span>
                </div>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="debug-actions">
              <fx-button @click="clearEventLogs">清空日志</fx-button>
              <fx-button @click="triggerGlobalReset">全局重置</fx-button>
              <fx-button @click="exportDebugData">导出调试数据</fx-button>
            </div>
          </div>
        </template>
      </dht-web-container-product-list>
    </div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <h3>测试控制</h3>
      
      <div class="control-group">
        <label>显示调试面板：</label>
        <fx-switch v-model="showDebugPanel"></fx-switch>
      </div>
      
      <div class="control-group">
        <label>初始分类ID：</label>
        <fx-input-number v-model="initialCategoryId" :min="0"></fx-input-number>
      </div>
      
      <div class="control-group">
        <label>初始搜索关键字：</label>
        <fx-input v-model="initialKeyword" placeholder="输入关键字"></fx-input>
      </div>
      
      <div class="control-group">
        <label>分页大小：</label>
        <fx-select v-model="pageConfig.pageSize">
          <fx-option :value="10" label="10"></fx-option>
          <fx-option :value="20" label="20"></fx-option>
          <fx-option :value="50" label="50"></fx-option>
        </fx-select>
      </div>
    </div>
  </div>
</template>

<script>
// 导入组件
import DhtWebContainerProductList from './src/index.vue';
import DhtWebCategoryTree from '../category-tree/src/index.vue';
import DhtWebSearchBox from '../search-box/src/index.vue';
import DhtWebShopList from '../shop-list/src/index.vue';

export default {
  name: 'ProductListTestExample',
  components: {
    DhtWebContainerProductList,
    DhtWebCategoryTree,
    DhtWebSearchBox,
    DhtWebShopList
  },
  
  data() {
    return {
      // 测试配置
      showDebugPanel: true,
      initialCategoryId: null,
      initialKeyword: '',
      
      // API配置
      apiConfig: {
        listApi: 'ShopMall',
        categoryApi: 'Category',
        searchApi: 'Search'
      },
      
      // 分页配置
      pageConfig: {
        pageSize: 20,
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '50', '100']
      },
      
      // 调试数据
      eventLogs: []
    };
  },
  
  methods: {
    /**
     * 处理商品点击
     */
    handleProductClick(product) {
      console.log('测试页面: 商品点击', product);
      this.$message.info(`点击了商品: ${product.name}`);
    },
    
    /**
     * 处理加入购物车
     */
    handleAddToCart(product) {
      console.log('测试页面: 加入购物车', product);
      this.$message.success(`${product.name} 已加入购物车`);
    },
    
    /**
     * 清空事件日志
     */
    clearEventLogs() {
      this.eventLogs = [];
    },
    
    /**
     * 触发全局重置
     */
    triggerGlobalReset() {
      // 这里需要获取到容器的eventBus来触发事件
      // 在实际使用中，可以通过ref或其他方式获取
      this.$message.info('全局重置已触发');
    },
    
    /**
     * 导出调试数据
     */
    exportDebugData() {
      const debugData = {
        eventLogs: this.eventLogs,
        timestamp: new Date().toISOString(),
        config: {
          initialCategoryId: this.initialCategoryId,
          initialKeyword: this.initialKeyword,
          apiConfig: this.apiConfig,
          pageConfig: this.pageConfig
        }
      };
      
      const dataStr = JSON.stringify(debugData, null, 2);
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `product-list-debug-${Date.now()}.json`;
      link.click();
      
      URL.revokeObjectURL(url);
      this.$message.success('调试数据已导出');
    },
    
    /**
     * 记录事件日志
     */
    logEvent(eventName, eventData) {
      this.eventLogs.unshift({
        time: new Date().toLocaleTimeString(),
        event: eventName,
        data: eventData
      });
      
      // 限制日志数量
      if (this.eventLogs.length > 100) {
        this.eventLogs = this.eventLogs.slice(0, 100);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.test-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.container-wrapper {
  margin-bottom: 30px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.layout {
  display: flex;
  min-height: 600px;
  
  .sidebar {
    width: 280px;
    border-right: 1px solid #f0f0f0;
    padding: 16px;
    background-color: #fafafa;
  }
  
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .search-section {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      background-color: #fff;
    }
    
    .list-section {
      flex: 1;
      padding: 16px;
      background-color: #fff;
    }
  }
}

.debug-panel {
  margin-top: 20px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  
  h3, h4 {
    margin-top: 0;
    color: #333;
  }
  
  .debug-section {
    margin-bottom: 20px;
    
    pre {
      background-color: #fff;
      padding: 12px;
      border-radius: 4px;
      border: 1px solid #e8e8e8;
      max-height: 200px;
      overflow-y: auto;
      font-size: 12px;
    }
  }
  
  .event-log {
    max-height: 300px;
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    
    .log-item {
      display: flex;
      padding: 8px 12px;
      border-bottom: 1px solid #f0f0f0;
      font-size: 12px;
      
      &:last-child {
        border-bottom: none;
      }
      
      .log-time {
        width: 80px;
        color: #666;
        margin-right: 12px;
      }
      
      .log-event {
        width: 150px;
        font-weight: 500;
        color: #1890ff;
        margin-right: 12px;
      }
      
      .log-data {
        flex: 1;
        color: #333;
        word-break: break-all;
      }
    }
  }
  
  .debug-actions {
    display: flex;
    gap: 12px;
  }
}

.control-panel {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  
  h3 {
    margin-top: 0;
    color: #333;
  }
  
  .control-group {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    label {
      width: 150px;
      color: #666;
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .layout {
    flex-direction: column;
    
    .sidebar {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid #f0f0f0;
    }
  }
}
</style>
