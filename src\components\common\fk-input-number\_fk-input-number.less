.fk-input-number {
  box-sizing: border-box;
  font-variant: tabular-nums;
  list-style: none;
  font-feature-settings: "tnum";
  position: relative;
  width: 100%;
  height: 32px;
  color: rgba(0,0,0,.65);
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
  background-image: none;
  transition: all .3s;
  display: inline-block;
  margin: 0;
  padding: 0;
  border: 1px solid #d9d9d9;
  border-radius: 4px;

  &-focused {
    outline: 0;
    box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
  }

  &-disabled {
    color: rgba(0,0,0,.25);
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 1;
    &:hover {
      border-color: #d9d9d9 !important;
      border-right-width: 1px!important;
    }
    .fk-input-number-handler-wrap {
      display: none;
    }
    .fk-input-number-input {
      cursor: not-allowed;
      color: rgba(0,0,0,.25);
    }
  }

  &-focused,
  &:hover {
    border-color: #ff8000;
    border-right-width: 1px!important;

    .fk-input-number-handler-wrap {
      opacity: 1;
    }
  }

  &-handler-wrap {
    position: absolute;
    top: 0;
    right: 0;
    width: 22px;
    height: 100%;
    background: #fff;
    border-left: 1px solid #d9d9d9;
    border-radius: 0 4px 4px 0;
    opacity: 0;
    transition: opacity .24s linear .1s;
    box-sizing: border-box;
    &:hover {
      .fk-input-number-handler {
        height: 40%;
      }
    }
  }

  &-handler {
    position: relative;
    display: block;
    width: 100%;
    height: 50%;
    overflow: hidden;
    color: rgba(0,0,0,.45);
    font-weight: 700;
    line-height: 0;
    text-align: center;
    transition: all .1s linear;
    box-sizing: border-box;
    &-disabled {
      cursor: not-allowed !important;
    }
    &-up {
      border-top-right-radius: 4px;
      cursor: pointer;
      &:hover {
        height: 60%!important;
        i {
          color: #ff8000;
        }
      }
    }
    &-down {
      top: 0;
      border-top: 1px solid #d9d9d9;
      border-bottom-right-radius: 4px;
      cursor: pointer;
      &:hover {
        height: 60%!important;
        i {
          color: #ff8000;
        }
      }
    }

    &-down-inner,
    &-up-inner {
      display: inline-block;
      font-style: normal;
      text-align: center;
      text-transform: none;
      vertical-align: -.125em;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      position: absolute;
      right: 4px;
      width: 12px;
      height: 12px;
      color: rgba(0,0,0,.45);
      line-height: 12px;
      font-size: 14px;
      font-weight: 600;
      transition: all .1s linear;
      user-select: none;
      transform: scale(.58333333) rotate(0deg);
    }

    &-up-inner {
      top: 50%;
      margin-top: -5px;
      text-align: center;
      display: inline-block;
      font-size: 12px;
      min-width: auto;
      margin-right: 0;
      font-size: 12px;
    }
  }

  &-input {
    width: 100%;
    height: 30px;
    padding: 0 11px;
    text-align: left;
    background-color: transparent;
    border: 0;
    border-radius: 4px;
    outline: 0;
    transition: all .3s linear;
    -moz-appearance: textfield!important;
    box-sizing: border-box;
  }
}
