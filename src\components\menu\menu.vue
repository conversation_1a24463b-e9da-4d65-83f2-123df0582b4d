<template>
  <div class="crm-menu crm-aside portal-menu"></div>
</template>

<script lang="ts">
import { api } from '../../api';
import asyncLoadAside from './component';

const Component: VueComponent = {
  props: ['appId'],

  created() {
    // TODO 这里需要在destroyed里回收吗
    this.$router.afterEach(() => {
      this.refresh();
    });
  },

  mounted() {
    this.mountMenu();
  },

  destroyed() {
    if (this.$menu) {
      this.$menu.destroy();
    }
  },

  methods: {
    /**
     * 创建并挂载菜单组件
     */
    mountMenu(): Promise<void> {
      return asyncLoadAside().then((Ctor) => {
        this.$menu = new Ctor({
          el: this.$el,
          apiMenus: api.getUserMenus,
          appId: this.appId || this.$query!('appId'),
        });
      });
    },
    /**
     * 菜单刷新
     */
    refresh() {
      if (!this.$menu) return;
      this.$menu.setHighlight();
      this.$menu.updataScroll();
    },
    /**
     * 菜单折叠展开
     */
    toggle(expand = false) {
      if (!this.$menu) return;
      // eslint-disable-next-line no-unused-expressions
      !expand
        ? this.$menu.fold()
        : this.$menu.unfold();
    },
  },

};

export default Component;
</script>

<style>
.portal-menu .set-search-wrap .search-ipt {
  width: 142px;
}
.portal-menu .set-search-wrap {
  width: 200px;
}
.portal-menu .quickly {
  display: none;
}
</style>
