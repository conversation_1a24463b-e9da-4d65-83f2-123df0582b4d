const path = require('path');
// const webpack = require('webpack');
const { VueLoaderPlugin } = require('vue-loader');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const TransferToCmdPlugin = require('webpack-transfer-cmd-plugin');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const OptimizeCssAssetsPlugin = require('optimize-css-assets-webpack-plugin');
// const HtmlWebpackPlugin = require('html-webpack-plugin');
// const ESLintPlugin = require('eslint-webpack-plugin');

module.exports = {
  mode: 'production',

  // entry: {
  //   cardItem: './src/components/product/carditem/carditem.vue',
  // },
  entry: {
    carditem: './src/components/product/carditem/carditem.vue',
  },

  output: {
    filename: '[name].js',
    path: path.resolve(__dirname, '../packages'),
    libraryTarget: 'commonjs2',
    // library: 'dht_carditem',
  },

  // devtool: 'inline-source-map',

  resolve: {
    extensions: ['.js', '.vue', '.tsx', '.ts', '.json'],
    alias: {
      vue$: 'vue/dist/vue.esm.js',
    },
  },

  module: {
    rules: [
      {
        test: /\.css$/,
        use: [
          'vue-style-loader',
          {
            loader: 'css-loader',
          },
        ],
      },
      {
        test: /\.less$/,
        use: [
          'vue-style-loader',
          {
            loader: 'css-loader',
          },
          'less-loader',
        ],
      },
      {
        test: /\.js?$/,
        loader: 'babel-loader',
        exclude: (file) => (
          /node_modules/.test(file) && !/\.vue\.js/.test(file)
        ),
      },
      {
        test: /\.vue$/,
        loader: 'vue-loader',
        options: {
          compilerOptions: {
            preserveWhitespace: false,
          },
        },
      },
      {
        test: /\.(ts|tsx)$/,
        exclude: /node_modules/,
        use: [
          'babel-loader',
          {
            loader: 'ts-loader',
            options: { appendTsxSuffixTo: [/\.vue$/] },
          },
        ],
      },
      {
        test: /\.(png|svg|jpe?g|gif|woff|woff2|eot|ttf|otf)$/,
        loader: 'file-loader',
        options: {
          esModule: false,
        },
      },
    ],
  },

  optimization: {
    minimizer: [
      new UglifyJsPlugin({
        uglifyOptions: {
          cache: true,
          parallel: true,
          mangle: {
            reserved: ['require', 'exports', 'module'],
          },
          output: {
            comments: false,
          },
          warnings: false,
          compress: {
            drop_console: true,
            drop_debugger: true,
          },
        },
      }),
      new OptimizeCssAssetsPlugin(),
    ],
  },

  plugins: [
    // 请确保引入这个插件来施展魔法
    // new ESLintPlugin({
    //     extensions: ['js', 'vue']
    // }),
    new CleanWebpackPlugin(),
    new VueLoaderPlugin(),
    new TransferToCmdPlugin(),
  ],
};
