import runningHooks from './running-hooks';
export default function () {
    return {
        name: 'dht_web_product_list_category_tree',
        displayName: '分类树',
        data: {
            style: {
                justifyContent: 'center',
                alignItems: 'center',
            }
        },
        related: {
            attributeSettings: [
                {
                  name: 'SetterField',
                  data: {
                    setter: {
                      component: () => import('./setting.vue')
                    }
                  }
            }],
            previewDisplay: () => import(
                './display.vue'
            )
        },
        hooks: runningHooks.hooks
    };
} 