// @vue/component
export default Vue.extend({
  name: 'CardOrderItem',

  components: {},

  mixins: [],

  props: {
    item: Object,
  },

  data() {
    return {};
  },

  computed: {
    orderClassName() {
      const me = this as any;
      return {
        'dht-asc': me.item.status === 1,
        'dht-desc': me.item.status === 2,
      };
    },
  },

  watch: {},

  created() {},

  methods: {},
});
