/**
 * 神策上传
 * @param subModule
 * @param operationId
 * @param eventType
 * @param data
 */
export const upLoadLog = (
  subModule: string,
  operationId: string,
  eventType: string,
  data?: any,
) => {
  Fx.async(['vcrm/sdk'], async (vcrmSdk: any) => {
    const { logService } = await vcrmSdk.widgetService.getService('logService');
    logService.log(subModule, operationId, eventType, data);
  });
};
