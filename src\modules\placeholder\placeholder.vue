<script lang="ts">
import adapterModule from '../mixins/index.vue';
import { getCrmCssComponent } from '../../base/utils';

// 从那个项目搜索
const searchModules = [
    'crm',
    'bi',
];

// 动态查询searchModules中page目录下的组件
const requireModule = (path: string = 'page', moduleName: string, index = 0) => {
    return (<any>$).Deferred((deferred: any) => {
        Fx.async(`${searchModules[index]}-modules/page/${path}/${moduleName}`, (Ctor: BackboneComponent) => {
            if (Ctor) {
                deferred.resolve(Ctor);
            } else if (index >= searchModules.length - 1) {
                deferred.resolve(null);
            } else {
                deferred.reject();
            }
        });
    }).then(
        (Ctor: BackboneComponent) => Ctor,
        () => requireModule(path, moduleName, ++index)
    );
};
const Component: VueComponent = {
    name: 'PortalPalceholder',

    mixins: [adapterModule],

    methods: {
        async getCssCompoent() {
            return await getCrmCssComponent(this.dirname);
        },

        async getModuleComponent() {
            const Ctor = await requireModule(this.component, this.component);
            return Ctor;
        },

        async getModuleCtor() {
            await this.getCssCompoent();
            return await this.getModuleComponent();
        },
    }
};

export default Component;
</script>