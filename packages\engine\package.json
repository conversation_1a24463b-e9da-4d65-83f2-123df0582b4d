{"name": "@beecraft/engine", "version": "1.7.83", "description": "", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"build": "vite build", "clean-npm": "node ./scripts/clean-npm.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@beecraft/core": "workspace:~", "@beecraft/fxui": "workspace:~", "@beecraft/materials": "workspace:~", "@beecraft/setters": "workspace:~", "@beecraft/shared": "workspace:~", "@beecraft/workbench": "workspace:~"}}