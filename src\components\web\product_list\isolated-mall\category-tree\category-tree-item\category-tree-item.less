.category-row {
  display: flex;
  min-height: 40px;
  border-top: 1px solid var(---Special-02, #EEF0F3);

  &.category-row-1 {
    border-top: none;
  }
}

.level-label {
  width: 88px;
  padding: 10px 0 10px 0;
  margin-top: 5px;
  color: var(--color-neutrals11);
  font-size: 14px;
  flex-shrink: 0;
}

.category-content {
  flex: 1;
  position: relative;
  padding: 5px 70px 5px 0;
  width: 0;
  min-width: 0;
}

.items-container {
  min-height: 30px;
  line-height: 30px;
  overflow: hidden;
  padding: 5px 0;
  width: 100%;

  &:not(.expanded) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &.expanded {
    height: auto;
    white-space: normal;
    display: block;
  }

  .level-item {
    display: inline-block;
    padding: 0 12px;
    margin: 0 8px 8px 0;
    cursor: pointer;
    font-size: 14px;
    color: #181C25;
    position: relative;
    border-radius: 6px;
    transition: all .3s ease-in-out;

    &:hover {
      background: #fff7f2;
      color: #f60;
    }

    &.active {
      color: #f60;

      &.active {
        background: #fff7f2;

        &::after {
          background-color: #f60;
        }
      }

      &:hover {
        color: #ff8533;
      }
    }
  }
}

.expand-control {
  position: absolute;
  right: 0;
  top: 16px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 2px;
  transition: all .3s ease-in-out;

  &:hover {
    background: #fff7f2;
    color: #ff8533;
  }

  .el-icon-arrow-right {
    margin-left: 3px;
    transition: all .3s ease-in-out;

    &.is-expanded {
      transform: rotate(90deg);
    }
  }
}
