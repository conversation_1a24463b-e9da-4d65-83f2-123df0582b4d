/**
 * 节点辅助工具类
 * 提供查找和获取节点实例的通用方法
 */
const nodeHelper = {
  /**
   * 在对象集合中根据名称查找第一个匹配的对象
   * @param {Object} objs - 要搜索的对象集合
   * @param {string} name - 要查找的名称
   * @returns {Object|null} 找到的对象或null
   */
  findFirstObjectByName(objs, name) {
    if (typeof objs !== 'object' || objs === null) {
      return null;
    }

    for (const key in objs) {
      if (objs.hasOwnProperty(key)) {
        const value = objs[key];
        if (typeof value === 'object' && value !== null && value?.name?.toLowerCase() === name?.toLowerCase()) {
          return value;
        }
      }
    }
    return null;
  },

  /**
   * 根据名称获取容器组件实例
   * @param {Object} query - 查询对象
   * @param {string} name - 容器名称
   * @returns {Object|null} 容器实例或null
   */
  getContainerInstanceByName(query, name) {
    const containerNode = this.findFirstObjectByName(query.getNodes(), name);
    return containerNode ? query.instance(containerNode.id) : null;
  }
};

export default nodeHelper;