import {Column} from './table';

export interface FilterRule {
  /**
   * 设置过滤规则
   * @param column：列配置
   * @param val：列值
   * @param comparison：条件
   * @param type：advance 高级筛选  filter 过滤，高级检索条件任意添加  过滤替换已有条件
   * @param item
   */
  setRule (column: Column, val: any, comparison: string, type: 'advance' | 'filter', item: {FilterGroup: any}): void;

  /**
   * 获取规则
   */
  getRule (): [{
    type: string;
    value: Array<{
      Comparison: number;
      FieldName: string;
      FilterValue: any;
      FilterGroup: string;
    }>;
    rules: Array<{
      Comparison: number;
      FieldName: string;
      FilterValue: any;
      FilterGroup: string;
      type: 'advance' | 'filter';
      // 字段类型，3：金额，4：时间，10：日期，6：单选，7：多选，9：图片，11：二级及联
      fieldType: string;
    }>
  }];

  /**
   * 删除指定名称的规则
   * @param name
   * @param comparison: 删除指定条件的
   * @param value: 指定值
   */
  delRule (name: string, comparison?: string, value?: any): void;

  /**
   * 清除所搜搜索规则
   */
  clearRule (): void;
}
