<template>
    <div class="picture-component">
        <h1>低代码PaaS平台能力全景图</h1>
        <div>
            <img src="https://www.fxiaoke.com/ap/wp-content/uploads/2021/09/<EMAIL>" alt="picture-component">
        </div>
        <div>{{ text }}</div>
    </div>
</template>
<script>
export default {
    props: {
        text: String,
        filters: Object
    },
    watch: {
        filters(newVal) {
            debugger;
            console.log(newVal);
        }
    },
    beecraft: function () {
        return {
            data: {
                filters: {
                    name: 'test'
                }
            },
            $$data: {
                layer: {
                    w: 6,
                    h: 10,
                },
                noWrapper: true
            },
            related: {
                attributeSettings: [{
                    name: 'SetterField',
                    data: {
                        label: '单行文本',
                        display: 'block',
                        setter: {
                            component: 'ColorPickerSetter',
                            ranges: ['data', 'text']
                        }
                    }
                }]
            },
            /* hooks: {
                created(node) {
                    console.log('running hooks created', node);
                    console.log('running hooks created', ...arguments);
                    
                    // if (node.data.basicInfo.state === 2) {
                    //     setLayerSizeByNode(node);
                    //     setLayerEditNameIconByNode(node, name); //编辑名称
                    //     setLayerEditIconByNode(node, name); //编辑 保存
                    //     setLayerInteractionIconByNode(node); //联动
                    // }
                    // 设置是否可以下钻
                    // setCanDrill(node);
                },
                beforeRender(node, { query, actions }) {
                    console.log('running hooks beforeRender', ...arguments);
                    // setFilterDataConfig(...arguments);
                    console.log('running hooks beforeRender query:', query);
                    console.log('running hooks beforeRender actions:', actions);
                    
                },
                rendered(node, { query, actions }) {
                    console.log('running hooks rendered', ...arguments);
                    // bindLinkageInitiator(...arguments);
                    // bindLinkageRecevier(...arguments);
                    console.log('running hooks rendered query:', query);
                    console.log('running hooks rendered actions:', actions);
                }
            } */
        }
    }
}
</script>
<style lang="less" scoped>
h1 {
    text-align: center;
}

.picture-component {
    width: 100%;
    box-sizing: border-box;

    img {
        width: 100%;
    }
}
</style>