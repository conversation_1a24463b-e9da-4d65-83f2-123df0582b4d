import { getCrmActionComponent } from '../../base/bridge';
import { actionExtend } from './action/extend';

/* eslint-disable func-names */

/**
 * 新建回款
 * @param data
 *  apiname: 'PaymentObj',
    dataIds: [],
    dataList: [],
    describe: {fields: {…}, actions: {…}, index_version: 1, _id: "", tenant_id: "78436", …}
    displayName: '回款',
    field_list: [],
    pageApiname: undefined,
    queryParam: { .... },
    recordType: undefined,
    source: 'list',
 */
function addAction(param: any): Promise<any> {
  const type = param.__type || 'add';
  const data = _.omit(param, ['success', 'error', 'destroy', '__type']);
  /* eslint-disable-next-line prefer-template */
  data.title = param.title || (type === 'edit' ? $t('编辑') : $t('新建')) + (' ' + (param.displayName || ''));
  data.show_type = param.show_type || 'full';
  data._error = function () {
    param.error && param.error.apply(null, arguments);
  };
  CRM.util.waiting();
  return getCrmActionComponent('paymentobj', actionExtend)
    .then((Ctor: BackboneComponent) => {
      CRM.util.waiting(false);
      const widget = new Ctor(data);
      widget.on('refresh', function (res: any) {
        param.success && param.success.apply(null, arguments);
      });
      widget[type](data);
    });
}

export const add = addAction;
