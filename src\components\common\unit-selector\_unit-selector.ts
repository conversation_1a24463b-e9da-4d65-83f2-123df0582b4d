// @vue/component
export default {
  name: 'UnitSelector',

  components: {},

  mixins: [],

  model: {
    prop: 'value',
    event: 'change',
  },

  props: {
    units: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Object,
      default: null,
    },
    position: {
      type: String,
      default: 'bottom',
    },
    visibleArrow: {
      type: Boolean,
      default: true,
    },
    noPadding: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      visible: false,
    };
  },

  computed: {
    visibleUnits() {
      const me = this as any;
      return me.units.filter((item: any) => !item.is_hidden);
    },
    baseUnitName() {
      const me = this as any;
      const baseUnit = me.visibleUnits.find((item: any) => item.is_base);
      return baseUnit ? baseUnit.name : '';
    },
  },

  watch: {},

  created() {},

  methods: {
    onSelect(unit: any) {
      const me = this as any;
      me.visible = false;
      me.$emit('change', unit);
    },
  },
};
