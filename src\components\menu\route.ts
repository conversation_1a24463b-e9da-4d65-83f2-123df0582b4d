/**
 * 路由配置文件
 */
const pageData = {
  index: {
    text: $t('首页'),
    hash: 'index',
    icon: 'ico-index',
  },

  recentUsed: {
    text: $t('最近使用'),
    hash: '',
    icon: 'ico-recentlyapplied',
    key: 'recent',
    searchWords: ['最近使用', 'zuijinshiyon', 'zjsy'],
  },

  crmRemind: {
    text: $t('CRM提醒'),
    hash: 'remind/weakremind',
    icon: 'ico-remind',
    // isRemind: true,
    key: 'remind',
    searchWords: ['CRM提醒', 'crmtixing', 'tx'],
  },

  crmTodo: {
    text: $t('CRM待办'),
    hash: 'remind/approvalbyme',
    icon: 'ico-todo',
    isHidden: true,
    key: 'todo',
    searchWords: ['CRM待办', 'crmdaiban', 'db'],
  },

  DataBoard: {
    hash: 'bi/dashboard',
    icon: 'ico-data',
    isBi: true,
  },

  Report: {
    hash: 'bi/list',
    icon: 'ico-report',
    isBi: true,
  },

  ReportPermissionMgr: {
    hash: 'bi/authority',
    icon: 'ico-authority',
    isBi: true,
  },

  SubscMgr: {
    hash: 'bi/subscription',
    icon: 'ico-bisub',
    isBi: true,
  },

  ReportLog: {
    hash: 'bi/log',
    icon: 'ico-bilog',
    isBi: true,
  },

  StatThemeMgr: {
    hash: 'bi/theme',
    icon: 'ico-bitheme',
    isBi: true,
  },

  CrmInfo: {
    hash: 'info',
    icon: 'ico-info',
  },

  GoalBoard: {
    hash: 'bi/goalboard',
    icon: 'ico-goalboard',
  },

  BpmInstance: {
    hash: 'opeflow',
    icon: 'ico-flow',
  },

  ApprovalMonitor: {
    hash: 'approvalflowmonitor',
    icon: 'ico-flow',
  },

  CrmServiceManager: {
    spell: 'fuwuguanli',
    hash: 'service',
    icon: 'ico-service',
    log: '10004',
    checkedProp: 'IsShowServiceManage',
  },

  InventoryReport: {
    hash: 'stockbi',
    icon: 'ico-inventoryreport',
  },
};

const remindData = [
  {
    name: $t('CRM通知'),
    key: 'weakRemind',
    isOld: true,
  },
  // {
  //     name: $t('库存补货与预警'),
  //     key: 'stockwarn',
  //     isOld: true
  // },
  // {
  //     name: $t('临到期的库存'),
  //     key: 'stockmature',
  //     isOld: true
  // },
  // {
  //     name: $t('发起的审批流程'),
  //     key: 'sentbyme',
  //     isOld: true
  // },
  // {
  //     name: $t('审批消息'),
  //     key: 'approvalPush',
  //     isOld: true
  // },
  // {
  //     name: $t('工作流消息'),
  //     key: 'workflowInfo',
  //     isOld: true
  // },
  // {
  //     name: $t('导入导出消息'),
  //     key: 'importhelper',
  //     isOld: true
  // },
  // {
  //     name: $t('@我的'),
  //     key: 'atmefeeds',
  //     isOld: true
  // },
  // {
  //     name: $t('我的赞'),
  //     key: 'likefeeds',
  //     isOld: true
  // }
];

const todoData = [
  {
    name: $t('待处理的审批流程'),
    key: 'approvalByMe',
    isOld: true,
  },
  {
    name: $t('crm.待处理的业务流程'),
    key: 'flowHandle',
    isOld: true,
  },
  {
    name: $t('待处理的阶段任务'),
    key: 'saleaction2',
    isOld: true,
  },
  {
    name: $t('crm.待审核的客户报备'),
    key: 'customerCheck',
  },
  {
    name: $t('crm.待确认的销售订单'),
    key: 'leaderConfirm',
  },
  {
    name: $t('待发货的销售订单'),
    key: 'deliveryTobe',
  },
  {
    name: $t('待确认的退货单'),
    key: 'returnConfirm',
  },
  {
    name: $t('待确认的退款'),
    key: 'gbConfirm',
  },
  {
    name: $t('待确认的开票申请'),
    key: 'invoiceConfirm',
  },
  {
    name: $t('待确认的销售阶段'),
    key: 'saleConfirm',
  },
  {
    name: $t('待分配的销售线索'),
    key: 'clueAssign',
  },
  {
    name: $t('待处理的销售线索'),
    key: 'clueHandle',
  },
];

export default {
  pageData,
  todoData,
  remindData,
};
