/**
 * 该文件内容只处理订货通独立频道方案的初始化逻辑
 */
import { api, ApiContext } from '../api';

const apiContext: ApiContext = {
  appId: '',
  coverHeaders: false,
};

/**
 * update
 * @param context
 */
function updateApiContext<T extends ApiContext>(context: T) {
  // 清空所有组件的生命周期
  Fx.http.deletesend('lifeCycleConnectApi');
  apiContext.appId = context.appId;
  apiContext.coverHeaders = !!context.coverHeaders;
}

function initLifeCycleApi() {
  // 更新上下文
  updateApiContext({
    appId: window.$dht.appId,
  });
  // 注册初始化钩子
  Fx.install({
    router: false,
    http: {
      parseParams: function l(options: any) {
        const customHeaders = {
          'x-out-link-type': 1,
          'fs-out-appid': apiContext.appId,
        };
        const headers = apiContext.coverHeaders
          ? { ...options.headers, ...customHeaders }
          : Object.assign(customHeaders, options.headers || {});

        return Object.assign(options, {
          url: options.url.replace('EM1', 'EM6'),
          headers,
        });
      },
    },
  });
}

/**
 * 打各种补丁
 */
export function init() {
  window.$dht.updateApiContext = updateApiContext;

  // 手动处理动态添加的元素，然后之后再dom挂载
  $('#app-portal').append($("<div class='hd f-g-hd'></div>"));

  initLifeCycleApi();
}

/**
 * 互联身份换取，理论上来说进入订货通都是已经保证互联身份不需要再登录的，这里临时做个兼容
 *
 * @param params 互联登录参数
 */
export function erlogin(params: { appId: string; upstreamEa: string; }): Promise<any> {
  return new Promise((resolve, reject) => {
    CRM.util.regConnectApp({
      urls(url: string) {
        return true;
      },
      success(res: any) {
        resolve(res);
      },
      error(err: any) {
        reject(err);
      },
      appId: params.appId,
      upstreamEa: params.upstreamEa,
    });
  });
}

interface PortalEnv {
  /**
   * 是否有无租户
   */
  hasTenants: boolean;

  activedTpl: number | string;

  portalTpls: any[];
}

class EnvProvider<T extends PortalEnv> {
  options: any;

  constructor(options?: any) {
    this.options = options;
  }

  get(key: keyof T) {
    return window.$dht.env[key];
  }

  set(key: keyof T, value: any) {
    window.$dht.env[key] = value;
  }
}

/**
 * 获取应用门户配置信息
 *
 * @param params 门户请求参数
 */
export function getPortalTpls(params: { appId: string; }): Promise<void> {
  return api
    .getPortalTpls({ appId: params.appId })
    .then(({ userWebPageTemplateList }) => {
      const env: PortalEnv = {
        hasTenants: false,
        activedTpl: 0,
        portalTpls: userWebPageTemplateList || [],
      };
      window.$dht.env = env;
      window.$dht.envProvider = new EnvProvider();
    });
}

/**
 * 获取下游身份信息
 */
export function getDownstreamInfo() {
  return api
    .getDownstreamEmployeeInfo().then((res) => {
      window.$dht.downstream = res;
    });
}
