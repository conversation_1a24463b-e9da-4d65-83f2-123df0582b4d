{"name": "@beecraft/examples", "version": "2.1.83", "description": "", "main": "index.js", "scripts": {"start": "vite", "build": "vite build", "clean-npm": "node ./scripts/clean-npm.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@beecraft/core": "workspace:~", "@beecraft/engine": "workspace:~", "@beecraft/examples": "workspace:^", "@beecraft/fxui": "workspace:~", "@beecraft/materials": "workspace:~", "@beecraft/setters": "workspace:~", "@beecraft/shared": "workspace:~", "@beecraft/workbench": "workspace:~", "vue-router": "3.6.5"}}