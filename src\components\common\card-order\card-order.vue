<template>
    <div class="dht-card-order">
      <fx-popover
        placement="bottom"
        trigger="hover"
        :visible-arrow="false"
        :width="170">
        <div class="dht-card-order-input" slot="reference">
          <div class="dht-card-order-input-select">
            <div class="dht-card-order-item" v-if="!selectItem">{{ $t('默认排序') }}</div>
            <card-order-item
              v-else
              :item="selectItem"
              @click.native="selectChange(selectItem)"
            ></card-order-item>
          </div>
          <div class="dht-card-order-input-pull">
            <i class="el-icon-arrow-down"></i>
          </div>
        </div>
        <div class="dht-card-order-list">
          <div class="dht-card-order-item" @click="selectDefaultOrder">{{ $t('默认排序') }}</div>
          <card-order-item
            v-for="(item, index) in items"
            :item="item"
            :key="index"
            @click.native="selectChange(item)">
          </card-order-item>
        </div>
      </fx-popover>

    </div>
</template>

<script src="./_card-order.ts" lang="ts"></script>
<style src="./_card-order.less" lang="less"></style>
