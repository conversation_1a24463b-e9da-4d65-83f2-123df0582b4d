export const USER_CONFIG_KEYS = {
  RECORD_TYPE: 'dht_sales_order_record_type',
  AVAILABLE_RANGE: 'available_range_filter_condition',
  MULTI_LEVEL_ORDER: 'dht_multi_level_order_select_partner_or_org', // 供货类型_合作伙伴id/部门id
};

// 分隔符
export const SEPARATOR = '_';

export const DISTRIBUTION = 'distribution__c';

export function getVcrmComponents(services: string[], widgets: string[]): Promise<any> {
  return new Promise((resolve) => {
    Fx.async(['vcrm/sdk'], (vcrmSdk: any) => {
      const promiseList: Promise<any>[] = [];
      services.forEach((item: string) => {
        promiseList.push(vcrmSdk.widgetService.getService(item));
      });
      widgets.forEach((item: string) => {
        promiseList.push(vcrmSdk.widgetService.getWidget(item));
      });
      Promise.all(promiseList).then((result: any[]) => {
        resolve(result);
      });
    });
  });
}

export function getUserConfigByKey(configs: any[] = [], key: string = '') {
  return configs.find((item: any) => item.key === key);
}
