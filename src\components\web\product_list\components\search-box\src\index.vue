<template>
  <div class="dhtbiz-search-box">
    <div class="search-container">
      <fx-input
        v-model="searchKeyword"
        :placeholder="placeholder"
        :size="size"
        clearable
        @keyup.enter="handleSearch"
        @clear="handleClear">
        <template #prefix>
          <fx-icon name="search"></fx-icon>
        </template>
        <template #suffix>
          <fx-button 
            type="primary" 
            :size="size"
            @click="handleSearch"
            :loading="isSearching">
            搜索
          </fx-button>
        </template>
      </fx-input>
    </div>
    
    <!-- 搜索历史 -->
    <div v-if="showHistory && searchHistory.length > 0" class="search-history">
      <div class="history-header">
        <span class="history-title">搜索历史</span>
        <fx-button type="text" size="small" @click="clearHistory">清空</fx-button>
      </div>
      <div class="history-tags">
        <fx-tag
          v-for="(item, index) in searchHistory"
          :key="index"
          :closable="true"
          @click="handleHistoryClick(item)"
          @close="removeHistoryItem(index)">
          {{ item }}
        </fx-tag>
      </div>
    </div>
    
    <!-- 热门搜索 -->
    <div v-if="showHotSearch && hotSearchList.length > 0" class="hot-search">
      <div class="hot-header">
        <span class="hot-title">热门搜索</span>
      </div>
      <div class="hot-tags">
        <fx-tag
          v-for="(item, index) in hotSearchList"
          :key="index"
          :type="index < 3 ? 'danger' : 'default'"
          @click="handleHotSearchClick(item)">
          {{ item }}
        </fx-tag>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'dht_web_search_box',
  props: {
    // 从容器组件传入的事件总线
    eventBus: {
      type: Object,
      required: true
    },
    // 从容器组件传入的业务数据
    bizPageData: {
      type: Object,
      required: true
    },
    // 从容器组件传入的方法
    containerMethods: {
      type: Object,
      required: true
    },
    // 搜索框占位符
    placeholder: {
      type: String,
      default: '请输入商品名称'
    },
    // 搜索框大小
    size: {
      type: String,
      default: 'default'
    },
    // 是否显示搜索历史
    showHistory: {
      type: Boolean,
      default: true
    },
    // 是否显示热门搜索
    showHotSearch: {
      type: Boolean,
      default: true
    },
    // 最大历史记录数
    maxHistoryCount: {
      type: Number,
      default: 10
    }
  },
  
  data() {
    return {
      // 搜索关键字
      searchKeyword: '',
      // 是否正在搜索
      isSearching: false,
      // 搜索历史
      searchHistory: [],
      // 热门搜索列表
      hotSearchList: []
    };
  },
  
  computed: {
    // 当前搜索数据（从bizPageData中获取）
    searchData() {
      return this.bizPageData.search;
    },
    
    // 事件类型常量
    EVENT_TYPES() {
      return this.containerMethods.getEventTypes();
    }
  },
  
  watch: {
    // 监听bizPageData中的搜索关键字变化
    'searchData.keyword'(newKeyword) {
      if (newKeyword !== this.searchKeyword) {
        this.searchKeyword = newKeyword;
      }
    }
  },
  
  created() {
    this.initEventListeners();
    this.initSearchData();
    this.loadHotSearchList();
    this.loadSearchHistory();
  },
  
  beforeDestroy() {
    this.removeEventListeners();
  },
  
  methods: {
    /**
     * 初始化事件监听
     */
    initEventListeners() {
      // 监听搜索重置事件
      this.eventBus.$on(this.EVENT_TYPES.SEARCH_RESET, this.handleSearchReset);
    },
    
    /**
     * 移除事件监听
     */
    removeEventListeners() {
      this.eventBus.$off(this.EVENT_TYPES.SEARCH_RESET, this.handleSearchReset);
    },
    
    /**
     * 初始化搜索数据
     */
    initSearchData() {
      // 从bizPageData中获取初始搜索关键字
      this.searchKeyword = this.searchData.keyword || '';
    },
    
    /**
     * 加载热门搜索列表
     */
    async loadHotSearchList() {
      try {
        // 这里应该调用实际的热门搜索API
        // const response = await this.callHotSearchApi();
        
        // 模拟热门搜索数据
        this.hotSearchList = [
          'iPhone', '华为', '小米', '笔记本', '耳机',
          '键盘', '鼠标', '显示器', '充电器', '数据线'
        ];
      } catch (error) {
        console.error('SearchBox: 加载热门搜索失败', error);
      }
    },
    
    /**
     * 加载搜索历史
     */
    loadSearchHistory() {
      try {
        const history = localStorage.getItem('product_search_history');
        if (history) {
          this.searchHistory = JSON.parse(history);
        }
      } catch (error) {
        console.error('SearchBox: 加载搜索历史失败', error);
        this.searchHistory = [];
      }
    },
    
    /**
     * 保存搜索历史
     */
    saveSearchHistory() {
      try {
        localStorage.setItem('product_search_history', JSON.stringify(this.searchHistory));
      } catch (error) {
        console.error('SearchBox: 保存搜索历史失败', error);
      }
    },
    
    /**
     * 处理搜索
     */
    handleSearch() {
      const keyword = this.searchKeyword.trim();
      
      if (!keyword) {
        this.$message.warning('请输入搜索关键字');
        return;
      }
      
      console.log('SearchBox: 执行搜索', keyword);
      
      // 添加到搜索历史
      this.addToHistory(keyword);
      
      // 触发搜索变更事件
      this.eventBus.$emit(this.EVENT_TYPES.SEARCH_CHANGE, {
        keyword: keyword
      });
    },
    
    /**
     * 处理清空
     */
    handleClear() {
      console.log('SearchBox: 清空搜索');
      this.searchKeyword = '';
      
      // 触发搜索变更事件
      this.eventBus.$emit(this.EVENT_TYPES.SEARCH_CHANGE, {
        keyword: ''
      });
    },
    
    /**
     * 处理搜索重置事件
     */
    handleSearchReset() {
      console.log('SearchBox: 接收到搜索重置事件');
      this.searchKeyword = '';
    },
    
    /**
     * 处理历史记录点击
     * @param {String} keyword - 搜索关键字
     */
    handleHistoryClick(keyword) {
      console.log('SearchBox: 历史记录点击', keyword);
      this.searchKeyword = keyword;
      this.handleSearch();
    },
    
    /**
     * 处理热门搜索点击
     * @param {String} keyword - 搜索关键字
     */
    handleHotSearchClick(keyword) {
      console.log('SearchBox: 热门搜索点击', keyword);
      this.searchKeyword = keyword;
      this.handleSearch();
    },
    
    /**
     * 添加到搜索历史
     * @param {String} keyword - 搜索关键字
     */
    addToHistory(keyword) {
      // 移除已存在的相同关键字
      const index = this.searchHistory.indexOf(keyword);
      if (index > -1) {
        this.searchHistory.splice(index, 1);
      }
      
      // 添加到开头
      this.searchHistory.unshift(keyword);
      
      // 限制历史记录数量
      if (this.searchHistory.length > this.maxHistoryCount) {
        this.searchHistory = this.searchHistory.slice(0, this.maxHistoryCount);
      }
      
      // 保存到本地存储
      this.saveSearchHistory();
    },
    
    /**
     * 移除历史记录项
     * @param {Number} index - 索引
     */
    removeHistoryItem(index) {
      this.searchHistory.splice(index, 1);
      this.saveSearchHistory();
    },
    
    /**
     * 清空搜索历史
     */
    clearHistory() {
      this.searchHistory = [];
      this.saveSearchHistory();
    }
  }
};
</script>

<style lang="less" scoped>
.dhtbiz-search-box {
  width: 100%;
  
  // 搜索容器
  .search-container {
    margin-bottom: 16px;
  }
  
  // 搜索历史
  .search-history {
    margin-bottom: 16px;
    
    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .history-title {
        font-size: 14px;
        color: #666;
      }
    }
    
    .history-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .fx-tag {
        cursor: pointer;
        
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
  
  // 热门搜索
  .hot-search {
    .hot-header {
      margin-bottom: 8px;
      
      .hot-title {
        font-size: 14px;
        color: #666;
      }
    }
    
    .hot-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .fx-tag {
        cursor: pointer;
        
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dhtbiz-search-box {
    .history-tags,
    .hot-tags {
      gap: 6px;
    }
  }
}
</style>

