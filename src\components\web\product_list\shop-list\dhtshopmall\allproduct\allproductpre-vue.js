define(function(require, exports, module) {
const ShopMallMixins = require('../shopmall-mixins');
module.exports = Vue.extend({
    name: "ShopMallProductPre",
    template: '<div class="dht-shopmall-allproduct-preview" v-loading="isLoading"><div class="content-list" v-if="view_mode === \'list\'"><div class="list-item" :class="{active: index === 1}" v-for="index in 10" :key="index"><p class="list-item-td" v-for="index in 5" :key="index"></p></div></div><div class="content-card" v-else-if="view_mode === \'card\'"><div class="card-item" v-for="index in 10" :key="index"><p class="card-item-img" v-show="isShowImg"></p><p class="card-item-title" v-show="isShowName">{{ $t(\'dht.shopmall_widget.spu_sku_name\') }} ({{ $t(\'dht.shopmall_widget.example\')}})</p><p class="card-item-price" v-show="isShowPrice">¥88.88 ({{ $t(\'dht.shopmall_widget.example\')}})</p><p class="card-item-field" v-for="item in showFields" :key="item">{{ formatShowField(item) }}</p><p class="card-item-tags" v-show="isShowTag"><span class="card-item-tag">{{$t(\'dht.shopmall_widget.tag_area\')}}</span><span class="card-item-tag">{{$t(\'dht.shopmall_widget.tag_area\')}}</span></p><span class="card-item-tag card-item-tag_top" v-show="isShowTag &amp;&amp; tagVal">{{ tagVal }}</span></div></div></div>',
    mixins: [ShopMallMixins],
    props: {
        api_name: String,
        view_mode: String,
        is_card_init: Boolean,
        card_main_info: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            isLoading: true,
        }
    },
    computed: {
        isShowImg() {
            return this.is_card_init || this.card_main_info.picture_apiname;
        },
        isShowName() {
            return this.is_card_init || this.card_main_info.name_apiname;
        },
        isShowPrice() {
            return this.is_card_init || this.card_main_info.price_apiname;
        },
        isShowTag() {
            return this.is_card_init || this.card_main_info.is_tag_show;
        },
        tagVal() {
            let field = this.allFields.commodity_label;
            let options = field && field.options || [];
            let target = _.find(options, a => {
                return a.value === this.card_main_info.tag_apiname
            });
            return target && target.label || '';
        },
        showFields() {
            return this.is_card_init
                    ? ['unit', 'virtual_available_stock']
                    : this.card_main_info.show_fields || [];
        }
    },
    watch: {
        // card_main_info: {
        //     handle() {},
        //     deep: true
        // }
    },
    created() {
        this.initFetch();
    },
    methods: {
        render() {
            this.isLoading = false;
        },
        formatShowField(apiname) {
            let field = this.allFields[apiname];
            if (!field) return;
            let text = '';
            switch (field.type) {
                case 'currency': text = '¥88.88'; break;
                case 'number', 'count': text = '99999'; break;
                case 'date': text = 'YYYY-MM-DD'; break;
                case 'date_time', 'time': text = 'YYYY-MM-DD mm:ss'; break;
                default: text = $t('文本'); break;
            }
            return `${field.label}: ${text} (${ $t('dht.shopmall_widget.example')})`;
        }
    }
})
});