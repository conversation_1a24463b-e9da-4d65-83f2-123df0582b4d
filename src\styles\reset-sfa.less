// 重置SFA选规则组件样式
.SPEC_ITEM {
  height: 22px;
  line-height: 22px;
  background-color: #fff;
  border: 1px solid #E5E5E5;
  border-radius: 2px;

  &.active {
    border-color: var(--color-primary06, #ff8000);
  }

  &.disabled {
    background-color: #F8F8F8;
    color: #C1C5CE;
  }
}

.SPEC_ROW_DETAIL {
  padding: 0;
  border-bottom: none;
}

.SPEC_ITEM_DETAIL {
  margin: 0 8px 12px 0;
}

.SPEC_HEIGHT {
  height: 24px;
  line-height: 24px;
}

@LABEL_COLOR: #91959E;

.crm-multispec-picksku-dialog.crm-c-dialog {
  .crm-c-dialog-content {

    .dialog-con {
      padding-top: 0;
    }
  }
}
.dht-pick-sku.crm-comp-dketable {
  font-size: 12px;

  > .loading-wrapper {
    bottom: 50px;
  }

  .tit {
    display: none;
  }

  .cp-options-container {
    .cp-options {
      // 单选
      > .spec-single-comp {
        > .spec-single-label {
          line-height: 22px;
        }

        > .spec-single-values {
          > .btn-spec-item {
            .SPEC_ITEM();
          }
        }
      }

      // 多选
      > .cpopt-wrapper {
        > .cpopt-select-wrapper {
          .SPEC_HEIGHT();

          > .spec-label {
            font-size: 12px;
            .SPEC_HEIGHT();

            > span {
              .SPEC_HEIGHT();
            }

            > .require {
              display: none;
            }
          }
        }

        > .cpopt-options {
          > .cpopt-area  {
            > .cpopt-item {
              .SPEC_ITEM();
            }
          }
        }
      }
    }

    > .spec-toggle-btn {
      padding: 2px 8px;
      color: @LABEL_COLOR;
      border-radius: 0 0 4px 4px;
      background-color: #EEF0F3;
      overflow: hidden;
      cursor: pointer;

      > .fx-icon-arrow-up,
      > .fx-icon-arrow-down {
        margin-left: 5px;
        color: @LABEL_COLOR;
      }
    }
  }
}

// 详情页重置SFA选规则组件样式
.dht-product-detail {
  .dht-pick-sku.crm-comp-dketable {

    .cp-options-container {
      position: relative;
      padding: 16px 16px 6px;
      margin: 16px 0 46px;
      border: 1px solid #e5e5e5;

      > .spec-toggle-btn {
        position: absolute;
        bottom: -23px;
      }
    }

    .md-handle,
    .f-group-label {
      display: none;
    }

    .crm-table th {
      border-top: none;
    }

    .cp-options {
      // 单选
      > .spec-single-comp {
        .SPEC_ROW_DETAIL();

        > .spec-single-label {
          color: @LABEL_COLOR;
        }

        > .spec-single-values {
          > .btn-spec-item {
            .SPEC_ITEM_DETAIL();
          }
        }
      }

      // 多选
      > .cpopt-wrapper {
        .SPEC_ROW_DETAIL();

        > .cpopt-select-wrapper {
          > .spec-label {
            > .name {
              color: @LABEL_COLOR;
            }
          }
        }

        > .cpopt-options {
          > .cpopt-area  {
            > .cpopt-item {
              .SPEC_ITEM_DETAIL();
            }
          }

        }
      }
    }
  }
}
