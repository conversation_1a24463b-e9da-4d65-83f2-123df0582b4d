/**
 * 给按钮文字前面加loading
 * @param $btn 需要加loading的按钮
 */
const showLoaingByBtn = ($btn: any) => {
  const stext = $btn.text();
  $btn.html(`<span class="crm-action-icon-requesting"></span> ${stext}`);
};

/**
 * 给按钮文字前面去掉loading
 * @param $btn 需要取消loading的按钮
 */
const hideLoaingByBtn = ($btn: any) => {
  $btn.children('.crm-action-icon-requesting').remove();
};

export default {
  showLoaingByBtn,
  hideLoaingByBtn,
};
