define(function (require, exports, module) {
    const crmUtil = CRM.util;
    const dhtChooseSkuKey = 'dht_choose_sku';

    const mixins = {
        data () {
            return {
                allFields: {},
                // 订货管理开启适配商品选产品
                dhtChooseSku: true,
            }
        },
        computed: {
            // 当前企业的是 sku?spu
            isSpuMode() {
                // productOpenSpu：是否开启spu
                // spuStatus：基于商品选择产品
                const { productOpenSpu, spuStatus } = CRM._cache;
                // 开启适配商品选产品，则判断商品选产品；
                // 未开启适配商品选产品，则不判断是否开启商品选产品，直接显示商品列表
                const isSpuChooseSku = !this.dhtChooseSku || spuStatus;
                // 是否开启商品模式，并开启商品选产品
                const isSpuMode = productOpenSpu && isSpuChooseSku;
                return isSpuMode;
            },
            productApiName() {
                return this.isSpuMode ? 'SPUObj' : 'ProductObj';
            },
        },
        methods: {
            async initFetch() {
                const config = await this.fetchConfigValue();
                this.dhtChooseSku = config[dhtChooseSkuKey] === '2';

                const allFields = await this.fetchAllFields();
                this.allFields = allFields.fields;
                this.render && this.render();
            },

            fetchAllFields() {
                const apiName = this.productApiName;
                return new Promise((resolve,reject) => {
                    crmUtil.FHHApi({
                        url: '/EM1HNCRM/API/v1/object/describe/service/findDescribeByApiName',
                        data: {
                            describe_apiname: apiName,
                            include_layout: false,
                            include_related_list: false,
                            get_label_direct: true,
                            includeDescribeExtra: false // 是否包含扩展字段
                        },
                        success: (res) => {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value.objectDescribe);
                            } else {
                                reject();
                            }
                        },
                        complete: () => {}
                    });
                });
            },

            fetchConfigValue() {
                const keys = [dhtChooseSkuKey];
                return new Promise((resolve, reject) => {
                    crmUtil.FHHApi({
                        url: '/EM1HDHT/API/v1/object/dht_config/service/get_config_values',
                        data: {
                            isAllConfig: false,
                            keys: keys
                        },
                        success: res => {
                            if (res.Result.StatusCode === 0) {
                                const list = res.Value.values || [];
                                const obj = {};

                                // 将数组转换成对象
                                list.forEach(({ key, value }) => {
                                    obj[key] = value;
                                });

                                resolve(obj);
                            } else {
                                crmUtil.error(res.Result.FailureMessage);
                                reject();
                            }
                        }
                    });
                });
            },
        }
    };
    module.exports = mixins;
});
