<template>
  <div v-if="ready" class="dhtbiz-container-product-detail" :style="{ height: isPreview ? '700px' : 'auto' }">
    <div v-if="isPreview">
      product detail container running
    </div>
    
    <slot
      :eventBus="eventBus"
      :dhtPageData="dhtPageData"
      :dhtContainerApi="dhtContainerApi"
    ></slot>
  </div>
</template>

<script>
import { loadnsail } from '@/utils/load';
// import {loadCss, loadFxModule } from '@utils/load';

// 事件类型常量
const DHT_PAGE_EVENT_TYPES = {};

export default {
  inject: ['useInternalEditor', 'useInternalNode'],
  name: 'dht_web_container_product_detail',
  props: { 
  },

  data() {
    return {
      isPreview: false,  // 是否处于设计器预览模式下
      ready: false,
      // 事件总线
      eventBus: new Vue(),

      // 业务页面数据
      dhtPageData: {
        
      }
    };
  },

  computed: {
    // 容器方法，供子组件调用
    dhtContainerApi() {
      return {
        // 暴露必要的函数, 如 加入购物车, 选规格, 刷新票数等
        // $dht: window.$dht,
        // 获取事件类型常量
        getEventTypes: () => DHT_PAGE_EVENT_TYPES
      };
    }
  }, 

  created() {
    this.checkIsPreview();
    
    // 设计器预览模式, 直接进入
    if(this.isPreview) {
      this.ready = true;
      return;
    }

    // 确保初始化nsail
    if(!window.$dht) {
      loadnsail().then(() => {
        this.ready = true;
      });
    } else {
      this.ready = true;
    }
  },
  mounted() {
    this.setContainerData();
  },

  beforeDestroy() {
    // 清理事件监听
    this.eventBus?.$destroy?.();
  },

  methods: {
    // 检查是否处于设计器预览模式下
    checkIsPreview() {
      const { query } = this.useInternalEditor();
      const { enabled } = query.getOptions();
      this.isPreview = enabled;
    },


    setContainerData() {
      const { actions } = this.useInternalEditor();
      const { id } = this.useInternalNode();
      actions.history.ignore().setProp(id, ($$data) => {
          $$data.contextNode.data.dhtPageData = this.dhtPageData;
          $$data.contextNode.data.dhtContainerApi = this.dhtContainerApi;
      });
    },

    // 设置dhtPageData  
    setDhtPageData(data, key, merge = true) {
      console.log('Container: 设置dhtPageData', data, key);

      // 如果 key 为空，则设置整个 dhtPageData
      if (!key) {
        this.dhtPageData = merge ? { ...this.dhtPageData, ...data } : { ...data };
        const { id } = this.useInternalNode();
        const { actions } = this.useInternalEditor();
        actions.history.ignore().setProp(id, ($$data) => {
            $$data.contextNode.data.dhtPageData = this.dhtPageData;
        });
        return;
      }

      // 默认合并更新
      const setData = {
        ...(merge ? this.dhtPageData[key] : {}),
        ...data
      };
      console.log('Container: 设置dhtPageData', setData);
      this.dhtPageData[key] = setData;
      const { id } = this.useInternalNode();
      const { actions } = this.useInternalEditor();
      actions.history.ignore().setProp(id, ($$data) => {
          $$data.contextNode.data.dhtPageData[key] = setData;
      });
    },

    
  }
};
</script>


<style lang="less" scoped>
.dht-container-product-detail {
  width: 100%;
}
</style>
