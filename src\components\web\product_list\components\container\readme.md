# 商品列表容器组件 API 文档

## 概述

商品列表容器组件是一个为beecraft低代码设计器优化的容器组件，提供统一的事件总线和数据管理机制。容器组件负责协调各个子组件之间的数据流和事件通信，确保子组件之间的解耦和数据一致性。

## 组件特性

- ✅ 统一的事件总线管理
- ✅ 响应式数据对象
- ✅ 子组件解耦设计
- ✅ 支持第三方组件扩展
- ✅ beecraft设计器友好
- ✅ 完整的错误处理
- ✅ 响应式设计支持

## Props 配置

### 容器组件 Props

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| initialCategoryId | String/Number | null | 初始分类ID |
| initialKeyword | String | '' | 初始搜索关键字 |
| apiConfig | Object | 见下方 | API配置对象 |
| pageConfig | Object | 见下方 | 分页配置对象 |

#### apiConfig 默认值
```javascript
{
  listApi: 'ShopMall',      // 商品列表API
  categoryApi: 'Category',   // 分类API
  searchApi: 'Search'        // 搜索API
}
```

#### pageConfig 默认值
```javascript
{
  pageSize: 20,                           // 默认分页大小
  showSizeChanger: true,                  // 显示分页大小选择器
  pageSizeOptions: ['10', '20', '50', '100'] // 分页大小选项
}
```

### 子组件通用 Props

每个子组件都会接收到以下props：

| 参数名 | 类型 | 说明 |
|--------|------|------|
| eventBus | Vue实例 | 事件总线对象 |
| bizPageData | Object | 业务页面数据对象 |
| containerMethods | Object | 容器提供的方法集合 |

## 数据结构

### bizPageData 完整结构

```javascript
{
  // 分类信息
  category: {
    id: null,           // 当前选中分类ID
    name: '',           // 分类名称
    path: []            // 分类路径，数组格式：[{id, name}, ...]
  },

  // 搜索信息
  search: {
    keyword: '',        // 搜索关键字
    timestamp: 0        // 搜索时间戳，用于区分搜索操作
  },

  // 筛选条件
  filters: {
    price: {            // 价格筛选
      min: null,        // 最低价格
      max: null         // 最高价格
    },
    brand: [],          // 品牌筛选数组
    attributes: {},     // 商品属性筛选
    custom: {}          // 自定义筛选条件
  },

  // 列表状态
  list: {
    loading: false,     // 是否正在加载
    data: [],           // 商品列表数据
    total: 0,           // 总商品数量
    page: 1,            // 当前页码
    pageSize: 20,       // 每页显示数量
    error: null         // 错误信息
  },

  
}
```

## 事件系统

### 事件类型常量

```javascript
const EVENT_TYPES = {
  // 分类相关事件
  CATEGORY_CHANGE: 'category:change',     // 分类变更

  // 搜索相关事件
  SEARCH_CHANGE: 'search:change',         // 搜索变更

  // 筛选相关事件
  FILTER_CHANGE: 'filter:change',         // 筛选变更

  // 列表相关事件
  LIST_REFRESH: 'list:refresh',           // 列表刷新
  LIST_LOADING: 'list:loading',           // 加载状态变更
  LIST_ERROR: 'list:error',               // 列表错误
  LIST_DATA_UPDATE: 'list:data:update',   // 列表数据更新

  // 全局事件
  GLOBAL_RESET: 'global:reset'            // 全局重置
}
```

### 事件详细说明

#### CATEGORY_CHANGE
**触发时机：** 用户选择分类时
**数据格式：**
```javascript
{
  id: 123,                    // 分类ID
  name: '电子产品',           // 分类名称
  path: [                     // 分类路径
    { id: 1, name: '数码' },
    { id: 123, name: '电子产品' }
  ]
}
```

#### SEARCH_CHANGE
**触发时机：** 用户执行搜索时
**数据格式：**
```javascript
{
  keyword: 'iPhone'           // 搜索关键字
}
```

#### FILTER_CHANGE
**触发时机：** 用户修改筛选条件时
**数据格式：**
```javascript
{
  price: { min: 100, max: 1000 },  // 价格范围
  brand: ['Apple', 'Samsung'],     // 品牌筛选
  attributes: {                    // 属性筛选
    color: ['红色', '蓝色'],
    size: ['大', '中']
  }
}
```

#### LIST_REFRESH
**触发时机：** 需要刷新列表时
**数据格式：**
```javascript
{
  page: 1,                    // 目标页码（可选）
  pageSize: 20                // 分页大小（可选）
}
```

#### LIST_DATA_UPDATE
**触发时机：** 列表数据更新后
**数据格式：**
```javascript
{
  data: [...],                // 商品列表数据
  total: 100,                 // 总数量
  page: 1,                    // 当前页
  pageSize: 20                // 分页大小
}
```

## 容器方法

### containerMethods 对象

```javascript
{
  // 获取当前查询参数
  getQueryParams: Function,


  // 刷新列表
  refreshList: Function,

  // 获取事件类型常量
  getEventTypes: Function
}
```

### 方法详细说明

#### getQueryParams()
**返回值：** Object
**说明：** 获取当前所有查询参数，用于API调用
```javascript
{
  categoryId: 123,
  keyword: 'iPhone',
  filters: { ... },
  page: 1,
  pageSize: 20,
  sortBy: 'default'
}
```

#### resetComponentData(componentName)
**参数：**
- `componentName` (String): 组件名称 ('category' | 'search' | 'filters')

**说明：** 重置指定组件的数据到初始状态

#### refreshList()
**说明：** 手动触发列表刷新

#### getEventTypes()
**返回值：** Object
**说明：** 获取所有事件类型常量

## 子组件开发指南

### 1. 基础模板

```vue
<template>
  <div class="my-component">
    <!-- 组件内容 -->
  </div>
</template>

<script>
export default {
  name: 'MyComponent',
  props: {
    eventBus: { type: Object, required: true },
    bizPageData: { type: Object, required: true },
    containerMethods: { type: Object, required: true }
  },

  computed: {
    EVENT_TYPES() {
      return this.containerMethods.getEventTypes();
    }
  },

  created() {
    this.initEventListeners();
  },

  beforeDestroy() {
    this.removeEventListeners();
  },

  methods: {
    initEventListeners() {
      // 监听相关事件
    },

    removeEventListeners() {
      // 清理事件监听
    }
  }
}
</script>
```