export function cartModelExtend(model: any): any {
  const proto = model.prototype;
  const moment = CRM.util.moment;

  return model.extend({
    initialize() {
      // 设置描述缓存，避免再次发起请求
      this.set('cacheDescribe', arguments[0].dhtDescribe);
      proto.initialize.apply(this, arguments);
    },
    parse() {
      const obj = proto.parse.apply(this, arguments);
      // obj.data.discount = obj.data.discount || 100;
      obj.data.order_time = obj.data.order_time || moment(moment().format('YYYY-MM-DD')).unix() * 1000;
      obj.data.mc_currency = $dht.config.currency.currencyCode;
      obj.isFormLayout = false; // 强制不用新的从对象布局
      return obj;
    },
  });
}
