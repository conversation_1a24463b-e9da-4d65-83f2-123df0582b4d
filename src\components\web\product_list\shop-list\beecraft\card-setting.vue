<template>
  <div class="card-setting">
    <div class="dht-name dht-title" @click="toggleShow">
      <!-- 向下箭头 -->
      <i :class="show ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"></i>
      {{$t('dht.shopmall_widget.card_content')}}
    </div>

    <div class="dht-content" v-show="show">
      <div class="form-wrap">
        <div class="form-item">
          <span>{{$t('dht.shopmall_widget.picture')}}</span>
          <fx-select
            v-model="pictureVal"
            width="184"
            size="small"
            :options="pictureOptions"
          ></fx-select>
        </div>
        <div class="form-item">
          <span>{{$t('title')}}</span>
          <fx-select
            v-model="titleVal"
            width="184"
            size="small"
            :options="titleOptions"
          ></fx-select>
        </div>
        <div class="form-item">
          <span>{{$t('价格')}}</span>
          <fx-select
            v-model="priceVal"
            width="184"
            size="small"
            :options="priceOptions"
          ></fx-select>
        </div>
        <p class="mrb10">{{$t('dht.shopmall_widget.more_fields')}}</p>
        <div class="mrb10">
          <fx-transfer
            filterable
            is-simple
            v-model="showFields"
            :data="allShowFields"
            draggable
            target-order0="push"
            @change="fieldsChange"
          >
            <div class="right-title" slot="right-title">
              <span>{{ transferTitles[1] }}({{ showFields.length }}/{{ maxFieldsNum }})</span>
            </div>
          </fx-transfer>
        </div>
        <div class="form-item align-items-baseline" v-if="tagOptions.length">
          <span>{{$t('标签')}}</span>
          <div class="form-item_value">
            <div>
              <fx-radio v-model="isTagShow" :label="true">{{$t('显示')}}</fx-radio>
              <fx-radio v-model="isTagShow" :label="false">{{$t('隐藏')}}</fx-radio>
            </div>
            <div v-if="isTagShow">
              <p class="tag-title">{{$t('dht.shopmall_widget.top_left_tag_label')}}:</p>
              <fx-select
                v-model="tagVal"
                width="184"
                size="small"
                :multiple="true"
                :multiple-limit="3"
                :options="tagOptions"
              ></fx-select>
            </div>
          </div>
        </div>
      </div>
      <!-- end of dht-content -->
    </div>
  </div>
</template>

<script>
import { dhtBizModel } from '@/components/web/utils/model';
import { HIDE_FIELDS, SHOW_SYSTEM, HIDE_PACKAGE, HIDE_FIELD_TYPES, SAVE_KEYS } from './const';

export default {
  name: 'CardSetting',
  inject: ['useInternalNode'],

  props: {
    allFields: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      show: true,
      isCardInit: false,
      transferTitles: [$t('全部字段'), $t('显示字段')],
      maxFieldsNum: 8,
      allShowFields: [],
      showFields: [],
      pictureVal: null,
      titleVal: null,
      priceVal: null,
      tagVal: [],
      isTagShow: true,
    }
  },

  computed: {
    fieldsList() {
      return Object.values(this.allFields);
    },
    isSpuMode() {
      return dhtBizModel.isSpuMode();
    },
    pictureOptions() {
      const pictureKey = this.isSpuMode ? 'picture' : 'picture_path';
      const { [pictureKey]:picture } = this.allFields;
      const options = this.fieldsList
        .filter((field) => field.type === 'image' && field.api_name !== pictureKey)
        .map((field) => ({
          value: field.api_name,
          label: field.label,
        }));

      if (picture) {
        options.unshift({
          value: picture.api_name,
          label: picture.label,
        });
      }

      if (!this.pictureVal && options.length) {
        this.pictureVal = options[0].value;
      }
      return options;
    },
    titleOptions() {
      let { name } = this.allFields;
      const options = this.fieldsList
        .filter((field) => {
          const { define_type, type, api_name } = field;

          if (api_name === 'name') {
            return false;
          }

          if (type !== 'text') {
            return false;
          }

          if (define_type === 'system') {
            return SHOW_SYSTEM.includes(api_name);
          }

          if (define_type === 'package') {
            return !HIDE_PACKAGE.includes(api_name);
          }

          return true;
        })
        .map((field) => ({ label: field.label, value: field.api_name }));

      if (name) {
        options.unshift({
          value: name.api_name,
          label: name.label,
        });
      }

      if (!this.titleVal && options.length) {
        this.titleVal = options[0].value;
      }

      return options;
    },
    priceOptions() {
      // 价目表价格 + currency类型字段
      let { virtual_price_book_price } = this.allFields;
      const options = this.fieldsList
        .filter(
          (field) => field.type === 'currency'
        ).map(field => ({
          value: field.api_name,
          label: field.label,
        }));

      if (virtual_price_book_price) {
        options.unshift({
          value: virtual_price_book_price.api_name,
          label: virtual_price_book_price.label,
        });
      }
      // 首选价目表价格
      if (!this.priceVal && options.length) {
        this.priceVal = options[0].value;
      }

      return options;
    },
    tagOptions() {
      let field = this.allFields.commodity_label;
      const options = field && field.options || [];
      return [...options];
    },
  },

  watch: {
    allFields: {
      handler(newVal) {
        console.log('allFields changed in card-setting:', newVal);
      },
      immediate: true
    },
    pictureVal(val) {
      this.updateCardMainInfo(SAVE_KEYS.PICTURE, val);
    },
    titleVal(val) {
      this.updateCardMainInfo(SAVE_KEYS.NAME, val);
    },
    priceVal(val) {
      this.updateCardMainInfo(SAVE_KEYS.PRICE, val);
    },
    tagVal(val) {
      this.updateCardMainInfo(SAVE_KEYS.TAG, val);
    },
    isTagShow(val) {
      this.updateCardMainInfo(SAVE_KEYS.IS_TAG_SHOW, val);
    },
    showFields(val) {
      this.updateCardMainInfo(SAVE_KEYS.SHOW_FIELDS, val);
    },
  },

  methods: {
    toggleShow() {
      this.show = !this.show;
    },

    init(cardMainInfo, isCardInit) {
      this.isCardInit = isCardInit;
      this.setDefaultVal(cardMainInfo);
      this.render();

      // 确保 tagVal 有默认值
      if (!this.tagVal || !Array.isArray(this.tagVal)) {
        this.tagVal = [];
      }
    },

    setDefaultVal(cardMainInfo) {
      if (!_.isEmpty(cardMainInfo)) {
        const {
          picture_apiname,
          name_apiname,
          price_apiname,
          tag_apiname,
          is_tag_show,
          show_fields
        } = cardMainInfo;

        this.pictureVal = picture_apiname;
        this.titleVal = name_apiname;
        this.priceVal = price_apiname;

        // 兼容老企业
        if (tag_apiname !== 'null' && tag_apiname !== null && tag_apiname !== undefined) {
          this.tagVal = typeof(tag_apiname) === 'string' ? [tag_apiname] : tag_apiname;
        } else {
          this.tagVal = [];
        }

        this.isTagShow = is_tag_show;
        this.showFields = show_fields || [];
      } else {
        this.tagVal = [];
      }
    },

    render() {
      this.allShowFields = this.formatAllFields();
    },

    formatAllFields() {
      let result = [];
      _.each(this.allFields, (obj, key) => {
        // 过滤不外露的字段和不宜展示的的类型
        if (!HIDE_FIELDS.includes(key) && !HIDE_FIELD_TYPES.includes(obj.type)) {
          result.push({
            key: obj.api_name,
            label: obj.label,
            value: obj.api_name,
          });
        }
      });
      return result;
    },

    fieldsChange(val) {
      this.showFields = val;
      this.updateCardMainInfo(SAVE_KEYS.SHOW_FIELDS, val);
    },

    updateCardMainInfo(key, val) {
      const { actions } = this.useInternalNode();
      actions.setCustom((data) => {
        if (!data.card_main_info) {
          data.card_main_info = {};
        }
        data.card_main_info[key] = val;
      });
    },

    updateProps(key, val) {
      const { actions } = this.useInternalNode();
      actions.setCustom((data) => {
        data[key] = val;
      });
    },

    resetVal() {
      // 重置卡片设置
      this.pictureVal = null;
      this.titleVal = null;
      this.priceVal = null;
      this.tagVal = [];
      this.isTagShow = true;
      this.showFields = [];
    }
  }
}
</script>

<style lang="less" scoped>
.card-setting {
  display: flex;
  flex-direction: column;
  width: 100%;

  .dht-name {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #181c25;

    i {
      margin-right: 8px;
      font-size: 12px;
      color: #666;
    }
  }

  .dht-content {
    width: 100%;
  }

  .text-level-second {
    color: var(--color-neutrals15);
    font-size: 12px;
    line-height: 20px;
  }

  .text-hightlight {
    color: var(--color-primary06);
    cursor: pointer;
  }

  .form-wrap {
    .form-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      > span {
        width: 60px;
        font-size: 12px;
        color: var(--color-neutrals15);
      }

      &.align-items-baseline {
        align-items: baseline;
      }
    }

    .form-item_value {
      flex: 1;
      margin-left: 10px;

      .tag-title {
        font-size: 12px;
        color: var(--color-neutrals15);
        margin: 8px 0 4px 0;
      }
    }

    .mrb10 {
      margin-bottom: 10px;
    }

    .right-title {
      font-size: 12px;
      color: var(--color-neutrals15);
    }
  }
}
</style>
