export const TheLink = () => import('./TheLink/TheLink.vue');
// common 组件
export const fxObjectInputAutocomplete = () => import('./common/FxObjectInputAutocomplete/index.vue');

export const DhtAiOrder = () => import('./common/dhtAiOrder/index.vue');

export const FieldSelect = () => import('./common/fieldselect/index.vue');


import dht_web_container_product_detail from './web/product_detail/container/index.js';
import dht_web_product_detail_key_info from './web/product_detail/key_info/index.js';
import dht_web_product_detail_card from './web/product_detail/card/index.js';
import dht_web_product_detail_all from './web/product_detail/all/index.js';
import dht_web_product_detail_rich_text from './web/product_detail/rich_text/index.js';
import dht_web_product_list_all from './web/product_list/all/index.js';
import dht_web_container_product_list from './web/product_list/container/index.js';
import dht_web_product_list_category_tree from './web/product_list/category-tree/index.js';
import dht_web_product_list_shop_list from './web/product_list/shop-list/index.js';


import dht_web_order_card from './web/order_card/all/index.js';
import dht_web_shopping_cart_all from './web/shopping_card/all/index.js';
import dht_web_quick_order_all from './web/quick_order/all/index.js';

// todo not finish 注意此处与全部商品 走同一个组件, 需验证新旧设计器中兼容, 避免重复注册
// import dht_shopmall_allproduct from './web/product_list/all/index.js';


const loadWidget = (widgetName) => {
    return new Promise((resolve) => {
        Fx.async(['vcrm/sdk'], function(Sdk) {
            Sdk.widgetService.getWidget(widgetName).then((res)=>{
                // resolve({ default: res.default || res })
                resolve(res)
            })
        })
    })
}


// 使用loadWidget方法导出所有vcrm组件
export const cartQuantityInput = () => loadWidget('cartQuantityInput')
export const quantityInput = () => loadWidget('quantityInput')
export const orderQuickluQuantityInput = () => loadWidget('orderQuickluQuantityInput')
export const productList = () => loadWidget('productList')
export const sortSelect = () => loadWidget('sortSelect')
export const recordType = () => loadWidget('recordType')
export const cartRecordType = () => loadWidget('cartRecordType')
export const cartFooter = () => loadWidget('cartFooter')
export const shopCart = () => loadWidget('shopCart')
export const goodDetail = () => loadWidget('goodDetail')
export const spuDetail = () => loadWidget('spuDetail')
export const skuDetail = () => loadWidget('skuDetail')
export const productDetailMeta = () => loadWidget('productDetailMeta')
export const attachPreview = () => loadWidget('attachPreview')
export const splitScreen = () => loadWidget('splitScreen')
export const singleSelect = () => loadWidget('singleSelect')
export const selectConfirm = () => loadWidget('selectConfirm')
export const hotZoneEdit = () => loadWidget('hotZoneEdit')
export const dhtModules = () => loadWidget('dhtModules')
export const mallCategoryTree = () => loadWidget('mallCategoryTree')
export const mallShopList = () => loadWidget('mallShopList')


// 商品详情页
// fktest1395下游使用此参数可以打开 http://localhost/XV/Cross/Portal?fs_out_appid=FSAID_11490c84#/portal/depend/dht-api-dhtbiz-DhtProductDetail?_id=5e869924f71c7b0001edb324
export const DhtProductDetail = function(args) {
    return new Promise((resolve) => {
        Fx.async(['vcrm/sdk'], function(Sdk) {
            Sdk.widgetService.getWidgetApp('goodDetail', {...args}).then((res)=>{
                console.log(res)
                // resolve(res.default)
                resolve(res)
            })
        })
    })
}
// export const dhtWebProductDetailCard = () => import('./web/product_detail/card/index.js');


export const DhtProductDetailMeta = function(args) {
    return new Promise((resolve) => {
        Fx.async(['vcrm/sdk'], function(Sdk) {
            Sdk.widgetService.getWidgetApp('productDetailMeta', {...args}).then((res)=>{
                console.log(res)
                resolve(res)
            })
        })
    })
}

/* 
    商城列表,支持注入到站点中
 */
const DhtCrmProductList = function(args) {
    return new Promise((resolve) => {
        
        // E:\work\FS\crm\crm2\modules\page\shopmall\shopmall.js
        Fx.async(['crm-modules/page/shopmall/shopmall.js'], function(Page) { 
             // 使用Vue.extend创建组件
             const VueWrapper = Vue.extend({
                name: 'DhtCrmProductList',
                data() {
                    return {
                        pageView: null
                    }
                },
                mounted() {
                    // 初始化Backbone视图
                    this.pageView = new Page({
                        el: this.$el,
                        ...args
                    });
                },
                beforeDestroy() {
                    if (this.pageView && this.pageView.remove) {
                        this.pageView.remove();
                    }
                },
                template: '<div class="dht-crm-product-list-wrapper"></div>'
            });
            
            console.log('DhtCrmProductList', VueWrapper)
            resolve(VueWrapper);
        })
    })
}

export default {
    // common 组件
    fxObjectInputAutocomplete,
    DhtAiOrder,
    FieldSelect,

    cartQuantityInput,
    quantityInput,
    orderQuickluQuantityInput,
    productList,
    sortSelect,
    recordType,
    cartRecordType,
    cartFooter,
    shopCart,
    goodDetail,
    spuDetail,
    skuDetail,
    productDetailMeta,
    attachPreview,
    splitScreen,
    singleSelect,
    selectConfirm,
    hotZoneEdit,
    dhtModules,
    mallCategoryTree,
    mallShopList,


    TheLink,
    DhtProductDetail,
    dht_web_container_product_detail,
    dht_web_product_detail_key_info,
    dht_web_product_detail_card,
    dht_web_product_detail_all,
    dht_web_product_detail_rich_text,
    DhtCrmProductList,
    dht_web_product_list_all,
    dht_web_container_product_list,
    dht_web_product_list_category_tree,
    dht_web_product_list_shop_list,
    dht_web_shopping_cart_all,
    dht_web_quick_order_all,
    // ...vcrm_widgets


    // standalone中的代码
    dht_web_order_card,    
};