interface SearchTermOptions {
  apiname: string;
  id: string;
  name: string;
  // 场景变化需要请求数据渲染表格
  isNotRequest: boolean;
  // 是否默认
  isdef: boolean;
  // 默认排序
  orders: number | null;
  // 是否为新增
  isNew: boolean;
  key: '1';
  // 原始类型
  _type: 'default' | 'custom' | 'tenant';
  // 对应以上原始类型
  type: 1 | 2 | 3;
  field_list_type: number;
  record_type: string | null;
  filters: Array<any>;
  // 若field_list_type = 0，会根据这个字段过滤表头
  field_list: Array<{
    field_name: string;
    is_show: true;
  }>
}

interface SearchTerm {
  // 会作为请求的key
  type: 'term';
  // 位置
  pos: 'T' | 'C';
  // 是否显示管理
  showManage: boolean;
  showCustom: boolean;
  // 默认值
  defaultValue: string;
  // 下拉列表数组
  options: SearchTermOptions;
  addUrl: '/EM1HNCRM/API/v1/object/custom_scene/service/createCustomScene'; // 新建场景
  editUrl: '/EM1HNCRM/API/v1/object/custom_scene/service/updateCustomScene'; // 编辑场景
  saveUrl: '/EM1HNCRM/API/v1/object/custom_scene/service/adjustCustomSceneOrder'; // 保存场景顺序和隐藏
  delUrl: '/EM1HNCRM/API/v1/object/custom_scene/service/deleteCustomScene'; // 删除场景
  setUrl: '/EM1HNCRM/API/v1/object/custom_scene/service/setDefaultScene'; // 设置为默认场景
  getByIdUrl: '/EM1HNCRM/API/v1/object/custom_scene/service/findCustomSceneById'; // 根据id 获取场景数据
  parseParam: Function;
  parseResult: Function;
}
