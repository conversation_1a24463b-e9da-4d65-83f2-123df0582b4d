import { modelExtend } from './model/model';
import { viewExtend } from './view/view';
import { upLoadLog } from '../../../utils/logUtil';

export function actionExtend(Action: BackboneComponent): BackboneComponent {
  // const proto = Action.prototype;

  return Action.extend({
    /**
     * 新建订单
     */
    add(obj: any) {
      const me = this;
      // 如果开启了可售范围过滤并配置了参数，则放入主对象数据中
      if ($dht.config.newAvailableRangeFilter?.isEnable) {
        const availableRangeFilterData = $dht.getMainFilterData();
        const { apiname, value } = availableRangeFilterData;
        if (apiname) {
          if (!obj.data) obj.data = {};
          obj.data[apiname] = value;
          // lookup字段把存起来的__r拿过来
          const label = $dht.config.customer[`${apiname}__r`];
          if (label) obj.data[`${apiname}__r`] = label;
        }
      }
      console.log('==============', obj);

      CRM.util.waiting();
      this.isThirdOrder(() => { // 在标准订单中，这个方法里有请求（获取配置数据），所以保留这个逻辑，和行业订单逻辑没关系
        Fx.async([
          'crm-modules/action/field/field',
          'crm-modules/action/orderform/view/view',
          'crm-modules/action/orderform/model/model',
        ], (Field: BackboneComponent, View: BackboneComponent, Model: BackboneComponent) => {
          CRM.util.waiting(false);

          Field.add(_.extend({
            View: viewExtend(View),
            Model: modelExtend(Model),
            apiname: 'SalesOrderObj',
            workFlowType: 2,
            constraintPriceBookPriority: true, // 强制走价目表优先级
            plugins: {
              domains: [{
                pluginApiName: 'dht_orderform',
                objectApiName: 'SalesOrderObj',
                fieldApiName: '',
                resource: 'vcrm/plugin/dht_orderform',
                params: {
                  source: obj.source,
                  fieldMapping: {},
                  details: [{
                    objectApiName: 'SalesOrderProductObj',
                    detailKey: 'dht_orderform_detail',
                    fieldMapping: {},
                  }],
                },
              }],
              fields: [],
              hooks: [],
            },
            success(data: any, modelData: any) {
              const mdData = me.getTablesDataByModel(modelData); // 用于购物车删除
              me.trigger('refresh', 'add', data, data._id, mdData);
              !data.__isContinue && obj.showDetail && me.showDetail({
                id: data && data._id,
                apiname: 'SalesOrderObj',
              });
            },
            error(data: any, objs: any) {
              if (data && data.Result && data.Result.FailureCode === '48888888') {
                CRM.util.alert(data.Result.FailureMessage, () => {
                  me.trigger('refresh');
                  objs.destroy && objs.destroy();
                });
              }
            },
          }, obj));
        });
      });
      // 神策上传
      let operationId = 'createBtn';
      if (obj.source) {
        operationId = `${obj.source}_${operationId}`;
      }
      upLoadLog('order', operationId, 'cl');
    },

    edit(obj: any) {
      const me = this;
      CRM.util.waiting();
      this.isThirdOrder(() => {
        Fx.async([
          'crm-modules/action/field/field',
          'crm-modules/action/orderform/view/view',
          'crm-modules/action/orderform/model/model',
        ], (Field: BackboneComponent, View: BackboneComponent, Model: BackboneComponent) => {
          CRM.util.waiting(false);
          Field.edit(_.extend({
            View: viewExtend(View),
            Model: modelExtend(Model),
            apiname: 'SalesOrderObj',
            title: obj.title || $t('编辑销售订单'),
            constraintPriceBookPriority: true, // 强制走价目表优先级
            plugins: {
              domains: [{
                pluginApiName: 'dht_orderform',
                objectApiName: 'SalesOrderObj',
                fieldApiName: '',
                resource: 'vcrm/plugin/dht_orderform',
                params: {
                  source: obj.source,
                  fieldMapping: {},
                  details: [{
                    objectApiName: 'SalesOrderProductObj',
                    detailKey: 'dht_orderform_detail',
                    fieldMapping: {},
                  }],
                },
              }],
              fields: [],
              hooks: [],
            },
            success: (data: any) => {
              me.trigger('refresh', 'edit', data);
            },
          }, obj));
        });
      });
    },

    // 获取订单产品表格数据
    getTablesDataByModel(modelData: any) {
      if (!modelData) return [];

      let mdData: any = [];
      const tablesData = modelData.allTablesData && modelData.allTablesData.SalesOrderProductObj;
      _.map(tablesData || {}, (list: any) => {
        mdData = mdData.concat(list);
      });
      return mdData;
    },

  });
}
