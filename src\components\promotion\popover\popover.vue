<template>
  <div v-show="isVisible" v-loading="isLoading"
    class="dht-promotion-popover"
    :style="{ top: `${top}px`, left: `${left}px`}"
  >
    <promotion-snippet v-for="(item, index) in promotionList" :key="index"
      :promotionDetail="item.promotionDetail"
      :promotionRules="item.promotionRules"
      @view-gifts="handleViewGifts"
    />
  </div>
</template>

<script lang="ts">
import PromotionSnippet from './snippet.vue';

export default {
  name: 'DhtPromotionPopover',
  components: { PromotionSnippet },
  props: {
    // 是否延迟获取数据，由外部调用
    lazyload: {
      type: Boolean,
      default: true,
    },
    // 促销列表，如果外部已经获取到，避免重复获取；
    // 否则，内部会重新加载一次
    promotions: {
      type: Array,
      default: null,
    },
    spuId: {
      type: String,
      default: '',
    },
    productId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      top: 0,
      left: 0,
      isVisible: false,
      isLoading: false,
      isLoaded: false,
      /**
       * 目标ID，即SPU-ID或者SKU-ID
       */
      targetId: '',
      /**
       * Array<{promotionDetail: PromotionExt; promotionRules: PromotionRule[]}>
       */
      promotionList: [],
    };
  },
  created() {
    // 用于缓存促销赠品数据
    this.promotionGiftMap = {};
    this.targetId = this.spuId || this.productId;
    if (!this.lazyload) {
      this.getData();
    }
  },
  destroyed() {
    this.promotionModels = null;
    this.promotionGiftMap = null;
  },
  methods: {
    show(options: any = {}) {
      this.isVisible = true;
      this.top = options.top || 0;
      this.left = options.left || 0;
      if (this.targetId !== options.productId) {
        this.isLoaded = false;
        this.targetId = options.productId;
      }
      this.getData();
    },
    hide() {
      this.isVisible = false;
    },
    /**
     * 获取促销列表详情
     */
    getData() {
      if (this.isLoading) return;
      this.promotionList = [];
      this.promotionModels = [];

      this.isLoading = true;
      const p = this.promotions
        ? Promise.resolve(this.promotions)
        : $dht.services.promotion.getPromotionsBySkuId(this.targetId);
      p.then((promotions: any[]) => {
        this.promotionModels = promotions || [];
        this.promotionList = _.map(this.promotionModels, (model: any) => {
          return {
            promotionDetail: model.getPromotionInfo(),
            promotionRules: model.getPromotionRules(true),
          };
        });
      }).catch((err: any) => {
        console.warn(err);
      }).then(() => {
        this.isLoaded = true;
        this.isLoading = false;
      });
    },
    /**
     * 查看赠品
     * @param promotionRuleId 促销规则ID
     * @param promotionId 促销ID
     */
    handleViewGifts(promotionRuleId: string, promotionId: string) {
      console.log(promotionRuleId, promotionId);
      // const targetPromotionModel = _.find(this.promotionModels, (each: any) => {
      //   return each.getPromotionInfo().id === promotionId;
      // });
      // if (!targetPromotionModel) return;
      // let promise = null;
      // if (this.promotionGiftMap[promotionId]) {
      //   promise = Promise.resolve(this.promotionGiftMap[promotionId]);
      // } else {
      //   const promotionGiftIds = targetPromotionModel.getPromotionGiftIds();
      //   promise = Promise.all([
      //     this.batchGetProductsBySkuIds({ ids: promotionGiftIds.skuGiftIds }),
      //     this.batchGetProductsBySpuIds({ ids: promotionGiftIds.spuGiftIds }),
      //   ]).then((values) => {
      //     this.promotionGiftMap[promotionId] = {
      //       skuList: _.flatten(values[0]),
      //       spuList: _.flatten(values[1]),
      //     };
      //     return this.promotionGiftMap[promotionId];
      //   });
      // }
      // promise.then(({ skuList, spuList }) => {
      //   const promotionRules = targetPromotionModel.getPromotionRules();
      //   const promotionGifts = targetPromotionModel.getPromotionRuleGifts(promotionRuleId);
      //   const promotionRule = _.find(promotionRules, item => item.id === promotionRuleId);
      //   let renderGifts = _.map(promotionGifts, (promotionGift) => {
      //     if (promotionGift.giftType === '3') { // 赠送SPU
      //       const finded = _.find(spuList, (item) => item.spuId === promotionGift.giftSpuId);
      //       return !finded ? promotionGift : {
      //         ...promotionGift,
      //         giftProductIdR: finded.spuName,
      //         productImages: finded.spuImages,
      //       };
      //     }
      //     if (promotionGift.giftType === '1') { // 赠送SKU
      //       const finded = _.find(skuList, (item) => item.id === promotionGift.giftProductId);
      //       return !finded ? promotionGift : {
      //         ...promotionGift,
      //         giftProductIdR: finded.name,
      //         productImages: finded.productImages,
      //       };
      //     }
      //     // 剩余的就是赠送本品了
      //     return promotionGift;
      //   });
      //   PromotionGiftSelector.selectGifts({
      //     gifts: renderGifts,
      //     readonly: true,
      //     optionalCount: promotionRule.optionGiftNum,
      //   });
      // });
    },
  },
} as VueComponent;
</script>

<style>
.dht-promotion-popover {
  z-index: 10;
  position: absolute;
  top: 0;
  left: 0;
  min-height: 50px;
  min-width: 300px;
  background: #fff;
  border: 1px solid #d6d6d6;
}
</style>
