function getAsyncComonent(): Promise<VueComponent> {
  return new Promise((resolve) => {
    Fx.async('paas-appcustomization/runsdk.js', (appcustom: any) => {
      appcustom
        .runningcrosslayout()
        .then((CrossPaasLayout: VueComponent) => {
          resolve(CrossPaasLayout);
        });
    });
  });
}

export default async function AsyncPaasLayout() {
  const CrossPaasLayout = await getAsyncComonent();
  const Component: VueComponent = {
    extends: CrossPaasLayout,

    props: ['value'],

    methods: {
      getCustomerLayout() {
        return new Promise((resolve) => {
          if (this.value) {
            Fx.util.FHHApi({
              url: '/EM1HWebPage/UserHomePage/GetUserHomePageLayoutByLayoutId',
              data: { LayoutID: this.value.webPageId },
              success: (data: any) => {
                const homePageLayout = data.Value.HomePageLayout;
                if (homePageLayout && homePageLayout.customerLayout) {
                  // const components = homePageLayout.customerLayout.components;
                  // _.each(components, (value: any, key: string) => {
                  //   const arr = key.split('-');
                  //   if (arr[1] === 'FSAID_11490c84') {
                  //     value.type = arr[0];
                  //   }
                  // });
                  // console.log(components);
                  // components.training_partner = {
                  //   api_name: 'training_partner',
                  //   limit: 1,
                  //   props: {
                  //     api_name: 'training_partner-FSAID_11490c84',
                  //     appId: 'FSAID_11490c84',
                  //     buttons: [],
                  //     cardId: 'training_partner',
                  //     dataId: 'training_partner',
                  //     header: '培训助手',
                  //     nameI18nKey: 'dht.training_partner',
                  //     related_list_name: '',
                  //     title: '培训助手',
                  //   },
                  //   type: 'training_partner',
                  // };
                }
                return resolve(data.Value.HomePageLayout);
              },
            });
          } else {
            resolve(undefined);
          }
        });
      },

      beforeInit(callback: (data: any) => any) {
        this.getCustomerLayout().then((homePageLayout: any) => {
          callback(homePageLayout);
        });
      },
    },
  };

  return Component;
}
