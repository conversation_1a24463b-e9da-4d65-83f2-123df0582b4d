<template>
    <div class="dht-shopmall-common-pre">
        <p class="text-level-second">{{ $t('dht.shopmall_widget.setting_in_all_product') }}</p>
    </div>
</template>

<script>
module.exports = Vue.extend({
    name: "ShopMallCommonPre",

    template: '__template__',
})
</script>

<style lang="less" scoped>
.dht-shopmall-common-pre {
    .text-level-second {
        font-size: 12px;
        color: var(--color-neutrals11);
    }
}
</style>