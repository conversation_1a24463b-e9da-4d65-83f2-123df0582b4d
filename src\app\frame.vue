<template>
  <div class="portal-root">
    <portal-menu ref="menu" v-if="showFrame" class="portal-aside" :appId="appId"></portal-menu>
    <div class="portal-view" v-if="showFrame">
      <transition name="fade" mode="out-in">
        <router-view :appId="appId" class="view-content"></router-view>
      </transition>
    </div>
  </div>
</template>

<script lang="ts">
import PortalMenu from '../components/menu/menu.vue';

const Component: VueComponent = {
  components: {
    PortalMenu,
  },
  props: {
    appId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      showFrame: true,
    };
  },

  watch: {
    '$route.path': function w(newValue: string, oldValue: string) {
      console.log(newValue, oldValue);
      this.$refs.menu.refresh();
    },
  },

  created() {
    window.addEventListener('tpls.change', () => {
      console.log('.....');
    });
  },
  methods: {
    refresh() {
      // TODO
    },
  },

};

export default Component;
</script>

<style lang="less">
.portal-root {
  position: absolute;
  display: flex;
  top: 56px;
  right: 0;
  bottom: 0;
  left: 0;
}

.portal-aside {
  position: relative;
  flex: 0 0 240px;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(33, 43, 54, 0.1);

  &.fold {
    flex: 0 0 64px;
  }
}

.portal-view {
  position: relative;
  flex: 1;
  z-index: 3;
  overflow-x: auto;
  overflow-y: hidden;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.75s ease;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

.view-content {
  transition: all 0.75s cubic-bezier(0.55, 0, 0.1, 1);
}

.dht-module-content {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .crm-module-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
}

// .dht-stepbtn-hack-wrapper {
//   display: flex;
//   width: 100%;
// }
// .dht-stepbtn-hack {
//   width: 28px;
//   height: 28px;
//   line-height: 28px;
//   text-align: center;
// }
// .dht-stepbtn-hack-left {
//   margin-left: -5px;
//   border-right: 1px solid #dee1e6;
// }
// .dht-stepbtn-hack-body {
//   width: 100%;
// }
// .dht-stepbtn-hack-right {
//   margin-right: -5px;
//   border-left: 1px solid #dee1e6;
// }
</style>

<style>
html,
body {
  width: 100%;
  height: 100%;
  overflow: auto;
}
</style>
