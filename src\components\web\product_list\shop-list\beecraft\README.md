# 商品列表设置组件

## 概述
这是一个用于配置商品列表显示的设置组件，现在包含卡片设置和基础设置两个主要部分。

## 组件结构

### 主要组件
- `setting.vue` - 主设置组件容器
- `card-setting.vue` - 卡片设置组件（原有功能）
- `base-setting.vue` - 基础设置组件（新增，包含基本信息、筛选、排序）

### 数据结构

#### card_main_info（卡片主要信息）
```javascript
{
  picture_apiname: 'string',    // 图片字段API名称
  name_apiname: 'string',       // 标题字段API名称
  price_apiname: 'string',      // 价格字段API名称
  tag_apiname: ['string'],      // 标签字段列表
  is_tag_show: boolean,         // 是否显示标签
  show_fields: ['string'],      // 显示字段列表
  component_name: 'string',     // 组件名称（新增）
  show_name: boolean           // 是否显示名称（新增）
}
```

#### filter_info（筛选信息）
```javascript
{
  filter_fields: ['string'],    // 筛选字段列表
  filter_layout: 'string'       // 筛选布局（'1'-单列, '2'-双列, '3'-内联, '4'-网格）
}
```

#### sort_info（排序信息）
```javascript
{
  sort_fields: ['string'],      // 排序字段列表
  default_sort: 'string'        // 默认排序方式
}
```

## 功能特性

### 卡片设置（card-setting.vue）
- 图片字段选择
- 标题字段选择
- 价格字段选择
- 更多字段配置
- 标签显示设置

### 基础设置（base-setting.vue）
#### 基本信息
- 组件名称输入框
- 是否显示名称复选框

#### 筛选设置
- 支持多种字段类型的筛选配置
- 使用fieldselect组件进行字段选择
- 筛选布局选择（单列、双列、内联、网格）

#### 排序设置
- 排序字段选择
- 默认排序方式配置

### 折叠功能
- 每个设置部分都支持折叠/展开
- 参考card-setting.vue的折叠样式实现

## 使用方法

```vue
<template>
  <div class="dht-shopmall-allproduct-setting" v-if="!isLoading">
    <card-setting
      ref="cardSetting"
      :all-fields="allFields"
    />
    
    <base-setting
      ref="baseSetting"
      :all-fields="allFields"
    />
  </div>
</template>

<script>
import CardSetting from './card-setting.vue';
import BaseSetting from './base-setting.vue';

export default {
  components: {
    CardSetting,
    BaseSetting
  },
  data() {
    return {
      isLoading: true,
      allFields: {} // 字段信息对象
    }
  },
  methods: {
    async init() {
      // 获取字段信息
      await this.fetchAllFields();
      
      // 初始化子组件
      this.$refs.cardSetting && this.$refs.cardSetting.init(card_main_info, this.isCardInit);
      this.$refs.baseSetting && this.$refs.baseSetting.init(card_main_info);
      
      this.isLoading = false;
    }
  }
}
</script>
```

## 设计稿对应关系

### 基本信息部分
- 组件名称输入框 - 对应设计稿中的文本输入框
- 是否显示名称复选框 - 对应设计稿中的复选框

### 筛选部分
- 筛选字段选择器 - 支持多字段选择和配置

### 排序部分
- 排序字段选择器 - 支持多字段选择
- 默认排序下拉框 - 预设排序选项

## 事件和方法

### 初始化
- `init(cardMainInfo)` - 初始化组件数据

### 数据更新
- `updateBasicInfo()` - 更新基本信息
- `updateFilterInfo()` - 更新筛选信息
- `updateSortInfo()` - 更新排序信息

### 重置
- `resetVal()` - 重置所有设置为默认值

## 样式说明
- 使用Less预处理器
- 采用与card-setting.vue一致的折叠样式
- 支持响应式布局
- 与fx-ui设计系统保持一致
