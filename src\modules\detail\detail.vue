<script lang="ts">
import BaseMixin from '../mixins/base';
import { getHashQuery } from '../../utils/url';

/**
 * 订货通的商品详情落地页
 */
const Component: VueComponent = {
  name: 'DhtModuleGoodDetail',

  mixins: [BaseMixin],

  methods: {
    renderComponent(): void {
      Fx.async(['vcrm/sdk'], async (vcrmSdk: any) => {
        this.$el.classList.add('dht-good-detail-page');
        const wrapper = document.createElement('div');
        this.$el.appendChild(wrapper);
        const query = getHashQuery(window.location.hash);

        this.$$c.goodDetailInstance = await vcrmSdk.widgetService.getWidgetApp('goodDetail', {
          el: wrapper,
          propsData: {
            isSpuMode: window.$dht.config.sail.isSpuMode,
            isUseByModal: false,
            product: {
              spu_id: query.spu_id || '',
              _id: query._id || '',
            },
          },
        });
      });
    },
  },
};

export default Component;
</script>
<style>
  .dht-good-detail-page {
    overflow: auto !important;
    background: white;
  }
</style>
