type QueryType =
  | 'appId'
  | 'path'
  | 'module'
  | 'component'
  | 'subComponent'
  | 'params'
  | 'upstreamEa'
  | 'apiName';

/**
 * Url参数解析
 *
 * @param query
 */
function $query(this: VueComponent, query: QueryType, base?: any): string | undefined | string[] {
  const $route = !base ? this.$router.currentRoute : base;
  const params = $route.params;

  if (!params) return undefined;

  // query = apiName 单独处理
  if (query === 'apiName' && params.params) {
    return params.params.split('/')[0];
  }

  // query = params 单独处理
  if (query === 'params' && params.params) {
    return params.params.split('/');
  }

  // query = module 单独处理
  if (query === 'module') {
    return [params.component, params.subComponent]
      .filter((item) => !!item)
      .join('/');
  }

  // query = upstreamEa 单独处理
  if (query === 'upstreamEa') {
    return $.cookie('ERUpstreamEa');
  }

  if (params && query) {
    return params[query];
  }

  return undefined;
}

export function init() {
  // 注入一些Vue全局方法，方便组件里调用
  Vue.prototype.$query = $query;
}
