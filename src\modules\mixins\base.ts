export default {
  data() {
    return {
      isEmpty: false,
      isError: false,
    };
  },
  /**
   * 生成`BackboneComponent`的组件链接
   */
  created() {
    this.$$c = {};
  },
  /**
   * 渲染`BackboneComponent`
   */
  mounted() {
    this.renderComponent();
  },
  /**
   * 销毁`BackboneComponent`
   */
  destroyed() {
    _.each(this.$$c, ($c: any) => {
      if (!$c) return;
      if (_.isFunction($c.destroy)) {
        $c.destroy();
      }
      if (_.isFunction($c.$destroy)) {
        $c.$destroy();
      }
    });
    this.$$c = null;
  },
  render(h: any) {
    const children = [];
    if (this.isEmptry) {
      children.push(h('p', { class: { 'dht-module-empty': true } }, $t('该页面不存在！')));
    }
    if (this.isError) {
      children.push(h('p', { class: { 'dht-module-error': true } }, $t('页面加载失败！')));
    }
    return h('div', {
      class: { 'dht-module-content': true, 'portal-module-content': true, 'crm-module-wrap': true },
      style: { height: '100%' },
    }, children);
  },
  methods: {
    renderComponent(): Promise<void> {
      return Promise.resolve();
    },
  },
} as any;
