import './styles/index.less';
import { initDHT } from './bootstrap-dht';
import { getCrmAllConfig } from './context/global';
import distributionService from './utils/distribution';
import { showAppStepGuide } from './utils/guide';
import { getCurrency } from './utils/currency';
import {
  setUserConfigCache,
  selectAvailableRange,
  selectOneModal,
} from './utils/availableRange';
import { USER_CONFIG_KEYS, getUserConfigByKey } from './utils/common';
import { adService } from './utils/advertisementPop';

initDHT();

/**
 * 监听自定义路由改变事件，进入首页会触发
 */
function addEventListenerByRouterChange() {
  window.addEventListener('router.change', (e: any) => {
    console.log('================== router.change');
    const detail = e.detail;
    if (detail?.appId === window.$dht.appId) {
      window.dht.lifeHookEnter(null, null, () => {});
    }
  });
}
/**
 * 备用方法
 * 监听 url hash 改变事件
 * tips: 初次进入应用不会触发，如果要用这个方法，需要注意兼容这个点
 */
// function addEventListenerByHashChange() {
//   window.addEventListener('hashchange', (e: any) => {
//     console.log('================== hashchange');
//     const reg = /#\/portal\/index/; // 首页的hash
//     if (reg.test(e.newURL)) { // 进入首页
//       window.dht.lifeHookEnter(null, null, () => {});
//     }
//   });
// }

// export async function bootstrap() {
//   initDHT();
// }

export async function appWillMount() {
  addEventListenerByRouterChange();

  // 订单业务类型个人级, 可售范围过滤个人级, 供货商个人级
  const userConfigKeys = Object.values(USER_CONFIG_KEYS);
  const p0 = getCrmAllConfig();
  const p1 = $dht._initBizConfig();
  const p2 = getCurrency();
  const p3 = $dht.getService('bizconfig').getUserConfigValues({ keys: userConfigKeys }); // 订单业务类型个人级
  const p4 = $dht.getRecordTypeData();

  const configs = await Promise.all([p0, p1, p2, p3, p4]);

  const userConfigs = configs[3].values;
  let recordTypeList = configs[4];

  // 多级订货选择供货商（没有开启商城模式也要选择，否则丢失供应商数据）
  if ($dht.config.sail.isOpenMultiLevelOrder) {
    const relationList = await distributionService.getChannelRelationList();

    // 没有渠道交易关系退出
    if (!relationList || relationList.length === 0) {
      CRM.util.alert($t('dht.multi_level_order.no_supplier_tip'));

      return Promise.reject(new Error($t('dht.multi_level_order.no_supplier_tip')));
    }

    const storedConfig = getUserConfigByKey(userConfigs, USER_CONFIG_KEYS.MULTI_LEVEL_ORDER);
    let storedSupplier = storedConfig && storedConfig.value;

    // 一键转单场景，必须要重新选择供应商
    if (window.location.hash.includes('origin_order_id')) {
      storedSupplier = '';
    }

    const supplierValue = await distributionService.selectSupplier(
      relationList,
      storedSupplier,
    );

    distributionService.updateUserInfo(relationList, supplierValue);
    recordTypeList = distributionService.filterDistributionRecordType(recordTypeList);
  }

  // 没有开启商城模式，什么都不用选，直接返回
  if (!$dht.config.sail.isShopMallMode) return configs;

  setUserConfigCache(userConfigs);

  // 开启可售范围过滤需选择数据后进入页面
  if ($dht.config.newAvailableRangeFilter.isEnable) {
    await selectAvailableRange(configs, recordTypeList);
  } else {
    await selectOneModal(
      { api_name: 'record_type', label: $t('业务类型') },
      recordTypeList,
      'recordtype',
    );
  }

  return configs;
}
function showAd() {
  adService.create();
}
export async function appDidMount() {
  showAppStepGuide();
  showAd();
}
