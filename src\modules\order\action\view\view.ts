import { LifeHooks } from '../../../../base/hooks';
import { mdExtend } from '../form/md/md';
import { orderamountExtend } from '../form/orderamount';
import { discountExtend } from '../form/discount';

export function viewExtend(View: BackboneComponent): BackboneComponent {
  const proto = View.prototype;

  /* eslint no-param-reassign: 0 */
  View = View.extend({
    initialize() {
      const me = this;
      proto.initialize.apply(this, arguments);
      Fx.async([
        'crm-modules/action/orderform/form/md/md',
        'crm-modules/action/orderform/form/orderamount',
        'crm-modules/action/orderform/form/discount',
      ], (Md: BackboneComponent, OrderAmount: BackboneComponent, Discount: BackboneComponent) => {
        !CRM.util.isUsePlugin('SalesOrderObj') && (me.mycomponents.md = mdExtend(Md)); // 插件灰度
        // me.mycomponents.md = mdExtend(Md);
        me.mycomponents.order_amount = orderamountExtend(OrderAmount);
        me.mycomponents.discount = discountExtend(Discount);
      });
    },
  });

  // TODO 为什么不能移到上面
  View = View.extend(LifeHooks());

  return View;
}
