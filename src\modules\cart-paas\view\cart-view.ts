import { LifeHooks } from '../../../base/hooks';
import { discountExtend } from '../../order/action/form/discount';
import { orderamountExtend } from '../../order/action/form/orderamount';
import { cartMdExtend } from '../extend/md-extend';

export function viewExtend(View: BackboneComponent): BackboneComponent {
  const proto = View.prototype;

  /* eslint no-param-reassign: 0 */
  View = View.extend({
    initialize() {
      const me = this;
      proto.initialize.apply(this, arguments);
      Fx.async([
        'crm-modules/action/orderform/form/md/md',
        'crm-modules/action/orderform/form/orderamount',
        'crm-modules/action/orderform/form/discount',
      ], (Md: BackboneComponent, OrderAmount: BackboneComponent, Discount: BackboneComponent) => {
        !CRM.util.isUsePlugin('SalesOrderObj') && (me.mycomponents.md = cartMdExtend(Md));
        // me.mycomponents.md = cartMdExtend(Md);
        me.mycomponents.order_amount = orderamountExtend(OrderAmount);
        me.mycomponents.discount = discountExtend(Discount);
      });
    },
    /**
     * @override
     * 屏蔽中加载中骨架
     */
    loadTemplate() {
      return '';
    },
    /**
     * @override
     */
    toggleGroup() {
      this.hiddenOtherGroups();
    },
    /**
     * 隐藏除开订单产品的所有布局
     */
    hiddenOtherGroups() {
      const me = this as any;
      if (me.$groups) {
        me.$groups.each((index: number, group: any) => {
          if (!$(group).children('.j-comp-wrap[data-apiname="md"]').length) {
            $(group).hide();
          }
        });
      }
    },

    mdrendercomplete() {
      proto.mdrendercomplete.apply(this, arguments);

      if (!CRM.util.isUsePlugin('SalesOrderObj') && window.$dht.cartLoading) {
        $dht.cartLoading.close();
        $dht.cartLoading = null;
      }
    },
  });

  View = View.extend(LifeHooks());

  return View;
}
