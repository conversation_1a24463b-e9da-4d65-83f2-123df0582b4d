/// <reference path='./env-provider.d.ts' />

declare const _: any;

declare const $: any;

declare const $t: (key: string, options?: any, defaultValue?: string) => string;

declare const Vue: any;

declare const Vuex: any;

declare const VueRouter: any;

declare const $dht: {
  upstreamEa: string;
  appId: string;
  name: string;
  version: string;
  config: Record<string, any>;
  store: any;
  services: Record<string, any>;
  getService: (name: string) => any;
  util: Record<string, Function>;
  lifeHook: {
    enter: () => void;
    leave: () => void;
  };
  [key: string]: any;
};

declare const CRM: {
  api: any;
  util: any;
  _cache: any;
};

declare interface BaseModel {
  _id: string | null;
  name?: string;
  object_describe_api_name?: string;
  /**
   * 多余的未被识别自定义字段
   */
  [fieldName: string]: any;
}
declare type ObjectDataMap = Record<string, any>;

declare interface BackboneComponent {
  options?: any;

  new(options?: any, props?: any): BackboneComponent;

  extend(options: any): BackboneComponent;

  [key: string]: any;
}

declare interface PaasCustomComponent {
  init(el: HTMLElement, props: any): any;
}

declare interface VueComponent {
  [key: string]: any;

  $router?: any;
}

declare type CachedVueComponent = VueComponent | (() => Promise<VueComponent>);

declare interface VueRoute {
  fullPath: string;
  hash: string;
  matched: any[];
  meta: any;
  name: string;
  params: {
    appId: any;
    component: any;
    subComponent: any;
    params: any;
  };
  path: string;
  query: any;
}

declare namespace Fx {
  const ROOT_PATH: string;

  const PATH_SUFFIX: string;

  const staticPath: string;

  const http: any;

  function install(options: any): void;

  function installGlobalEvents(): void;

  function getComponent(name: string): any;

  function getBizComponent(bizname: string, componentName: string): any;

  function async(path: string | string[], callback: any): any;

  const i18n: {
    downLoad: () => Promise<any>;
  };

  const contacts: {
    getCurrentEmployee: () => Record<string, string>;
  };

  const store: {
    getItem(key: string): Promise<number>;
    setItem(key: string, val: number): Promise<number>;
  };

  export const util: {
    deepClone: (obj: object) => object;

    FHHApi: (options: object, props?: object) => Promise<any>;

    alert: (message?: String, callback?: Function, opts?: Object, btnName?: String) => object;

    error: (message?: string, callback?: Function, opts?: Object) => any;

    getAvatarPath: () => string;

    getQueryStringArgs: () => any;
  };
  const topNav: any;
}

declare namespace FxUI {
  function Message(message: string): void;

  const Utils: any;

  const Loading: any;
}

declare interface Window {
  $dht: any;

  FS: any;

  /**
   * 在线客服
   */
  FSImChat: any;

  CRM: any;

  PAAS_CONFIG: Record<string, string>;

  // eslint-disable-next-line camelcase
  CRM_All_MENU: any;

  Portal: any;

  dht: any;
}

declare const FS: {
  util: any;
  getAccount: any;
};

declare module '@sail/core' {
  const bootstrap: (options: any, global: any) => {};
  const vueInstall: () => {};
}
