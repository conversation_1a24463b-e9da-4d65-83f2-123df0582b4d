import debug from 'debug';
import { imports } from '../base/utils';

const log = debug('@nsail:loader');

const componentCache: Record<string, CachedVueComponent> = {};

/**
 * 注册Vue组件到Vue和本地cache里
 * @param component 组件配置
 */
function registModule(
  component: { path: string; dirname?: string; component: CachedVueComponent },
) {
  log(`Load module: path = ${component.path}, dirname = ${component.dirname}`);
  if (component.path) {
    componentCache[component.path] = component.component;
  } else if (component.dirname) {
    componentCache[`dirname_${component.dirname}`] = component.component;
  } else {
    throw new Error('Module definition must has path or dirname');
  }
}

imports(
  (require as any).context('./', true, /\w+\/index\.ts$/), // 需要排除自身这个文件
  ({ default: component }: any) => {
    if (_.isArray(component)) {
      component.forEach((item: any) => {
        registModule(item);
      });
    } else {
      registModule(component);
    }
  },
);

export type ModuleOptions = {
  component: string;
  subComponent?: string | null;
  objectApiName?: string | null;
  objectRecordType?: string | null;
}

/**
 * 获取页面级组件
 * @param options 模块参数
 */
export function getModule(options: ModuleOptions): CachedVueComponent | null {
  let c: CachedVueComponent | null = null;

  if (options.component === 'index') {
    return componentCache['sail-index'];
  }
  if (options.component === 'list') {
    const key = `sail-${options.objectApiName}`;
    if (!componentCache[key]) {
      componentCache[key] = () => {
        return import(
          /* webpackChunkName: "object-list" */
          /* webpackMode: "lazy" */
          './object-list/object-list.vue'
        ).then((comp) => {
          // 需要复制一份配置，让每个对象都拥有自己的列表组件，否则
          // 在上层PaaS框架因为会认为是同一个组件而导致不会刷新
          return {
            extends: comp.default,
            name: `DhtModule${options.objectApiName}`,
          };
        });
      };
    }
    const recordType = options.objectRecordType;
    if (recordType) {
      const subKey = `sail-${options.objectApiName}-${recordType}`;
      if (!componentCache[subKey]) {
        componentCache[subKey] = () => {
          const fn: CachedVueComponent = componentCache[key];
          if (typeof fn === 'function') {
            return fn().then((comp) => {
              return {
                extends: comp.extends || comp.default,
                name: `DhtModule${options.objectApiName}${recordType}`,
              };
            });
          }
          return fn;
        };
      }
      return componentCache[subKey];
    }
    c = componentCache[key];
    return c;
  }

  // const dirname = name.split('/')[0];
  // component = componentCache[`dirname_${dirname}`];

  return componentCache.Placeholder;
}
