import { api } from '../api';

// -1未知，0离开，1进入
let status: -1 | 0 | 1 = -1;
/**
 * 进入订货通应用要做的事情
 */
export function enter() {
  if (status === 1) return;
  status = 1;
  // @ts-ignore
  // 暴露两个属性给非本项目代码使用，比如`crm`和`vcrm`里判断
  window.Fx.IS_DHT_CONNECTAPP = true;
  window.FS.IS_DHT_CONNECTAPP = true;

  // 从底层重写CRM对象的`ajax`请求路径
  CRM.util.FHHApiStandard = CRM.util.FHHApi;
  CRM.util.FHHApi = api.fhhApi;

  // 从底层重写CRM对象的详情展示接口
  CRM.api.show_crm_detail_standard = CRM.api.show_crm_detail;
  CRM.api.show_crm_detail = $dht.showCrmDetail;

  // 获取typeKey为500的配置，将引导页的标记都设为已读
  const enterpriseConfig = window.FS.enterpriseConfig || {};
  if (!enterpriseConfig[500]) {
    const guidData = {
      guide_add_PaymentObj: 1,
      guide_add_v2_PaymentObj: 1,
      guide_add_PromotionObj: 1,
      guide_page_paymentobj: 1,
    };
    CRM.util.setEnterpriseConfig(500, JSON.stringify(guidData));
  }
}

/**
 * 离开订货通应用要还原现场
 */
export function leave() {
  // 一定要进入的时候才可以离开
  if (status !== 1) return;
  status = 0;
  // @ts-ignore
  delete window.Fx.IS_DHT_CONNECTAPP;
  delete window.FS.IS_DHT_CONNECTAPP;

  CRM.util.FHHApi = CRM.util.FHHApiStandard;
  CRM.api.show_crm_detail = CRM.api.show_crm_detail_standard;
}
