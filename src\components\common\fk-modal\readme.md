# DModal
动态创建模态框。

## 1. 设计灵感
"modal的使用者更应该专注于modal的内容而不是modal本身"。基于这样的设计理念，
我们特意的设计屏蔽掉了模态框组件这个概念，转而提供了更加简单的操作模态框内
容的API。DModal这个类因此而诞生了。

## 2. How to use
DModal动态模态框在`Vue.prototype`上添加了`$DModal`对象。调用该对象的
`create`方法可以动态的创建模态框，简单的例子如下所示：

```js
export default {
    components: {RecordConfirm},
    methods: {
      this.$DModal.create({
        title: 'demo',
        width: '800px',
        maxHeight: '450px',
        component: RecordConfirm,
        componentParams: {
            data: {greet: 'hello'}
        },
        onOk: (componentRef) => componentRef.save()
      })
    } 
}
```

## 3. 函数签名以及完整配置。
`$DModal.create`会返回一个模态框的实例引用`ModalRef`，若有需要可以调用它的
`close`方法来程序关闭模态框。

```typescript
export interface $DModal {
    // create modal
    create(options: ModalConfigInterface): ModalRef;
}

export interface ModalRef {
    // close modal
    close(): void;
}

export interface ModalConfigInterface {
    // modal title
    title?: string;
    // min height, default 200px
    minHeight?: string;
    // max height, default null
    maxHeight?: string;
    // modal width, default 640px
    width?: string;
    // if enable mask click close modal, default false
    maskClick?: boolean;
    // modal content component, prior to content
    component?: any;
    // component data, this value will as component's props.data property
    componentParams?: any;
    // modal content, when component is existed, content will be ignore
    content?: string;
    // ok button text
    okText?: string;
    // cancel button text
    closeText?: string;
    // when click ok button callback, this will get then content component instance
    // as params, when the returned's Promise resolve or return true, modal will close.
    // otherwise will not.
    onOk?: (componentRef: any) => Promise<any> | boolean;
    // when click cancel or close button callback, this will get then content component instance
    // as params, when the returned's Promise resolve or return true, modal will close.
    // otherwise will not.
    onClose?: (componentRef: any) => Promise<any> | boolean;
}
```

>注意：每一个模态框组件和内容组件都是一个单例，每次调用create都会创建新的实例，
>每次关闭模态框都会销毁其实例以及其相应的dom，因此不必担心数据初始化的过程。另外在内容组件中
>可以正常的访问`vuex`和`vue router`中变量和方法。

## 4. TODO
1. 支持拖拽和拉伸。
2. 支持自定义按钮。



