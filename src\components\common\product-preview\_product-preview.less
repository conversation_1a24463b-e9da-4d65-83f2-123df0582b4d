.dht-product-preview {
  position: relative;
  width: 320px;

  &-poster {
    position: relative;
    width: 320px;
    height: 320px;
    margin-bottom: 20px;
    text-align: center;
    line-height: 317px;
    border: 1px solid #eeeeee;
    box-sizing: border-box;
    &.one-picture {
      margin-bottom: 0;
    }

    img {
      display: inline-block;
      max-width: 100%;
      max-height: 100%;
      vertical-align: middle;
    }
  }

  &-mask {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
    width: 160px;
    height: 160px;
    background-image:url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAIAAAACCAMAAABFaP0WAAAABGdBTUEAAK/INwWK6QAAABl0RVh0U29mdHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAAGUExURT1uzv///62t27cAAAACdFJOU/8A5bcwSgAAABBJREFUeNpiYGBkYGQECDAAAA0ABMZIs2EAAAAASUVORK5CYII=');
  }

  .zoom-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    line-height: 24px;
    height: 24px;
    width: 24px;
    text-align: center;
    background-color: rgba(0, 0, 0, .2);
    color: #fff;
  }

  &-zoom {
    position: absolute;
    z-index: 9999;
    left: 321px;
    top: 0;
    width: 320px;
    height: 320px;
    line-height: 640px;
    overflow: hidden;
    border: 1px solid #d8d8d8;
    background-color: #fff;

    img {
      position: relative;
      display: inline-block;
      width: 200%;
      vertical-align: middle;
    }
  }

  .el-carousel__container {
    height: 66px;
    width: 294px;
  }

  .preview-carousel-arrow {
    border: none;
    outline: 0;
    padding: 0;
    margin: 0;
    height: 36px;
    width: 18px;
    cursor: pointer;
    background-color: rgba(31,45,61,.11);
    color: #FFF;
    z-index: 10;
    text-align: center;
    font-size: 12px;
    &.carousel-arrow-left {
      margin-right: 10px;
      flex: none;
    }
    &.carousel-arrow-right {
      margin-left: 10px;
      flex: none;
    }
  }

  &-specs {
    display: flex;
    align-items: center;
    .el-carousel {
      flex: none;
    }
  }

  .fsc-carousel {
    width: 294px;
    height: 66px;

    .fsc-carousel-prev, .fsc-carousel-next {
      position: absolute;
      z-index: 10;
      width: 14px;
      height: 24px;
      color: #fff;
      background-color: rgba(0, 0, 0, .2);
      text-align: center;
      line-height: 24px;
      font-size: 14px;
    }

    .fsc-carousel-prev {
      left: -31px;
    }

    .fsc-carousel-next {
      right: -31px;
    }
  }
  .img-wrap {
    width: 294px;
  }
  .img-item {
    float: left;
    width: 66px;
    height: 66px;
    padding-right: 10px;

    &:last-child {
      padding-right: 0;
    }

    .img {
      width: 100%;
      height: 100%;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: contain;
      border: 1px solid #eeeeee;
      box-sizing: border-box;

      &.selected {
        border-color: #FC5C08;
      }
    }
  }
}
