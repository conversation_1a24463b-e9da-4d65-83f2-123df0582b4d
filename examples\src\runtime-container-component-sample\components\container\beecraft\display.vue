<template>
  <div class="dhtbiz-container-product-list-display" v-bind="$attrs" style="width: 100%; min-height: 600px;">
    product container display
    <slot></slot>
    <!-- <container-running></container-running> -->
  </div>
</template>

<script>
// import ContainerRunning from '../src/index.vue';
export default {
  name: 'dht_web_container_product_list_beecraft_display',
  props: {
    // bizContext: {
    //   type: Object,
    //   default: () => ({}),
    // },
  },
}
</script>
