<template>
  <div class="portal-index">
    <paas-layout :value="value"></paas-layout>
  </div>
</template>

<script lang="ts">
import PaasLayout from './paas-layout';

const Componet: VueComponent = {
  name: 'DhtModuleIndex',

  components: {
    PaasLayout,
  },

  computed: {
    value() {
      const portalEnv = window.$dht.envProvider;
      const activedTpl = portalEnv!.get('activedTpl');
      const portalTpls = portalEnv!.get('portalTpls');
      if (typeof activedTpl === 'number' && portalTpls[activedTpl]) {
        return {
          ...portalTpls[activedTpl],
        };
      }

      const tpl = portalTpls.find((portalTpl: any) => portalTpl.templeId === activedTpl);

      return {
        ...tpl,
      };
    },
  },

  mounted() {
    this.asyncComponent().then((index: PaasCustomComponent) => {
      this.$index = index.init(this.$refs.container, {
        upstreamEa: this.$query!('upstreamEa'),
      });
    });
  },

  destroyed() {
    this.$index?.$destroy();
    this.$index = null;
  },

  methods: {
    asyncComponent() {
      return new Promise((resolve) => {
        Fx.async('paas-appcustomization/runsdk.js', (paasAppcustom: any) => {
          resolve(paasAppcustom.runningmanufacturer());
        });
      });
    },
  },

};

export default Componet;
</script>

<style lang="less">
.portal-index {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  .uicusom-running-manufacturer-container {
    overflow-x: auto !important;
  }
}
</style>
