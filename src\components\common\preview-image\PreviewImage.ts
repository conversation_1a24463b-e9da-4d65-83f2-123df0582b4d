import PM from './preview-image.vue';

const PreviewImageCom = Vue.extend(PM);

interface Options {
  list: Array<{
    label?: string; // 图片描述
    url: string; // 图片地址
    thumbUrl?: string; // 缩略图的图片地址
    downloadUrl?: string; // 图片下载地址
  }>;
  active?: number;
}

class PreviewImageRef {
  instance: any = null;

  constructor(options: Options) {
    this.instance = new PreviewImageCom({
      el: document.createElement('div'),
      propsData: {
        config: options,
      },
    });

    const container = this.getContainer();
    container.appendChild(this.instance!.$el as HTMLElement);
    this.instance.$on('hide', () => {
      this.instance.show(false);
      const timer = setTimeout(() => {
        clearTimeout(timer);
        this.instance.$destroy();
        container.removeChild(this.instance.$el);
      }, 300);
    });
  }

  getContainer() {
    let container = document.getElementById('DPreviewImageContainer');
    if (!container) {
      container = document.createElement('div');
      container.id = 'DPreviewImageContainer';
      document.body.appendChild(container);
    }
    return container;
  }
}

export class PreviewImage {
  create(options: Options) {
    return new PreviewImageRef(options);
  }
}
