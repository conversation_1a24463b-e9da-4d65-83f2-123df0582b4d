import runningHooks from './running-hooks';
export default function () {
    return {
        name: 'dht_web_product_list_shop_list',
        displayName: '商品列表',
        data: {
            style: {
                justifyContent: 'center',
                alignItems: 'center',
            }
        },
        $$data: {
        },
        related: {
            attributeSettings: [
                {
                  name: 'SetterField',
                  data: {
                    setter: {
                      component: () => import('./setting.vue')
                    }
                  }
            }],
            previewDisplay: () => import(
                './display.vue'
            )
        },
        rules: {
        },
        hooks: runningHooks.hooks
    };
} 