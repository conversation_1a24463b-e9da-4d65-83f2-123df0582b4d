<script lang="ts">
import { getCrmListComponent } from '../../base/bridge';
import { parseParams } from '../../base/utils';
import BaseMixin from '../mixins/base';
import { ext } from './list';
import { add } from './action';

const Component: VueComponent = {
  name: 'DhtModulePayment',

  mixins: [BaseMixin],

  data() {
    return {
      objectApiName: 'PaymentObj',
    };
  },

  methods: {
    renderComponent(): Promise<any> {
      return getCrmListComponent(this.objectApiName, 'PaymentObj', ext)
        .then((Ctor: BackboneComponent | null) => {
          if (!Ctor) {
            this.isEmpty = true;
            return;
          }
          this.$$c.list = new Ctor({
            wrapper: $(this.$el),
            isEdit: false,
          });
          const params = parseParams();
          this.$$c.list.render([this.objectApiName, params[1]]);
          this.$$c.list.on('renderAction', (data: any) => {
            this.renderActionComponent(data);
          });
        })
        .catch((err) => {
          console.error(err);
          this.isError = true;
        });
    },

    /**
     * action render
     */
    renderActionComponent(data: any): Promise<any> {
      return add(data);
    },
  },
};

export default Component;
</script>
