.dht-shopmall-allproduct-setting {    color: var(--color-neutrals15);    font-size: 14px;    .set-item-title {        color: var(--color-neutrals19);        font-size: 12px;        font-weight: bold;        margin-bottom: 12px;        padding-left: 4px;        border-left: 3px solid var(--color-primary06,#FF8000);    }    .dht-shopmall-subtitle {      margin-bottom: 4px;      font-size: 12px;      color: var(--color-neutrals11);    }    .form {        margin: 15px -10px;        padding: 10px;        border: 1px solid var(--color-neutrals04);        &-title {            font-size: 12px;            margin-bottom: 6px;        }        &-wrap {            padding-left: 5px;        }        &-item {            display: flex;            align-items: center;            justify-content: space-between;            margin-bottom: 12px;            &_value {                min-width: 193px;            }        }        .align-items-baseline {            align-items: baseline;        }        .tag-title {            font-size: 12px;            color: var(--color-neutrals15);            margin-top: 19px;            margin-bottom: 6px;        }    }    .pvui-transfer-panel .pvui-transfer-panel-title {        color: var(--color-neutrals11);    }    .mrb10 {        margin-bottom: 10px;    }    .text-level-second {        font-size: 12px;        color: var(--color-neutrals11);    }    .text-hightlight {        color: var(--color-info06);        cursor: pointer;    }}