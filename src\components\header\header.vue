<template>
  <div class="dht-header">
    <div class="dht-header-l">
      <span class="dht-header-uplogo"><img class="logo-img" :src="upstreamLogo" /></span>
      <span class="dht-header-upname">{{ downstream.upstreamEnterpriseName }}({{ $t('i18n.fe_sail_v2.header.supplier') }})</span>
    </div>
    <div class="dht-header-r">
      <ul class="dht-header-nav clearfix">
        <li class="nav-item">
          <div :id="cartComponentId"></div>
        </li>
        <!-- <li class="nav-item">
          <span class="nav-item-icon"><i class="fx-icon-message2"></i></span>
          <span v-if="messageCount > 0" class="nav-item-badge">{{ messageCount }}</span>
        </li> -->
        <li class="nav-item">
          <a class="nav-item-link" href="//www.fxiaoke.com/mob/guide/dht/" target="_blank">
            <span class="nav-item-icon"><i class="fsc-icon--help"></i></span>
            <span>{{ $t('i18n.common.header.help') }}</span>
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-item-link" href="javascript:;" @click="go2service">
            <span class="nav-item-icon"><i class="fsc-icon--service"></i></span>
            <span>{{ $t('i18n.common.header.service') }}</span>
            <span v-if="strMessageCount" class="nav-item-badge">{{ strMessageCount }}</span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts">
import { getDomain } from '../../base/utils';

export default {
  name: 'DhtHeader',
  data() {
    return {
      cartComponentId: `c${Math.floor((Math.random() * 1000000))}`,
      messageCount: 0,
      downstream: $dht.downstream,
    };
  },
  computed: {
    upstreamLogo() {
      return this.downstream.logo || '//a9.fspage.com/open/sail_web/static/<EMAIL>';
    },
    /**
     * 客服消息，只显示气泡点，所有返回非空字符串即可
     */
    strMessageCount() {
      if (this.messageCount <= 0) return '';
      if (this.messageCount <= 99) return this.messageCount;
      return '99+';
    },
  },
  mounted() {
    // 动态加载购物车插件
    const PAAS_CONFIG = window.PAAS_CONFIG;
    // //www.fxiaoke.com/open/plugins/cart/cart-0.1.0.js
    const cartSDK = !PAAS_CONFIG
      ? '//www.fxiaoke.com/open/plugins/cart/cart-0.1.0.js'
      : `${Fx.staticPath}/nsail-cart-dist/${PAAS_CONFIG.nsailCartJsApp || 'cart.js'}`;
    FxUI.Utils
      .loadJS(cartSDK)
      .then(() => {
        (window as any).ShoppingCart.init({ el: `#${this.cartComponentId}` });
      });
    // 动态载入客服模块
    this.loadOnlineService();
    // 在线客服消息漂数
    this._onServiceMessage = (e: any) => {
      const arr = e.origin && e.origin.split('//'); // e.origin=https://***.***.com
      if (arr[1] !== global.location.hostname) return;

      if (e.data != null && e.data !== 'close' && _.isNumber(e.data)) {
        this.messageCount = Number(e.data);
      }
    };
    window.addEventListener('message', this._onServiceMessage, false);
  },
  beforeDestroy() {
    window.removeEventListener('message', this._onServiceMessage, false);
    this._onServiceMessage = null;
  },
  methods: {
    go2service() {
      if (window.FSImChat) {
        this.messageCount = 0;
        window.FSImChat.switchToggle((res: any = {}) => {
          this.$message(res.errorMsg);
        });
      } else {
        this.$message($t('i18n.fe_sail_v2.header.online_service_initializing'));
      }
    },
    loadOnlineService() {
      const script = document.createElement('script');
      script.async = true;
      script.type = 'text/javascript';
      script.id = 'fs-im-script';
      script.src = `//www.${getDomain()}/open/imchat/static/creat.js?internalAppType=true&_t=20210114`;
      document.head.appendChild(script);
    },
  },
} as VueComponent;
</script>

<style lang="less">
.dht-header {
  display: flex;
  justify-content: space-between;
  line-height: normal;
}

.dht-header-l {
  line-height: 22px;
  margin-top: 16px;
  font-size: 14px;
}

.dht-header-uplogo {
  float: left;
  width: 72px;
  height: 22px;
  margin-right: 10px;
}

.dht-header-uplogo .logo-img {
  width: 100%;
  height: 100%;
}

.dht-header-upname {
  float: left;
  margin-right: 16px;
  color: #666;
}

.dht-header-appname {
  float: left;
  font-weight: bold;
}

.dht-header-nav {
  margin-top: 14px;
  margin-right: 20px;
  line-height: 32px;

  .nav-item {
    float: left;
    margin: 0 12px;
    font-size: 12px;
    color: #A3A7B0;
  }

  .nav-item-icon {
    margin-right: 2px;
    font-size: 14px;
  }

  .nav-item-badge {
    position: absolute;
    top: -4px;
    right: 0;
    width: 20px;
    height: 16px;
    line-height: 16px;
    transform: translateX(50%);
    border-radius: 3px;
    background-color: #f43;
    box-shadow: 0px 2px 4px 0px rgba(255, 68, 51, 0.5);
    font-size: 10px;
    text-align: center;
    color: #fff;
  }

  .nav-item-link,
  .nav-item-link:hover,
  .nav-item-link:visited {
    text-decoration: none;
    color: #A3A7B0;
  }
}
</style>
