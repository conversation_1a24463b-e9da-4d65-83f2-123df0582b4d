define(function(require, exports, module) {
const ShopMallMixins = require('../shopmall-mixins');
const VuiSdk = require('paas-vui/sdk');
// 标题
// 与预设产品字段列表同步
// 要展示的系统定义字段
const SHOW_SYSTEM = [
    'name',
    'created_by',
    'create_time',
    'last_modified_by',
    'last_modified_time',
    'out_owner',
    'owner',
    'life_status',
];
// 要隐藏的字段
const HIDE_PACKAGE = [
    'relevant_team',
    'lock_rule',
    'life_status_before_invalid',
    'lock_user',
    'extend_obj_data_id',
    'mc_exchange_rate_version',
    'mc_functional_currency',
];
// 更过字段
// 不宜在卡片上展示的字段类型
const HIDE_FIELD_TYPES = [
    'html_rich_text', // 富文本
    'image', // 图片
    'file_attachment', // 附件
    'big_file_attachment', // 大附件
];
// 不外露展示的字段
const HIDE_FIELDS = [
    'extend_obj_data_id',
    'lock_rule', // 锁定规则
    'object_describe_api_name',
    'package',
    'tenant_id',
    '_id',
    'commodity_label',
    'out_tenant_id',
];
module.exports = Vue.extend({
    name: 'ShopMallProductSetting',
    template: '<div class="dht-shopmall-allproduct-setting" v-if="!isLoading"><div class="set-item"><p class="set-item-title">{{$t(\'视图设置\')}}</p><div class="mrb10"><div class="dht-shopmall-subtitle">{{$t(\'默认视图\')}}</div><fx-radio-group v-model="defaultView"><fx-radio v-for="item in defaultViewOptions" :key="item.value" :label="item.value">{{item.label}}</fx-radio></fx-radio-group></div><div class="mrb10"><div class="dht-shopmall-subtitle">{{$t(\'视图设置\')}}</div><fx-select v-model="curView" width="216" size="small" :options="viewOptions"></fx-select></div><div class="text-level-second" v-if="isShowList">{{ $t(\'dht.shopmall_widget.list_setting_tip\') }}</div><div class="form" v-else-if="isShowCard"><template v-if="isCardInit"><div class="text-level-second">{{ $t(\'dht.shopmall_widget.init_setting\') + $t(\'点击\') }}<span class="text-hightlight" @click="setCardHandle"> {{ $t(\'dht.shopmall_widget.setting_now\') }} </span>{{ $t(\'dht.shopmall_widget.setting_owner_card\') }}</div></template><template v-else=""><p class="form-title">{{$t(\'dht.shopmall_widget.card_content\')}}</p><div class="form-wrap"><div class="form-item"><span>{{$t(\'dht.shopmall_widget.picture\')}}</span><fx-select v-model="pictureVal" width="184" size="small" :options="pictureOptions"></fx-select></div><div class="form-item"><span>{{$t(\'title\')}}</span><fx-select v-model="titleVal" width="184" size="small" :options="titleOptions"></fx-select></div><div class="form-item"><span>{{$t(\'价格\')}}</span><fx-select v-model="priceVal" width="184" size="small" :options="priceOptions"></fx-select></div><p class="mrb10">{{$t(\'dht.shopmall_widget.more_fields\')}}</p><div class="mrb10"><vui-transfer drag="" v-model="showFields" :data="allShowFields" :titles="transferTitles" :filterable="true" :is-hide-checkbox="true" :max="maxFieldsNum"><div class="right-title" slot="right-title"><span>{{ transferTitles[1] }}({{ showFields.length }}/{{ maxFieldsNum }})</span></div></vui-transfer></div><div class="form-item align-items-baseline" v-if="tagOptions.length"><span>{{$t(\'标签\')}}</span><div class="form-item_value"><div><fx-radio v-model="isTagShow" :label="true">{{$t(\'显示\')}}</fx-radio><fx-radio v-model="isTagShow" :label="false">{{$t(\'隐藏\')}}</fx-radio></div><div v-if="isTagShow"><p class="tag-title">{{$t(\'dht.shopmall_widget.top_left_tag_label\')}}:</p><fx-select v-model="tagVal" width="184" size="small" :options="tagOptions"></fx-select></div></div></div></div></template></div></div></div>',
    components: {
        VuiTransfer: () => {
            return VuiSdk.getComponents().then((comps) => {
                return comps.VuiTransfer;
            });
        }
    },
    mixins: [ShopMallMixins],
    props: {
        api_name: String,
    },
    data() {
        return {
            saveViewKey: 'view_mode',
            savePictureKey: 'picture_apiname',
            saveNameKey: 'name_apiname',
            savePriceKey: 'price_apiname',
            saveTagKey: 'tag_apiname',
            saveIsTagShowKey: 'is_tag_show',
            saveShowFieldsKey: 'show_fields',
            isLoading: true,
            isCardInit: false,
            transferTitles: [$t('全部字段'), $t('显示字段')],
            maxFieldsNum: 8,
            curView: '', // 当前视图：卡片/列表
            defaultView: '', // 默认视图
            allShowFields: [],
            showFields: [],
            pictureVal: null,
            titleVal: null,
            priceVal: null,
            tagVal: null,
            isTagShow: true,
        }
    },
    inject: {
        setProps: {
            from: 'setProps',
            default() {
                return () => {}
            }
        },
    },
    computed: {
        viewOptions() {
            return [{
                value: 'card',
                label: $t('卡片视图'),
            }, {
                value: 'list',
                label: $t('列表视图'),
            }];
        },
        // 注意,这里的value一定要和crm的保持一直
        defaultViewOptions() {
            return [{
                value: 'table',
                label: $t('列表视图'),
            }, {
                value: 'card',
                label: $t('卡片视图'),
            }, {
                value: 'split',
                label: $t('分屏视图'),
            }];
        },
        isShowList() {
            return this.curView === 'list';
        },
        isShowCard() {
            return this.curView === 'card';
        },
        fieldsList() {
            return Object.values(this.allFields);
        },
        pictureOptions() {
            const pictureKey = this.isSpuMode ? 'picture' : 'picture_path';
            const { [pictureKey]:picture } = this.allFields;
            const options = this.fieldsList
                .filter((field) => field.type === 'image' && field.api_name !== pictureKey)
                .map((field) => ({
                    value: field.api_name,
                    label: field.label,
                }));
            if (picture) {
                options.unshift({
                    value: picture.api_name,
                    label: picture.label,
                });
            }
            if (!this.pictureVal && options.length) {
                this.pictureVal = options[0].value;
            }
            return options;
        },
        titleOptions() {
            let { name } = this.allFields;
            const options = this.fieldsList
                .filter((field) => {
                    const { define_type, type, api_name } = field;
                    if (api_name === 'name') {
                        return false;
                    }
                    if (type !== 'text') {
                        return false;
                    }
                    if (define_type === 'system') {
                        return SHOW_SYSTEM.includes(api_name);
                    }
                    if (define_type === 'package') {
                        return !HIDE_PACKAGE.includes(api_name);
                    }
                    return true;
                })
                .map((field) => ({ label: field.label, value: field.api_name }));
            if (name) {
                options.unshift({
                    value: name.api_name,
                    label: name.label,
                });
            }
            if (!this.titleVal && options.length) {
                this.titleVal = options[0].value;
            }
            return options;
        },
        priceOptions() {
            // 价目表价格 + currency类型字段
            let { virtual_price_book_price } = this.allFields;
            const options = this.fieldsList
                .filter(
                    (field) => field.type === 'currency'
                ).map(field => ({
                    value: field.api_name,
                    label: field.label,
                }));
            if (virtual_price_book_price) {
                options.unshift({
                    value: virtual_price_book_price.api_name,
                    label: virtual_price_book_price.label,
                });
            }
            // 首选价目表价格
            if (!this.priceVal && options.length) {
                this.priceVal = options[0].value;
            }
            return options;
        },
        tagOptions() {
            let field = this.allFields.commodity_label;
            const unshow = {
                label: $t('不显示'),
                value: 'null',
            };
            const options = field && field.options || [];
            !this.tagVal && (this.tagVal = unshow.value);
            return [unshow, ...options];
        },
    },
    watch: {
        curView() {
            this.setProps(this.api_name, props => {
                props[this.saveViewKey] = this.curView;
            })
        },
        defaultView() {
            this.setProps(this.api_name, props => {
              props['default_view'] = this.defaultView;
            });
        },
        pictureVal() {
            this.setProps(this.api_name, props => {
                props.card_main_info[this.savePictureKey] = this.pictureVal;
            })
        },
        titleVal() {
            this.setProps(this.api_name, props => {
                props.card_main_info[this.saveNameKey] = this.titleVal;
            })
        },
        priceVal() {
            this.setProps(this.api_name, props => {
                props.card_main_info[this.savePriceKey] = this.priceVal;
            })
        },
        tagVal() {
            this.setProps(this.api_name, props => {
                props.card_main_info[this.saveTagKey] = this.tagVal;
            })
        },
        isTagShow() {
            this.setProps(this.api_name, props => {
                props.card_main_info[this.saveIsTagShowKey] = this.isTagShow;
            })
        },
        showFields(val) {
            this.setProps(this.api_name, props => {
                props.card_main_info[this.saveShowFieldsKey] = this.showFields;
            })
        },
    },
    async created () {
        try {
            await this.initFetch();
        } catch (error) {
            console.error(error);
        } finally {
            this.initData();
        }
    },
    methods: {
        initData() {
            let {view_mode, is_card_init, is_spu_mode, default_view} = this.$attrs.props;
            this.curView = view_mode;
            this.defaultView = default_view || 'card';
            this.isCardInit = is_card_init;
            // 设置当前商品模式
            this.setProps(this.api_name, props => {
                props.is_spu_mode = this.isSpuMode;
            });
            // 已经配置过，且模式改变，发出提示
            if (!is_card_init && is_spu_mode !== this.isSpuMode) {
                CRM.util.alert($t("dht.shopmall_widget.mode_changed_tip"));
                this.resetVal();
                return;
            }
            this.setDefalutVal();
        },
        resetVal() {
            // 其他字段会在computed里面重置
            // 重置配置的字段和标签显示
            this.setProps(this.api_name, props => {
                props.card_main_info[this.saveShowFieldsKey] = this.showFields;
                props.card_main_info[this.saveIsTagShowKey] = this.isTagShow;
            })
        },
        render() {
            this.allShowFields = this.formatAllFields();
            this.isLoading = false;
        },
        setDefalutVal() {
            let { card_main_info } = this.$attrs.props;
            if(!_.isEmpty(card_main_info)) {
                let {
                    picture_apiname,
                    name_apiname,
                    price_apiname,
                    tag_apiname,
                    is_tag_show,
                    show_fields
                } = card_main_info;
                this.pictureVal = picture_apiname;
                this.titleVal = name_apiname;
                this.priceVal = price_apiname;
                this.tagVal = tag_apiname;
                this.isTagShow = is_tag_show;
                this.showFields = show_fields;
            }
        },
        changeHandle(val, key) {
            this.setProps(this.api_name, props => {
                if (key === this.saveViewKey) {
                    props[key] = val;
                } else {
                    props.card_main_info[key] = val;
                }
            })
        },
        setCardHandle() {
            this.isCardInit = false;
            this.setProps(this.api_name, props => {
                props.is_card_init = this.isCardInit;
            })
        },
        formatAllFields() {
            let result = [];
            _.each(this.allFields, (obj, key) => {
                // 过滤不外露的字段和不宜展示的的类型
                if(!HIDE_FIELDS.includes(key) && !HIDE_FIELD_TYPES.includes(obj.type)) {
                    result.push({
                        key: obj.api_name,
                        label: obj.label,
                    });
                }
            })
            return [{
                title: $t("对象字段"),
                items: result
            }];
        },
        // 保存及校验
    }
})
});