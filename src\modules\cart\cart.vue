<script lang="ts">
import { getCrmListComponent } from '../../base/bridge';
import BaseMixin from '../mixins/base';
import { ext } from './list';

const Component: VueComponent = {
  name: 'DhtModuleCart',

  mixins: [BaseMixin],

  data() {
    return {
      objectApiName: 'ShoppingCartObj',
    };
  },

  methods: {
    renderComponent(): Promise<void> {
      return getCrmListComponent(this.objectApiName, '', ext)
        .then((Ctor: BackboneComponent | null) => {
          if (!Ctor) {
            this.isEmpty = true;
            return;
          }
          this.$$c.list = new Ctor({
            wrapper: $(this.$el),
            isEdit: false,
          });
          this.$$c.list.render([this.objectApiName]);
        })
        .catch((err) => {
          console.error(err);
          this.isError = true;
        });
    },
  },
};

export default Component;
</script>
