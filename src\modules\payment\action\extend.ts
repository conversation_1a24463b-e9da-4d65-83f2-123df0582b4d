import { modelExtend } from './model/model';
// import { viewExtend } from './view/view';

export function actionExtend(Action: BackboneComponent): BackboneComponent {
  // const proto = Action.prototype;
  let paymentobjModule = 'crm-modules/action/paymentobj/paymentobj';
  if (CRM.util.isGrayScale('CRM_PAYMENT_V2')) {
    paymentobjModule = 'crm-modules/action/paymentobj_v2/paymentobj';
  }

  return Action.extend({
    /**
     * 新建回款
     */
    add(obj: any) {
      const me = this;
      CRM.util.waiting();
      Fx.async([
        'crm-modules/action/field/field',
        paymentobjModule,
      ], (Field: BackboneComponent, Payment: BackboneComponent) => {
        CRM.util.waiting(false);

        const payment = new Payment();
        payment.add(_.extend(obj, {
          View: Payment.View || Field.View,
          Model: modelExtend(Payment.Model || Field.Model),
          success: (data: any) => {
            me.trigger('refresh', 'add', data);
            // 这里比标准的多加了一个 data._id 控制条件
            // 原因：线上付款的回款 是 支付成功后由支付那边发起的创建，前端拿不到回款创建成功后信息，所以也显示不了详情
            !data.__isContinue && data._id && obj.showDetail && me.showDetail({
              id: data._id,
              apiname: me.apiName,
            });
          },
        }));
      });
    },

  });
}
