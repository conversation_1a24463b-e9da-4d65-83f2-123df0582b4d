// @vue/component
import AddToCartMixins from '../../../mixins/add-to-cart-mixins';
import UnitSelector from '../unit-selector/unit-selector.vue';
import FkInputNumber from '../fk-input-number/fk-input-number.vue';

export default Vue.extend({
  name: 'QuantityInput',

  components: {
    UnitSelector,
    FkInputNumber,
  },

  mixins: [AddToCartMixins],

  props: {
    product: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      quantity: '',
    };
  },

  computed: {
    precision() {
      const value = window.$dht.config.meta.ShoppingCartObj.quantityPrecision;
      return typeof value === 'number' ? value : null;
    },
  },

  watch: {},

  created() {},

  methods: {
    cartAction(product: any) {
      const me = this as any;
      if (!me.quantity) {
        return;
      }
      let startPosition: any = null;
      if (document.querySelector('#dht-carts-entrance')) {
        // 仅在入口的情况下才显示动画
        startPosition = me.$el.getBoundingClientRect();
      }
      product.quantity = me.quantity;
      me.addProductToCart(product, startPosition).then(() => {
        me.quantity = '';
      });
    },
    /**
     * 选择单位改变
     * @param unit: 选择的单位
     * @param selectSku: 当前产品
     */
    unitChange(unit: any, product: any) {
      product._selectUnit = unit;
    },

    blur() {
      const me = this as any;
      if (me.$refs.fkInput) {
        me.$refs.fkInput.blur();
      }
    },

    focus() {
      const me = this as any;
      if (me.$refs.fkInput) {
        me.$refs.fkInput.focus();
      }
    },
  },
});
