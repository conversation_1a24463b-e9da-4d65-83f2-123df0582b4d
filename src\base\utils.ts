export function getDomain() {
  let n = window.location.hostname.split('.');
  n = n.splice(-2, 2);
  return n.join('.');
}

/**
 * 从当前Hash里解析参数
 */
export function parseParams(): string[] {
  let hash = window.location.hash;
  hash = hash.substr(1);
  let array = hash.split('/');
  const path = array[array.length - 1];
  if (/^dht-api/.test(path)) {
    array = path.split('-');
    // 错误的：dht-api-=-SalesOrderObj-record_fCKrP__c
    // 正确的：dht-api-SalesOrderObj-record_fCKrP__c
    return array.slice(path.indexOf('-=-') >= 0 ? 3 : 2);
  }
  return [];
}

/**
 * 依赖webpack的`require.context`全量引入模块
 * @param require require.context的返回值
 * @param callback 回调函数
 */
export function imports(require: any, callback: (component: any) => any) {
  require.keys().forEach((key: string) => {
    callback(require(key)); // eslint-disable-line import/no-dynamic-require
  });
}

/**
 * 将路由路径转成模块名，如对应的模块路由hash为`/sail/product`时：
 * 则对应的模块名为：`sail-product`
 * @param _path 路由路径
 */
export function convertPath2ModuleName(_path: string) {
  const path = _path.charAt(0) === '/' ? _path.substr(1) : _path;
  return path.split('/').join('-');
}

/**
 * 将URL参数转成具体格式，如：
 * ```
 * {a:1,b:2} => =/1/2
 * ```
 * @param params URL参数
 */
export function strUrlParams(params: Record<string, any> = {}): string {
  if (_.isEmpty(params)) return '';

  const array = Object.keys(params)
    .reduce((res: string[], key: string) => {
      if (params[key]) {
        res.push(String(params[key]));
      }
      return res;
    }, ['=']);
  return array.join('/');
}

export function getHashSchema(appId: string, path: string, params?: Record<string, any>) {
  const strParams = strUrlParams(params);
  return !strParams ? `/portal${appId}/${path}` : `/portal${appId}/${path}/${strParams}`;
}

/**
 * 获取CRM模块对象
 * @param path CRM对象路径，一般对应apiname
 * @param options 可选配置参数
 */
export function getCrmModuleComponent(
  path: string,
  { dirname = 'page', base = 'modules', use = false },
): Promise<BackboneComponent> {
  const ipath = use ? path : `crm-${base}/${dirname}/${path}/${path}`;
  return new Promise((resolve) => {
    Fx.async(ipath, (component: BackboneComponent) => {
      resolve(component);
    });
  });
}

/**
 * 获取CRM的css模块对象
 * @param path CRM对象路径，一般对应apiname
 * @param dirname CRM对象目录，一般同path
 * @param base 基础目录
 * @deprecated
 */
export function getCrmCssComponent(path: string, dirname = 'style', base = 'assets'): Promise<null> {
  return new Promise((resolve, reject) => {
    Fx.async(`crm-${base}/${dirname}/${path}.css`, (component: null) => {
      resolve(component);
    });
  });
}

/**
 * 获取BI模块对象
 * @param path BI对象路径，一般对应apiname
 * @param options 可选配置参数
 * @deprecated
 */
export function getBiModuleComponent(
  path: string,
  { dirname = 'page', base = 'modules', use = false },
): Promise<BackboneComponent> {
  const ipath = use ? path : `bi-${base}/${dirname}/${path}/${path}`;
  return new Promise((resolve) => {
    Fx.async(ipath, (component: BackboneComponent) => {
      resolve(component);
    });
  });
}

/**
 * 获取BI的css模块对象
 * @param path BI对象路径，一般对应apiname
 * @param dirname BI对象目录，一般同path
 * @param base 基础目录
 */
export function getBiCssComponent(path: string, dirname = 'style', base = 'assets'): Promise<null> {
  return new Promise((resolve, reject) => {
    Fx.async(`bi-${base}/${dirname}/${path}.css`, (component: null) => {
      resolve(component);
    });
  });
}

/**
 * 根据字符串、字符串数组创建map
 * @param items
 * @param delimiter 默认值/\s+/
 * @param map
 */
export function makeMap(_items: any[] | string, delimiter: string, _map: any) {
  let items = _items || [];
  if (typeof items === 'string') {
    items = items.split(delimiter || /\s+/);
  }

  const map = _map || {};
  for (let i = items.length - 1; i >= 0; i -= 1) {
    map[items[i]] = true;
  }
  return map;
}

export function supportProxy() {
  return ['Proxy', 'Reflect'].every((support: string) => !!(<any>window)[support]);
}

export function defineObject<T extends object>(value: T, handler: ProxyHandler<T>) {
  const esnext = supportProxy();
  return esnext ? new Proxy(value, handler) : value;
}
