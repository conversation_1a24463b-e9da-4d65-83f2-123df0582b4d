import { floatAdd, floatMul } from '@gamma/common';
import { getComponent } from '../../../components';
import { LifeHooks } from '../../../base/hooks';

export function cartMdExtend(MD: BackboneComponent): BackboneComponent {
  let parentMd = MD;
  const proto = parentMd.prototype;

  parentMd = parentMd.extend({
    /**
     * @override
     */
    initialize() {
      proto.initialize.apply(this, arguments);
      // 保存当前表格实例
      this.cartTable = null;
      // 保存选择的产品，不包括赠品
      this.cartCheckedData = [];
      // 窗口resize动态调整表格高度，保证统计信息永远在底部
      this.onCartWindowResize = () => {
        this.$('.j-table-wrap').height($('body').height() - 165);
      };
      // 监听窗口变化
      $(window).on('resize', this.onCartWindowResize);
    },

    /**
     * @override
     * 修改MD标题为‘购物车’
     */
    afterInitialize() {
      // TODO 处理购物车国际化问题
      this.detailObjectList[0].related_list_label = $t('购物车');
    },

    /**
     * 获取购物车服务
     */
    getCartService() {
      return window.$dht.services.cart;
    },

    /**
     * @override
     * 解析表格配置
     * @param opts：选项
     * @param table：明细表格
     */
    parseTableOptions(opts: any, table: any) {
      const options = proto.parseTableOptions.apply(this, arguments);
      // 不显示表格的term
      options.showTermBatch = false;
      options.termBatchPos = 'C';
      // 表格高度不自动变化
      options.autoHeight = null;
      options.maxHeight = null;
      options.height = null;
      // 设置勾选记忆数据
      options.checked = {
        idKey: 'cartId',
        data: [],
      };
      // 设置表格初始化高度
      options.$el.height($('.paas-cart-container').height() - 108);
      return options;
    },

    /**
     * 解析表格数据，禁用某些列编辑。
     * @override
     * @param columns
     * @param opts
     * @param table
     */
    parseTableColumns(columns:any, opts:any, table:any) {
      const myColumns = proto.parseTableColumns.apply(this, arguments);

      // 不可编辑字段：价格、销售价格、折扣、小计、是否赠品、价目表id、价目表明细id, 产品id
      const disableFields: Record<string, boolean> = {
        product_price: true,
        sales_price: true,
        discount: true,
        subtotal: true,
        is_giveaway: true,
        price_book_id: true,
        price_book_product_id: true,
        product_id: true,
      };
      // Currency字段需要添加币种符号
      const currencyFields: Record<string, boolean> = {
        product_price: true,
        sales_price: true,
        subtotal: true,
        price_book_price: true,
      };
      _.each(myColumns, (column: any) => {
        if (disableFields[column.api_name]) {
          column.isEdit = false;
        }
        if (currencyFields[column.api_name]) {
          column.render = (data: any) => {
            return $dht.config.currency.currencyFlag + data;
          };
        }
      });
      return myColumns;
    },

    /**
     * @override
     * 删除拖拽和复制按钮
     */
    getHandColunmBtns() {
      const buttons = proto.getHandColunmBtns.apply(this, arguments);
      if (!CRM._cache.advancedPricing) {
        return buttons.filter((item: any) => !item.isDrag && (item.action !== 'trCopyHandle'));
      }
      return buttons;
    },

    /**
     * @override
     * md表格渲染完成，需要保存表格对象、恢复上次选择的产品、更新底部统计信息
     * @param table
     */
    renderTableComplete(table:any) {
      // 保存表格对象
      this.cartTable = table;
      proto.renderTableComplete.apply(this, arguments);
      // 恢复上次选择
      this._setDefaultSelected(table);
      // 更新底部统计信息
      this.updateFooterData(table);
    },

    /**
     * @override
     * 渲染完成回调，屏蔽掉主对象信息，只保留明细，
     * 并且初始化购物车底部统计信息组件
     */
    renderAfter() {
      proto.renderAfter && proto.renderAfter.apply(this, arguments);
      // 屏蔽主对象信息
      this.$('.md-head').hide();
      this.$('.md-full').hide();
      this.$('.master-policy-content').hide();
      this.$('.order_promotion_container').hide();
      this.$('.nmd-nav .j-nav-item').css('border-bottom', 'none');
      // 初始化底部统计信息组件
      this.initFooter();
      // 初始化业务类型组件：如果可售范围设置了业务类型，则购物车不需要有切换业务组件的类型
      if (!$dht.config.newAvailableRangeFilter.isEnable || $dht.config.newAvailableRangeFilter.filter_field !== 'record_type') {
        this.initRecordTypeSelect();
      }
    },

    /**
     * 单元格编辑之前，常用单位不允许编辑
     * @param opts
     * @param table
     * @param next
     */
    beforeEditTableCellHandle(opts: any, table: any, next: any) {
      const apiName = opts.column.api_name;
      const isCommonUnit = opts.data.is_common_unit;
      if ((apiName === 'actual_unit' || apiName === 'other_unit') && isCommonUnit) {
        next(false);
      } else {
        proto.beforeEditTableCellHandle(opts, table, next);
      }
    },

    /**
     * 表格单元格编辑完回填之前的回调，对于数量和多单位的编辑需要落库
     * next(false)回到之前的值
     * @override
     * @param opts
     * @param table
     * @param next
     */
    beforeEditFillHandle(opts: any, table: any, next: Function) {
      const apiName = opts.column.api_name;
      const isEditQuantity = apiName === 'quantity';
      const isEditActualUnit = apiName === 'actual_unit';

      if (isEditQuantity || isEditActualUnit) {
        const quantity = isEditQuantity ? +opts.data : +opts.cellData.quantity;
        const unit = isEditActualUnit ? opts.data : opts.cellData.actual_unit;
        const qau = { quantity, unit };

        // 清空数量和单位时直接返回
        const isClearQuantity = isEditQuantity && +quantity === 0;
        const isClearUnit = isEditActualUnit && !unit;
        if (isClearQuantity || isClearUnit) {
          CRM.util.alert($t('单位和数量不可为空！'));
          next(false);

          return;
        }

        this.getCartService().updateQuantityAndUnit(opts.cellData.cartId, qau).then(() => {
          proto.beforeEditFillHandle.call(this, opts, table, next);
        }).catch((err: any) => {
          console.error(err);
          next(false);
        });
      } else {
        proto.beforeEditFillHandle.call(this, opts, table, next);
      }
    },

    /**
     * 删除购物车一行或者多行
     * @override
     * @param table: 明细表格
     * @param index {number | Array<number>>}
     * @param noCalculate: 是否需要计算
     */
    delRow(table: any, index: any, noCalculate = false, needTip = true) {
      // 统一转化成数组
      const allList = this.getTableCurData(table);
      const cartIds = [];
      if (!Array.isArray(index)) {
        cartIds.push(allList[index].cartId);
      } else {
        index.forEach((idx) => {
          cartIds.push(allList[idx].cartId);
        });
      }
      // 先批次删除购物车数据,再删除表格数据
      return this.getCartService().batchRemove(cartIds).then(() => {
        proto.delRow.call(this, table, index, noCalculate);
        if (needTip) {
          Vue.prototype.$message({ message: $t('删除成功'), type: 'success' });
        }
      });
    },

    /**
     * 删除购物车多行数据
     * @param cartIds: 购物车id数组
     * @private
     */
    _deleteCartRows(cartIds: Array<string>, needTip = true) {
      const allList = this.getTableCurData(this.cartTable);
      const indexArr = cartIds.map((cartId: string) => {
        return allList.findIndex((item: any) => item.cartId === cartId);
      });
      return this.delRow(this.cartTable, indexArr, false, needTip);
    },

    /**
     * 点击表格左侧checkbox事件，需要同步勾选其赠品，并更新底部信息
     * 其选择状态需要落库以便同步小程序
     * @override
     * @param table
     */
    updateLeftCheckState(table: any) {
      // 上次勾选数据，不包括赠品
      const oldCheckData = this.cartCheckedData || [];
      // 本次勾选数据，不包括赠品
      const newCheckData = (table.getCheckedData() || []).filter((item: any) => item.is_giveaway !== '1');
      // 获取哪些是新勾选的，哪些是取消的
      const {
        selectCartIds,
        unSelectCartIds,
        selectProducts,
        unSelectProducts,
      } = this._getDiffSelected(oldCheckData, newCheckData);
      const tableData = this.getTableCurData(table);
      // 新选择的赠品
      const selectedGifts = [];
      // 取消选则的赠品
      const unselectedGifts = [];
      if (selectCartIds.length) {
        // 选择需要落库
        this.getCartService().batchUpdateSettled(selectCartIds, true);
        selectedGifts.push(...this.getProductsGifts(selectProducts, tableData));
      }
      if (unSelectCartIds.length) {
        // 反选选择需要落库
        this.getCartService().batchUpdateSettled(unSelectCartIds, false);
        unselectedGifts.push(...this.getProductsGifts(unSelectProducts, tableData));
      }
      // 勾选赠品
      if (selectedGifts.length) {
        table.setCheckedRow('cartId', selectedGifts);
      }
      // 反选赠品
      if (unselectedGifts.length) {
        table.table.setUncheckedRow('cartId', unselectedGifts);
      }
      proto.updateLeftCheckState(table);
      // 更新底部信息
      this.updateFooterData(table);
    },

    /**
     * @override
     * 在表格上进行删除添加操作时调计算接口的回调函数
     * @param res
     * @param table
     * @param isAdd
     */
    afterDatasToTableCal(res:any, table:any, isAdd:any) {
      proto.afterDatasToTableCal.apply(this, arguments);
      if (!isAdd) {
        // 删除时，更新统计信息
        this.updateFooterData(table);
      }
    },

    /** 每次明细被更新的时候会调用，此时需要更新底部统计信息
     * @override
     * @param data
     * @param table
     */
    updateTableByData(data:any, table:any) {
      proto.updateTableByData.apply(this, arguments);
      if (!_.isEmpty(data)) {
        this.updateFooterData(table);
      }
    },

    /**
     * @override
     * 设置表格数据之前添加默认值及补充多单位信息
     * @param mdDatas {Record<string, any[]>}： 明细数据映射
     */
    async handleMdDataBeforeRender(mdDatas: Record<string, any[]>) {
      let products = mdDatas.SalesOrderProductObj;
      // 补充默认值
      const mdDefaultData = this.model.get('_mdDefaultData') || {};
      const defaultValue = mdDefaultData.SalesOrderProductObj || {};
      if (Object.keys(defaultValue).length) {
        products = products.map((item: any) => {
          return _.extend({}, defaultValue, item);
        });
      }
      mdDatas.SalesOrderProductObj = products;
      // 调用父级的handleMdDataBeforeRender，价格政策需要进行计算
      const result = await proto.handleMdDataBeforeRender.call(this, mdDatas);
      return result;
    },

    /**
     * @override
     * 价格政策变化后赠品可能变化，需要改变赠品选择状态
     */
    afterPolicyProcess() {
      proto.afterPolicyProcess.apply(this, arguments);
      if (this.cartTable) {
        // 先清除表格记录的赠品
        this._clearRemberGift(this.cartTable);
        // 再次计算一次需要选择的赠品
        this._setDefaultSelected(this.cartTable);
        this.updateFooterData(this.cartTable);
      }
    },

    /**
     * 初始化底部统计信息组件
     */
    initFooter() {
      const $vue = this.$$vue;
      if ($vue.footer) {
        // 如果组件存在则销毁
        $vue.footer.$destroy();
        $vue.footer = null;
      }
      // 获取购物车底部信息组件
      getComponent('CartFooter2').then((Ctor: any) => {
        if (Ctor) {
          // 创建信息组件
          const wrapper = document.createElement('div');
          const $targets = $('.paas-cart-container');
          $targets[0].append(wrapper);
          $vue.footer = new Ctor();
          $vue.footer.$mount(wrapper);
          // 监听底部信息组件抛出的事件
          this._addFooterListener($vue.footer);
        }
      });
    },

    /**
     * 初始化切换业务类型组件
     */
    initRecordTypeSelect() {
      const $vue = this.$$vue;
      if ($vue.recordtype) {
        // 如果组件存在则销毁
        $vue.recordtype.$destroy();
        $vue.recordtype = null;
      }
      const defaultVal = this.getData('record_type');
      const field = this.get('fields').record_type;
      // 获取订单业务类型组件
      getComponent('CartRecordType').then((Ctor: any) => {
        if (Ctor) {
          // 创建信息组件
          const wrapper = document.createElement('div');
          const $targets = $('.nmd-nav');
          $targets[0].append(wrapper);
          $vue.recordtype = new Ctor({
            propsData: { defaultVal, field },
          });
          $vue.recordtype.$mount(wrapper);
        }
      });
    },

    /**
     * 监听化底部信息组件抛出的事件，包括全选、结算、全部删除事件
     * @private
     * @param footer: 底部信息组件对象
     */
    _addFooterListener(footer: any) {
      // 全选或者反选
      footer.$on('select-change', () => {
        const targets = this.$('.j-all-checkbox');
        // 模拟md表格的全选按钮点击事件来触发全选或者反选
        if (targets && targets.length > 0) {
          const e = document.createEvent('MouseEvents');
          e.initEvent('click', true, true);
          targets[0].dispatchEvent(e);
        }
      });

      // 购物车结算事件
      footer.$on('order-create', async () => {
        if (!this.cartTable) return;
        // 获取选择的产品、赠品
        const checkedList = $.extend(true, [], this.cartTable.getCheckedData() || []);
        if (!checkedList || checkedList.length <= 0) {
          Fx.util.error($t('请至少选择一件商品结算'));
          return;
        }
        // 提交订单之前再次计算一次价格政策
        const { masterData, detailData } = await this.beforeSubmitToSailPage(checkedList);
        // 跳转到销售订单页面
        window.$dht.createOrder({
          apiname: 'SalesOrderObj',
          displayName: $t('销售订单'),
          source: 'cart', // 标识订货通购物车来源
          _from: 'cart', // 标识需要恢复购物车的价格政策
          showDetail: true, // 订单提交成功后显示订单详情
          noPreCalculate: true, // 标识不需要前期的预计算
          isSubmitAndCreate: false, // 不显示提交继续创建按钮
          record_type: this.model.get('record_type'), // 传入主对象的业务类型
          data: masterData, // 主对象数据
          mdData: { SalesOrderProductObj: detailData }, // 从对象数据
          success: (action: string, data: any, dataId: string, details: any[]) => {
            if (action === 'add') {
              // 提交成功后需要删除对应的购物车数据
              const ids = checkedList.map((item: any) => item.cartId);
              this._deleteCartRows(ids, false).catch((err: any) => {
                console.error(err);
              });
            }
            return Promise.resolve();
          },
        });
      });

      // 全部删除事件
      footer.$on('delete-selected', () => {
        // 选择的产品数据，不包括赠品
        const checkedList = this.cartCheckedData;
        const ids: string[] = checkedList.map((item: any) => item.cartId);
        this._deleteCartRows(ids).catch((err: any) => {
          console.error(err);
        });
      });
    },

    /**
     * 对于开启价格政策的，在跳转到订单页面时重新计算一次已选产品的政策
     * 对于没开启的，直接返回
     * @param products: any[]: 已选产品、赠品
     * @return {masterData: Object, detailData: Array<any>>}
     */
    async beforeSubmitToSailPage(products: any[]): Promise<any> {
      // 深复制主对象数据
      const masterData = $.extend(true, {}, this.get('data'));
      // 转化为产品映射对象
      const detailDataMap: Record<string, any> = {};
      products.forEach((product: any, index: number) => {
        const key = `${index}`;
        detailDataMap[key] = product;
      });

      CRM.util.waiting();
      // 计算主对象的数据
      const calculateMasterData = await this.calculateMasterDataBeforeSubmit(
        masterData, detailDataMap,
      );
      Object.assign(masterData, calculateMasterData);
      CRM.util.waiting(false);
      // 非价格政策直接返回
      return { masterData, detailData: this.convertStatUnitCount(products) };
    },
    /**
     * 统计单位只支持字符串类型，有些是数字类型会有问题
     * @param detailData
     */
    convertStatUnitCount(detailData: any[]) {
      detailData.forEach((item: any) => {
        item.stat_unit_count += '';
      });
      return detailData;
    },

    /**
     * 提交之前计算一次主对象的数据
     * @param masterData
     * @param detailDataMap
     */
    calculateMasterDataBeforeSubmit(masterData: any, detailDataMap: any) {
      const fieldsMap = this.getCalculateFieldsByFieldName(['quantity']);
      // eslint-disable-next-line max-len
      const SalesOrderObjFields = [].concat(fieldsMap.SalesOrderObj, this.getMasterCalculateFields());
      return new Promise((resolve) => {
        this.model.calculate({
          apiname: 'SalesOrderObj',
          data: masterData,
          detailDataMap: {
            SalesOrderProductObj: detailDataMap,
          },
          fields: {
            SalesOrderObj: _.uniq(SalesOrderObjFields),
          },
          _calculate_from: 'policy',
          modifiedObjectApiName: 'SalesOrderObj',
          modifiedDataIndexList: [],
          callback: (result: any) => {
            resolve(result.SalesOrderObj['0']);
          },
          error: (err: any) => {
            console.error(err);
          },
          fail: (err: any) => {
            console.error(err);
          },
        });
      });
    },

    getMasterCalculateFields() {
      const detailObjectList = this.model.get('detailObjectList');
      const detailObject = detailObjectList.find((item: any) => item.objectApiName === 'SalesOrderProductObj');
      const calculateRelation = detailObject.objectDescribe.calculate_relation;
      if (
        calculateRelation
        && calculateRelation.calculate_fields
        && calculateRelation.calculate_fields.SalesOrderObj
      ) {
        return calculateRelation.calculate_fields.SalesOrderObj;
      }
      return [];
    },

    /**
     * 更新底部统计信息
     * @param table
     */
    updateFooterData(table:any) {
      if (!this.$$vue.footer) return;
      // 保存选择的数据，不包括赠品
      const checkedList = (table.getCheckedData() || []).filter((item: any) => item.is_giveaway !== '1');
      this.cartCheckedData = checkedList;
      // 数量
      let quantity = 0;
      // 商品金额，原价
      let baseSubtotal = 0;
      // 结算金额，促销后的价格
      let subtotal = 0;

      const $allcheckbox = this.$('.j-all-checkbox');
      // 是否全选
      const isCheckedAll = $allcheckbox.hasClass('tb-checkbox-selected');
      // 是否半选
      const isIndeterminate = $allcheckbox.hasClass('tb-checkbox-half-selected');
      // 计算所有产品的金额
      if (Array.isArray(checkedList) && checkedList.length) {
        quantity = checkedList.length;
        _.each(checkedList, (item:any) => {
          const price = item.price_book_price == null ? item.product_price : item.price_book_price;
          baseSubtotal = floatAdd(baseSubtotal, floatMul(price, item.quantity));
          subtotal = floatAdd(subtotal, item.subtotal);
        });
      }
      // 更新信息
      this.$$vue.footer.updateCartSummary({
        total: quantity,
        amount: baseSubtotal,
        salesAmount: subtotal,
        isCheckedAll,
        isIndeterminate,
      });
    },

    /**
     * 清除上次记录的所有赠品信息，在政策切换赠品的时候上次的赠品会保留需要清除
     * @param table
     * @private
     */
    _clearRemberGift(table: any) {
      const giftRemberData = table.getRemberData().filter((item: any) => item.is_giveaway === '1');
      table.reduceRemberData(giftRemberData, true);
    },

    /**
     * 恢复购物车的默认选中，购物车退出后再次进入需要恢复上次的选择
     * @param table
     * @private
     */
    _setDefaultSelected(table: any) {
      // 所有表格数据
      const tableData = this.getTableCurData(table);
      // 所有产品
      const products = tableData.filter((item: any) => item.is_giveaway !== '1');
      // 默认选中的产品
      const defaultSelectProducts = products.filter((item: any) => item.is_settled);
      if (defaultSelectProducts.length) {
        // 根据产品获取赠品
        const gifts = this.getProductsGifts(defaultSelectProducts, tableData);
        defaultSelectProducts.push(...gifts);
        // 恢复上次的产品及其赠品的选择
        table.setCheckedRow('cartId', defaultSelectProducts);
      }
    },

    /**
     * 获取产品的赠品
     * @param products
     * @param allTableData
     */
    getProductsGifts(products: any[], allTableData: any[]) {
      if (CRM._cache.advancedPricing) {
        // eslint-disable-next-line max-len
        const productKeys = products.map((item: any) => item.prod_pkg_key).filter((key: any) => !!key);
        if (!productKeys.length) return []; // renderTableComplete时，是没有prod_pkg_key这个字段的
        const selectGifts = this.policyMediator.getGiftsOfProduct(productKeys, allTableData);
        // 为赠品添加购物车Id
        return this._createdCartIdForGifts(selectGifts);
      }
      // 不开启价格政策直接返回空数组
      return [];
    },

    /**
     * 为赠品生成购物车id，赠品默认是没哟购物车id的
     * 赠品cartId = 父产品Id + 赠品本身Id
     * @param selectGifts
     * @private
     */
    _createdCartIdForGifts(selectGifts: any[]) {
      selectGifts.forEach((item: any) => {
        if (!item.cartId) {
          item.cartId = `${item.parent_gift_key}_${item.product_id}`;
        }
      });
      return selectGifts;
    },

    /**
     * 获取新选的数据和反选的数据
     * @param oldCheckData: 上次选择的数据
     * @param newCheckData: 本次选择的数据
     * @private
     * @return {
     * selectCartIds: string[], 改变的新选择购物车Id
     * unSelectCartIds string[], 改变的反选的购物车Id
     * selectProducts: any[], 改变的新选择购物车产品
     * unSelectProducts: any[], 改变的反选的购物车产品
     * }
     */
    _getDiffSelected(oldCheckData: any[], newCheckData: any[]) {
      // 转化为map
      const oldCheckDataMap = oldCheckData.reduce((obj: Record<string, boolean>, item: any) => {
        obj[item.cartId] = item;
        return obj;
      }, {});
      const newCheckDataMap = newCheckData.reduce((obj: Record<string, boolean>, item: any) => {
        obj[item.cartId] = item;
        return obj;
      }, {});
      // 获取新选择的购物车id和被反选的购物车Id
      // eslint-disable-next-line max-len
      const { selectCartIds, unSelectCartIds } = this._getChangeSelected(oldCheckDataMap, newCheckDataMap);
      // 获取新选择的购物车产品
      const selectProducts = selectCartIds.map((key: string) => {
        const products = newCheckDataMap[key] as any;
        products.is_settled = true;
        return products;
      });
      // 获取反选的购物车产品
      const unSelectProducts = unSelectCartIds.map((key: string) => {
        const products = oldCheckDataMap[key] as any;
        products.is_settled = false;
        return products;
      });
      return { selectCartIds, unSelectCartIds, selectProducts, unSelectProducts };
    },

    /**
     * 获取改变的购物车Id，包括新选和反选的
     * @param oldCheckDataMap
     * @param newCheckDataMap
     * @private
     * @return {
     * selectCartIds: string[], 改变的新选择购物车Id
     * unSelectCartIds string[], 改变的反选的购物车Id
     * }
     */
    _getChangeSelected(
      oldCheckDataMap: Record<string, any>,
      newCheckDataMap: Record<string, any>,
    ) {
      const oldKeys = Object.keys(oldCheckDataMap);
      const newKeys = Object.keys(newCheckDataMap);
      const selectCartIds: string[] = [];
      const unSelectCartIds: string[] = [];
      oldKeys.forEach((key: string) => {
        if (!newCheckDataMap[key]) {
          unSelectCartIds.push(key);
        }
      });
      newKeys.forEach((key: string) => {
        if (!oldCheckDataMap[key]) {
          selectCartIds.push(key);
        }
      });
      return { selectCartIds, unSelectCartIds };
    },

    /**
     * @override
     */
    destroy() {
      proto.destroy.apply(this, arguments);
      // 取消窗口改变监听
      $(window).off('resize', this.onCartWindowResize);
    },
  });
  return parentMd.extend(LifeHooks());
}
