export interface ApiContext {
  appId: string;
  coverHeaders?: boolean;
}

export interface Api {
  fhhApi(opts: any, extend: any): Promise<any>,
  getUserMenus(
    params: { id: string; templeId: string },
    headers: any
  ): Promise<any>;

  getPortalTpls(params: { appId: string }): Promise<any>;

  getDownstreamEmployeeInfo(parmas?: any): Promise<any>;
}

/**
 * api请求url
 */
const apiConfig = {
  getUserMenus: '/EM6HWebPage/userMenu/getUserMenuByAppId',
};

/**
 * 接口返回值
 * @param response
 */
const retValIfSus = (response: any) => {
  if (response?.Result?.StatusCode === 0) {
    return response.Value;
  }

  return Promise.reject();
};

/* eslint-disable no-multi-spaces */
const modifyUrl = [
  // 销售订单
  '/SalesOrderObj/action/Add',                        // [订单] 新建
  // '/SalesOrderObj/controller/DescribeLayout',         // [订单] 布局
  // '/SalesOrderObj/controller/WebDetail',              // [订单] 详情
  // '/SalesOrderObj/controller/RelatedList',            // [订单] 相关列表
  // // 订单产品
  // '/SalesOrderProductObj/controller/List',            // [订单产品] 列表
  // '/SalesOrderProductObj/controller/RelatedList',     // [订单产品] 订单详情相关列表
  // // 发货单
  // '/DeliveryNoteObj/controller/List',                 // [发货单] 列表
  // '/DeliveryNoteObj/controller/RelatedList',          // [发货单] 相关列表
  // '/DeliveryNoteObj/controller/WebDetail',            // [发货单] 详情
  // '/DeliveryNoteObj/action/ConfirmReceipt',           // [发货单] 确认收货
  // // 发货单产品
  // '/DeliveryNoteProductObj/controller/List',          // [发货单产品] 列表
  // '/DeliveryNoteProductObj/controller/RelatedList',   // [发货单产品] 相关列表
  // '/DeliveryNoteProductObj/controller/WebDetail',     // [发货单产品] 详情
  // // 回款
  // '/PaymentObj/controller/List',                      // [回款] 列表
  // '/PaymentObj/controller/WebDetail',                 // [回款] 详情
  // '/PaymentObj/controller/DescribeLayout',            // [回款] 布局
  // '/PaymentObj/action/Add',                           // [回款] 新建
  // // 回款明细
  // '/OrderPaymentObj/controller/List',                 // [回款明细] 列表
  // '/OrderPaymentObj/controller/WebDetail',            // [回款明细] 详情
  // '/OrderPaymentObj/controller/DescribeLayout',       // [回款明细] 布局
  // '/OrderPaymentObj/action/Add',                      // [回款明细] 新建
  // // 客户地址
  // '/AccountAddrObj/controller/List',                  // [客户地址] 列表
  // '/AccountAddrObj/controller/RelatedList',           // [客户地址] 相关列表
  // '/AccountAddrObj/controller/WebDetail',             // [客户地址] 详情
  // '/AccountAddrObj/controller/DescribeLayout',        // [客户地址] 布局
  // '/AccountAddrObj/action/Add',                       // [客户地址] 新建
  // '/AccountAddrObj/action/Edit',                      // [客户地址] 编辑
  // // 联系人
  // '/ContactObj/controller/List',                      // [联系人] 列表
  // '/ContactObj/controller/RelatedList',               // [联系人] 相关列表
  // '/ContactObj/controller/WebDetail',                 // [联系人] 详情
  // '/ContactObj/action/Add',                           // [联系人] 新建
  // '/ContactObj/controller/DescribeLayout',            // [联系人] 布局
  // // 开票申请
  // '/InvoiceApplicationObj/controller/DescribeLayout', // [开票申请] 布局
  // '/InvoiceApplicationObj/action/Add',                // [开票申请] 新建
  // // 客户账户余额
  // '/NewCustomerAccountObj/controller/List',           // [客户账户余额] 列表
  // '/NewCustomerAccountObj/controller/WebDetail',      // [客户账户余额] 详情
  // // 账户收支流水
  // '/AccountTransactionFlowObj/controller/List',       // [账户收支流水] 列表
  // '/AccountTransactionFlowObj/controller/WebDetail',  // [账户收支流水] 详情
  // // 广告
  // '/AdvertisementObj/controller/List',                // [广告] 列表
  // '/AdvertisementObj/controller/WebDetail',           // [广告] 详情
  // // 客户-财务信息（发票抬头）
  // '/AccountFinInfoObj/controller/DescribeLayout',     // [财务信息] 布局
  // '/AccountFinInfoObj/action/Add',                    // [财务信息] 新建
  // // 促销-促销产品
  // '/PromotionProductObj/controller/RelatedList',      // [促销详情] 布局
  // // 促销规则
  // '/PromotionRuleObj/controller/RelatedList',          // [促销规则] 相关列表
  // // 促销
  // '/PromotionObj/controller/WebDetail',                // [促销] 详情
];

/**
 * 默认api
 */
export const api: Api = {
  /**
   * 覆写crm标准的底层请求
   * 目的：修改url
   * 原代码目录：crm\crm2\assets\js\util\src\req.js fhhApi方法
   */
  fhhApi(opts: any, extend: any) {
    if (_.find(modifyUrl, (item: any) => { return opts.url.includes(item); })) {
      opts.url = opts.url.replace('HNCRM/', 'HDHT/');
    }
    return CRM.util.FHHApiStandard(opts, extend);
  },

  getUserMenus(params, headers) {
    return Fx.util
      .FHHApi({
        headers,
        data: params,
        url: apiConfig.getUserMenus,
      })
      .then((response) => retValIfSus(response));
  },

  getPortalTpls(params) {
    return Fx.util
      .FHHApi({
        url: '/EM1HWebPage/userPage/getUserPage',
        data: params,
      })
      .then((response) => retValIfSus(response));
  },

  getDownstreamEmployeeInfo(params?: any) {
    return Fx.util
      .FHHApi({
        url: '/EM6HWebPage/userPage/getDownstreamEmployeeInfo',
        data: params,
      })
      .then((response) => retValIfSus(response));
  },
};
