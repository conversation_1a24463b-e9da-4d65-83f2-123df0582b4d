<template>
    <!-- <RenderNodeToElement v-if="noWrapper" :style="inlineStyle" @mounted="onMounted"
        :class="`${statusCls} ${fullHeightCls}`" v-sticky="isSticky" :sticky-offset="`{ top: ${frameOffsetTop} }`">
    </RenderNodeToElement>
    <component v-else :is="onRender" :style="inlineStyle" :class="`${statusCls} ${fullHeightCls}`" v-sticky="isSticky"
        :sticky-offset="`{ top: ${frameOffsetTop} }`">
        <RenderNodeToElement @mounted="onMounted" :class="fullHeightCls"></RenderNodeToElement>
    </component> -->
    <RenderNodeToElement v-if="noWrapper" :style="inlineStyle" @mounted="onMounted"
        :class="`${statusCls} ${fullHeightCls}`">
    </RenderNodeToElement>
    <component v-else :is="onRender" :style="inlineStyle" :class="`${statusCls} ${fullHeightCls}`">
        <RenderNodeToElement @mounted="onMounted" :class="fullHeightCls"></RenderNodeToElement>
    </component>
</template>
<script lang="ts">
import { Vue } from '@beecraft/shared';
import RenderNodeToElement from './DefaultRender.vue';
import RenderWrapper from './RenderWrapper.vue';
import WrapperBar from './WrapperBar.vue';
// import sticky from '../shared/sticky';

function camelToLineThrough(str) {
    return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
}

export default {
    name: 'RenderNode',

    components: {
        RenderNodeToElement,
        RenderWrapper
    },

    directives: {
        // sticky
    },

    inject: ['useInternalEditor', 'useInternalNode'],

    created() {
        const { onRender, query, disableDefaultWrapper, frameOffsetTop, preprocessData } = this.useInternalEditor((state) => {
            return {
                disableDefaultWrapper: state.options.disableDefaultWrapper,
                onRender: state.options.onRender,
                frameOffsetTop: state.options.frameOffset?.top || 0,
                preprocessData: state.options.preprocessData
            }
        });
        const { style, noWrapper } = this.useInternalNode(node => {
            return {
                style: node.$$data.style,
                noWrapper: node.$$data.noWrapper,
            }
        });
        // const { style, noWrapper } = node;

        this.onRender = onRender || 'RenderWrapper';
        this.noWrapper = disableDefaultWrapper || noWrapper;
        // this.style = typeof style === 'function' ? style(node, { query }) : style;
        this.hidden = true;
        this.frameOffsetTop = frameOffsetTop;
        this.preprocessData = preprocessData;
    },

    methods: {
        onMounted() {
            // if(this.disabledConnect) {
            //     return '';
            // }
            if (this.enabled !== false) {
                this.checkElemIsExist().then(() => {
                    const { connectors } = this.useInternalNode();
                    connectors.connect(connectors.drag(this.targetElem));
                })
            }
        },
        initWrapperBar() {
            this._wrapperBar = new (Vue.extend(WrapperBar))({
                propsData: {
                    target: this.targetElem,
                    useInternalNode: this.useInternalNode,
                    useInternalEditor: this.useInternalEditor
                }
            }).$mount();
        },
        // TODO：异步组件渲染时不一定能获取到真实的dom，所以需要进行轮询去校验。思考更优雅的方式
        checkElemIsExist() {
            if (this.$el.nodeName === '#comment') {
                if (this._check) {
                    return this._check;
                }
                this._check = new Promise((resolve) => {
                    this._loop = setInterval(() => {
                        if (this.$children[0]?.$el.nodeName !== '#comment') {
                            clearInterval(this._loop);
                            this._loop = null;
                            this._check = null;
                            this.targetElem = this.$children[0]?.$el;
                            resolve(1);
                        }
                    }, 100);
                });

                return this._check;
            }
            this.targetElem = this.$el;
            return Promise.resolve();
        }
    },

    mounted() {
        if (this.enabled !== false) {
            this.checkElemIsExist().then(() => {
                this.initWrapperBar();
            });
        }
    },

    beforeDestroy() {
        if (this._wrapperBar) {
            this._wrapperBar.$destroy();
        }
        this.targetElem = null;
    },

    computed: {
        statusCls() {
            const { isHover, isActive, isDragged } = this.useInternalNode((node) => ({
                isHover: node.events.hovered,
                isActive: node.events.selected,
                isDragged: node.events.dragged
            }));

            let clsArr = [];

            if (!isDragged) {
                if (isActive) {
                    clsArr.push('component-selected');
                } else if (isHover) {
                    clsArr.push('component-hovered');
                }
            }

            return clsArr.join(' ');
        },

        fullHeightCls() {
            const { isFullHeight } = this.useInternalNode((node) => ({
                isFullHeight: node.data.isFullHeight
            }));

            return isFullHeight ? 'component-h-100' : '';
        },

        isSticky() {
            const { isSticky } = this.useInternalNode((node) => ({
                isSticky: node.$$data.isSticky
            }));

            return isSticky || false;
        },

        inlineStyle() {
            let result = '';
            let { node } = this.useInternalNode(node => {
                return {
                    node
                }
            });
            const pData = this.preprocessData ? this.preprocessData(node.data, { extra: node.extra }, this.useInternalEditor()) : node.data;
            const { style = {} } = pData;
            let { style: $$style = {}, keepDataStyle } = node.$$data;

            if ($$style.format) {
                let f$$style = $$style.format(node, this.useInternalEditor());
                $$style = Object.assign({}, $$style, f$$style);
            }

            if (keepDataStyle !== true) {
                Object.keys(style).forEach(selector => {
                    const val = style[selector];
                    if (typeof val !== 'string') {
                        return;
                    }
                    selector = camelToLineThrough(selector);

                    result += `--bc-c-${node.name}-${selector}: ${val};`;
                });
            }

            Object.keys($$style).forEach(selector => {
                const val = $$style[selector];
                if (selector === 'format') {
                    return;
                }
                if (typeof val !== 'string') {
                    return;
                }
                selector = camelToLineThrough(selector);

                result += `${selector}: ${val};`;
            });

            return result;
        },

        enabled(){
            return this.useInternalEditor((options) => {
                return {
                    enabled: options.enabled
                };
            }).enabled;
        }
    }
}
</script>
<style lang="less" scoped>
.component-selected,
.component-hovered {
    position: relative;

    &:after {
        content: '';
        --tw-border-opacity: 1;
        border-color: var(--color-primary06);
        display: block;
        height: 100%;
        pointer-events: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        box-sizing: border-box;
        z-index: 100;
    }
}

.component-hovered {
    &:after {
        border-style: dashed;
        border-width: 1px;
    }
}

.component-selected {
    &:after {
        border-style: solid;
        border-width: 1px;
    }
}

.component-dragged {
    position: relative;

    &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(100, 100, 100, 0.3);
        z-index: 1000;
    }
}

.component-h-100 {
    height: 100%;
}
</style>
