import ChooseSpec from '../components/product/choose-spec/choose-spec.vue';
import { cartAnimation, Position } from './create-cart-animation';

export default {

  components: {},

  props: {},

  data() {},

  computed: {},

  created() {},

  methods: {
    // eslint-disable-next-line max-len
    addProductToCart(products: any[] | any, animationPosition?: Position, ignoreValidationRule?: boolean) {
      const me = this as any;
      const productArr = Array.isArray(products) ? products : [products];
      if (!me.checkValid(productArr)) return Promise.reject();

      const service = window.$dht.getService('cart');
      return service.batchCreateOrUpdate(
        me.getSubmitParams(productArr),
        true,
        ignoreValidationRule,
      ).then(() => {
        if (animationPosition) {
          cartAnimation.play(productArr.length, animationPosition);
        } else {
          me.$message({
            message: $t('加入购物车成功！'),
            type: 'success',
            center: true,
          });
        }
      }).catch((err: any) => {
        if (err && err.isValidationRuleError) {
          const result = err.result || {};
          if (result.blockMessages && result.blockMessages.length > 0) {
            $dht.handleValidationBlockMessage(result.blockMessages);
            return;
          }
          if (result.nonBlockMessages && result.nonBlockMessages.length > 0) {
            $dht.handleValidationNonBlockMessage(result.nonBlockMessages)
              .then(() => {
                console.log('继续提交吧');
                this.addProductToCart(productArr, animationPosition, true);
              })
              .catch((error: any) => {
                console.log(`第${error}步取消了`);
              });
            return;
          }
        }
        me.$message({
          message: $t('加入购物车失败！'),
          type: 'error',
          center: true,
        });
      });
    },

    /**
     * 获取提交的产品
     * @param productArr
     */
    getSubmitParams(productArr: any[]): any[] {
      return productArr.map((item: any) => {
        return {
          product_id: item._id,
          quantity: item.quantity,
          unit: item._selectUnit ? item._selectUnit.unit_id : '',
        };
      });
    },

    checkValid(products: any[]) {
      const me = this as any;
      let isValid = true;
      for (const item of products) {
        if (+item.quantity < 0) {
          isValid = false;
          me.$message.error($t('数量不能小于0！'));
          break;
        }
      }
      return isValid;
    },

    chooseSpec(product: any) {
      $dht.modal.create({
        title: $t('选择规格'),
        minHeight: '300px',
        width: '520px',
        component: ChooseSpec,
        componentParams: {
          spuId: product.spu_id,
          defaultSpec: product.product_spec,
          // mode: 'single',
          mode: window.$dht.config.sail.isMultiAdd2Cart ? 'multi' : 'single',
        },
        okText: $t('加入购物车'),
        onOk: ((componentRef: any) => {
          return componentRef.save();
        }),
      });
    },
  },
};
