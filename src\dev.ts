import debug from 'debug';

const log = debug('@nsail:debugger');

log('******************************');
log(process.env.NODE_ENV);
log('******************************');

if (process.env.NODE_ENV === 'development') {
  Vue.config.devtools = process.env.NODE_ENV === 'development';
  Vue.config.errorHandler = (err: any, vm: any, info: any) => {
    log(err, vm, info);
    console.error(err, vm, info);
  };
  Vue.config.warnHandler = (err: any, vm: any, info: any) => {
    log(err, vm, info);
    console.warn(err, vm, info);
  };
}
