<template>
  <div v-if="error" class="portal-thirdapp-error">
    {{ $t('该页面不存在！') }}
  </div>
  <div v-else class="portal-thirdapp-content"></div>
</template>

<script lang="ts">
const Component: VueComponent = {
  name: 'PortalThirdapp',

  props: {
    parameters: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      error: false,
    };
  },

  methods: {
    initEnv(): any {
      return {
        el: $(this.$el),
        data: {
          apiname: this.$query('apiName'),
        },
        complete: (comp: BackboneComponent) => {
          this.comp = comp;
        },
        error: () => {
          this.shwoError();
        },
      };
    },

    shwoError() {
      this.error = true;
    },

    appRender() {
      const env = this.initEnv();
      [
        'crm.thirdpage.render', // 兼容crm
        'portal.thirdpage.render',
      ].forEach((eventName) => {
        (Fx as any).MEDIATOR.trigger(eventName, env);
      });
    },
  },

  mounted() {
    this.appRender();
  },
};

export default Component;
</script>

<style lang="less">
.portal-thirdapp-content {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
