/* 标签结构 */
.dht-tag {
  display: inline-block;
  min-width: 26px;
  height: 14px;
  line-height: 14px;
  border-radius: 2px;
  font-size: 12px;
  text-align: center;
}

/* 主标签：橙色背景白色文字 */
.dht-tag-primary {
  border: 1px solid #ff8000;
  background-color: #ff8000;
  color: #fff;
}

/* 页面模块不存在或加载错误 */
.dht-module-empty,
.dht-module-error {
  padding: 48px;
  font-size: 20px;
  text-align: center;
}

.clearfix:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
  overflow: hidden;
}

.dht-absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.td-promotion-tag(@text-color, @bg-color) {
  border: 1px solid @text-color;
  color: @text-color;
  background-color: @bg-color;
  font-size: 12px;
  padding: 0 1px;
  margin-right: 3px;
}
.dht-td-promotion-tag {
  &-new {
    .td-promotion-tag(#4bb674, #e0f4cc);
  }
  &-hot {
    .td-promotion-tag(#ff8000, #ffe4cf);
  }
}

.dht-cart-animation {
  position: fixed;
  display: inline-block;
  z-index: 1000;
  min-width: 20px;
  height: 16px;
  font-size: 12px;
  line-height: 16px;
  white-space: nowrap;
  text-align: center;
  background: #ff4d4f;
  border-radius: 3px;
  box-shadow: 0 0 0 1px #fff;
  color: white;
  transition: left .3s cubic-bezier(.48,.17,.83,.68), top .3s linear;
}

.dht-price-unit {
  font-size: 12px;
  color: #999;
}

.dht-text-ellipsis {
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
}

// 首页提示模块
.home-intro-content {
  line-height: 18px;
  margin-bottom: 10px;
}
.home-intro-note {
  line-height: 18px;
  font-size: 12px;
  color: #cbcbcd;
}

// 重置portal工具箱样式
.portal-header {
  > .tools {
    margin-right: 0;
    padding: 0 15px;
  }
}
