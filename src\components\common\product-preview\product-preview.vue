<template>
  <div class="dht-product-preview">
    <div class="dht-product-preview-poster"
         :class="{'one-picture': onlyOnePicture}"
         @mousemove="doZoom($event)"
         @mouseenter="doZoom($event)"
         @mouseleave="stopZoom">
      <img :src="curImageSrc"/>
      <div class="dht-product-preview-mask" v-show="zooming" :style="maskPosition"></div>
      <i v-show="!zooming" class="el-icon-search zoom-icon"></i>
    </div>
    <div class="dht-product-preview-zoom" v-show="zooming">
      <img :src="curBigImageSrc" :style="previewPosition">
    </div>
    <div v-if="!onlyOnePicture" :class="{'dht-product-preview-specs': imgList.length > 1}">
      <button
        v-if="imgList.length > 1"
        class="preview-carousel-arrow carousel-arrow-left"
        @click="goPrev">
        <i class="el-icon-arrow-left"></i>
      </button>
      <fx-carousel
        arrow="never"
        :autoplay="false"
        :loop="false"
        :indicator-position="'none'"
        ref="specCarousel">
        <fx-carousel-item v-for="(imgs, i) in imgList" :key="i">
          <div class="img-wrap clearfix">
            <div class="img-item" v-for="(img, j) in imgs" @mouseenter="selectSpec(i, j)" :key="j">
              <div class="img" :class="{'selected': selectedIndex.i === i && selectedIndex.j === j}"
                   :style="{backgroundImage:'url(' + img.min + ')'}"></div>
            </div>
          </div>
        </fx-carousel-item>
      </fx-carousel>
      <button
        v-if="imgList.length > 1"
        class="preview-carousel-arrow carousel-arrow-right"
        @click="goNext">
        <i class="el-icon-arrow-right"></i>
      </button>
    </div>
  </div>
</template>

<script src="./_product-preview.ts" lang="ts"></script>
<style src="./_product-preview.less" lang="less"></style>
