/**
 * 显示分步引导
 */
export async function showAppStepGuide() {
  const appGuideStatus = await Fx.store.getItem('dht.guide.home');
  const hash = window.location.hash;

  // 首页且没有提示过
  if (appGuideStatus === 1 || hash !== '#/portal/index') {
    return;
  }

  const intro = Fx.getComponent('intro');

  intro({
    steps: [
      {
        $target: $('.portal-navbar'),
        content: $t('dht.guide.home.navbar_title'),
        htmlContent: `
          <div class="home-intro-content">${$t('dht.guide.home.navbar_tip1')}</div>
          <div class="home-intro-content">${$t('dht.guide.home.navbar_tip2')}</div>
        `,
        width: 300,
        pos: 'right-start',
      },
      {
        $target: $('.enterprise-wrapper'),
        content: $t('dht.guide.home.enterprise_title'),
        htmlContent: `<div class="home-intro-content">${$t('dht.guide.home.enterprise_tip1')}</div>`,
      },
      {
        $target: $('.tools'),
        content: $t('dht.guide.home.tools_title'),
        htmlContent: `
          <div class="home-intro-content">${$t('dht.guide.home.tools_tip1')}</div>
          <div class="home-intro-note">${$t('dht.guide.home.notes')}</div>
        `,
        pos: 'left',
      },
      {
        $target: $('.portal-aside'),
        content: $t('dht.guide.home.portal_aside_title'),
        htmlContent: `
          <div class="home-intro-content">${$t('dht.guide.home.portal_aside_tip1')}</div>
          <div class="home-intro-note">${$t('dht.guide.home.portal_aside_note')}</div>
        `,
      },
      {
        $target: $('.portal-view'),
        content: $t('dht.guide.home.portal_view_title'),
        htmlContent: `
          <div class="home-intro-content">${$t('dht.guide.home.portal_view_tip1')}</div>
          <div class="home-intro-note">${$t('dht.guide.home.notes')}</div>
        `,
        pos: 'left',
        width: 220,
      },
    ],
    onComplete() {
      Fx.store.setItem('dht.guide.home', 1);
    },
  });
}
