import { LifeHooks } from '../../../../base/hooks';

export function discountExtend(Discount: BackboneComponent): BackboneComponent {
  const proto = Discount.prototype;

  /* eslint no-param-reassign: 0 */
  Discount = Discount.extend({
    // ****************** 覆写 *****************
    render() {
      proto.render.apply(this, arguments);
      this.disable();
    },
    matchPromotion(e: any, options: any) {
      proto.matchPromotion.apply(this, arguments);
      this.disable();
    },
  });

  Discount = Discount.extend(LifeHooks());

  return Discount;
}
