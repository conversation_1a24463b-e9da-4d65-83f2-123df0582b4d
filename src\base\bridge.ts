import debug from 'debug';

const log = debug('@nsail:monitor');

/**
 * CRM模块的请求参数
 * ```
 * crm-${base}/${dirname}/${path}.css
 * crm-${base}/${dirname}/${path}/${path}
 * ```
 */
export interface CrmComponentOptions {
  base?: string;
  dirname?: string;
  path: string;
}

/**
 * 获取CRM的JS模块对象
 * @param options 可选配置参数
 */
export function getCrmJsComponent(_options: CrmComponentOptions): Promise<BackboneComponent> {
  const options = {
    base: 'modules',
    dirname: 'page',
    ..._options,
  };
  return new Promise((resolve) => {
    Fx.async(
      `crm-${options.base}/${options.dirname}/${options.path}/${options.path}`,
      (component: BackboneComponent) => {
        resolve(component);
      },
    );
  });
}

/**
 * 获取CRM的css模块对象
 * @param options 可选配置参数
 */
export function getCrmCssComponent(_options: CrmComponentOptions): Promise<null> {
  const options = {
    base: 'assets',
    dirname: 'style',
    ..._options,
  };
  return new Promise((resolve) => {
    Fx.async(
      `crm-${options.base}/${options.dirname}/${options.path}.css`,
      (component: null) => {
        resolve(component);
      },
    );
  });
}

/**
 * 获取CRM的组件对象
 * @param jsOptions JS模块配置参数
 * @param cssOptions CSS模块配置参数
 */
export function getCrmComponent(
  jsOptions: CrmComponentOptions,
  cssOptions?: CrmComponentOptions,
): Promise<BackboneComponent> {
  return Promise.all([
    !cssOptions ? Promise.resolve(null) : getCrmCssComponent(cssOptions),
    getCrmJsComponent(jsOptions),
  ]).then((value: any[]) => value[1]);
}

/**
 * 针对CRM的列表组件做的扩展，可以是一个配置对象，也可以是一个函数支持将CRM列表组件传进去返回扩展后的列表组件
 */
export type CrmListExtension = Record<string, any> | ((c: BackboneComponent) => BackboneComponent);

/**
 * 加载CRM列表组件对象，依赖模块：
 * ```
 * crm-assests/style/page.css
 * crm-modules/page/list/list.js
 * ```
 *
 * 使用方式：
 * ```
 * // 第一个参数为空，表示使用通用的“list”组件
 * getCrmListComponent('SPUObj', 'SPUObj', function (List: BackboneComponent) {
 *   // 以下写法即同标准的Backbone组件扩展
 *   return List.extend({
 *     ...
 *   });
 * })
 * ```
 *
 * @param objectApiName 对象apiname
 * @param moduleName CRM对应的模块名，一般跟apiname一致，没有特殊实现时即为标准“list”，表示通用列表组件
 * @param extend 扩展函数或者扩展选项配置
 */
export function getCrmListComponent(
  _objectApiName: string,
  _moduleName: string,
  extend?: CrmListExtension,
): Promise<BackboneComponent | null> {
  const objectApiName = _objectApiName;
  const moduleName = _moduleName || 'list';
  const $cache = window.$dht._crmComponents;
  if ($cache[objectApiName]) {
    log(`Cache for crm component: ${objectApiName}`);
    return Promise.resolve($cache[objectApiName]);
  }
  log(`Get for crm component: ${objectApiName}`);
  const jsOptions = { dirname: 'page', path: moduleName.toLowerCase() };
  const cssOptions = { path: 'page' };
  return getCrmComponent(jsOptions, cssOptions)
    .then((ListCtor: BackboneComponent) => {
      if (!extend) {
        return ListCtor;
      }
      if (typeof extend === 'function') {
        return extend(ListCtor);
      }
      if (extend.options) {
        extend.options = _.extend({}, ListCtor.prototype.options, extend.options);
      }
      // 父类的方法暴露给子类：`__super__`
      return ListCtor.extend(extend);
    }).then((ListCtor: BackboneComponent) => {
      window.$dht._crmComponents[objectApiName] = ListCtor;
      return ListCtor;
    }).catch((err: any) => {
      console.error(err);
      throw err;
    });
}

function extendCrmActionComponent(actionCtor: BackboneComponent, extend: any):BackboneComponent {
  if (!extend) {
    return actionCtor;
  }
  if (typeof extend === 'function') {
    return extend(actionCtor);
  }
  if (extend.options) {
    extend.options = _.extend({}, actionCtor.prototype.options, extend.options);
  }
  // TODO 如何把父类的方法暴露给子类呢
  return actionCtor.extend(extend);
}

/**
 * 加载CRM新建组件对象，依赖模块：
 * ```
 * crm-assests/style/all.css
 * crm-modules/action/-/-.js
 * ```
 */
export function getCrmActionComponent(
  _objectApiName: string,
  extend?: CrmListExtension,
): Promise<BackboneComponent> {
  const objectApiName = _objectApiName || 'field';
  const objectApiNameAction = `${objectApiName}-action`;
  const $cache = window.$dht._crmComponents;
  if ($cache[objectApiNameAction]) {
    log(`Cache for crm component: ${objectApiNameAction}`);
    return Promise.resolve(extendCrmActionComponent($cache[objectApiNameAction], extend));
  }
  log(`Get for crm component: ${objectApiNameAction}`);
  const jsOptions = { dirname: 'action', path: objectApiName.toLowerCase() };
  const cssOptions = { path: 'all' };
  return getCrmComponent(jsOptions, cssOptions)
    .then((ActionCtor: BackboneComponent) => {
      $cache[objectApiNameAction] = ActionCtor;
      return extendCrmActionComponent(ActionCtor, extend);
    });
}

/**
 * 加载CRM产品列表组件对象，依赖模块：
 * ```
 * crm-assests/style/page.css
 * crm-modules/page/productobj/productobj.js
 * ```
 */
export function getCrmProductComponent() {
  const { isSpuMode } = $dht.config.sail;
  const objectApiName = isSpuMode ? 'SPUObj' : 'ProductObj';
  return getCrmComponent({ dirname: 'page', path: objectApiName.toLowerCase() }, { path: 'page' });
}

/**
 * 加载CRM订单列表组件对象，依赖模块：
 * ```
 * crm-assests/style/page.css
 * crm-modules/page/salesorderobj/salesorderobj.js
 */
export function getCrmSalesOrderComponent() {
  const objectApiName = 'SalesOrderObj';
  return getCrmComponent({ dirname: 'page', path: objectApiName.toLowerCase() }, { path: 'page' });
}

/**
 * 加载CRM订单组件对象，依赖模块：
 * ```
 * crm-assests/style/page.css
 * crm-modules/page/salesorderobj/salesorderobj.js
 */
export function getCrmOrderComponent() {
  const objectApiName = 'SalesOrderObj';
  return getCrmComponent({ dirname: 'page', path: objectApiName.toLowerCase() }, { path: 'page' });
}
