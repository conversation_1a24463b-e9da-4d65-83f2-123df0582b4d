interface PromotionPopoverOptions {
  productId: string;
  top: number;
  left: number;
}

const getPromotionPopoverComponent = () => import(
  /* webpackChunkName: "promotion-popover" */
  /* webpackMode: "lazy" */
  './popover.vue'
);

let $instance: VueComponent | null = null;

export async function showPromotionPopover(options: PromotionPopoverOptions) {
  if ($instance === null) {
    const Comp = await getPromotionPopoverComponent();
    const Ctor = Vue.extend(Comp.default);

    $instance = new Ctor({
      el: document.createElement('div'),
    });
    document.body.appendChild($instance!.$el);
  }
  $instance!.show(options);
}

export function hidePromotionPopover() {
  if ($instance === null) return;
  $instance.hide();
}
