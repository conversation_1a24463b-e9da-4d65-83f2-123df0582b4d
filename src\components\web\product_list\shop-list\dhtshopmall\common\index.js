define(function (require, exports, module) {

    const displayComponent = () => {
        return new Promise((resolve, reject) => {
            require.async(['./commonpre-vue', 'app-standalone-assets/style/all.css'], (Comp) => {
                resolve(Comp);
            });
        });
    };

    const settingComponent = () => {
        return new Promise((resolve, reject) => {
            require.async(['./commonsetting-vue', 'app-standalone-assets/style/all.css'], (Comp) => {
                resolve(Comp);
            });
        });
    };

    module.exports = {
        name: 'dht_shopmall_common',
        // 预览区组件
        displayComponent,
        // 设置区组件
        settingComponent,
        // 运行态组件
        // runningComponent,
    };
});
