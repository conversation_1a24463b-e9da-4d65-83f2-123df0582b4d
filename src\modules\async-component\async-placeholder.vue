<template>
  <component :is="curComponent" v-bind="$attrs"></component>
</template>

<script lang="ts">
import businessComponents from '../business';
import PortalPalceholder from '../placeholder/placeholder.vue';

const AsyncComponent: VueComponent = {
  data() {
    return {
      curComponent: null,
    };
  },

  methods: {
    getComponent(components: any[]) {
      const name = this.$attrs.module;
      let component = components[name];
      if (component) return component;

      const dirname: string = name.split('/')[0];
      component = components[`dirname_${dirname}` as any];

      return component;
    },

    updateContent(components: any) {
      const component = this.getComponent(components);
      this.curComponent = component || PortalPalceholder;
    },
  },

  created() {
    Fx.async(businessComponents, (...components: VueComponent[]) => {
      this.updateContent(Object.assign({}, ...components));
    });
  },
};

export default AsyncComponent;
</script>
