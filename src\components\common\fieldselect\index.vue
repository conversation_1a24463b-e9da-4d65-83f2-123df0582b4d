<template>
  <div class="field-select-input-wrapper">
    <div class="field-select-input" >
      <div class="input-display">
        <div class="selected-tags">
          <fx-tag
            v-for="item in displayItems"
            :key="item.id"
            effect="plain"
            type="info"
            size="small"
            closable
            @close.stop="removeItem(item)"
          >
            {{ item.name }}
          </fx-tag>
          <span @click="openSelector" v-if="selectedCount > maxDisplayCount" class="more-count">
            +{{ selectedCount - maxDisplayCount }}
          </span>      
          <fx-button
            class="add-btn"
            size="micro"
            type="text"
            icon="fx-icon-add-2"
            @click.stop="openSelector"
          >
            {{ addBtnText }}
        </fx-button>    
        </div>       
      </div>
      <!-- <i class="fx-icon-arrow-down arrow-icon"></i> -->
    </div>

    <!-- 字段选择弹框 -->
    <field-selector-dialog
      :visible.sync="showSelector"
      v-bind="selectorOptions"
      :title="dialogTitle"
      :value="selectedItemsList"
      @change="onSelectionChange"
      @sort-change="onSortChange"
      @confirm="onConfirm"
      @cancel="onCancel"
    />
  </div>
</template>

<script>
import FieldSelectorDialog from './dialog.vue'

export default {
  name: 'FieldSelector',

  components: {
    FieldSelectorDialog
  },

  props: {
    maxDisplayCount: {
      type: Number,
      default: 30
    },
    dialogTitle: {
      type: String,
      default: '选择字段'
    },
    selectorOptions: {
      type: Object,
      default: () => ({})
    },
    value: {
      type: Array,
      default: () => []
    },
    addBtnText: {
      type: String,
      default: '添加'
    }
  },

  data() {
    return {
      showSelector: false,
      selectedItemsList: [],
      displayItems: [],
      selectedCount: 0      
    }
  },

  watch: {
    value: {
      handler(newVal) {
        if (Array.isArray(newVal)) {
          this.selectedItemsList = [...newVal]
        } else {
          // 兼容对象结构，保留向下兼容
          const list = []
          const tabs = this.selectorOptions.tabs || []
          Object.keys(newVal || {}).forEach(tabId => {
            const tab = tabs.find(t => t.id === tabId)
            if (tab && newVal[tabId]) {
              newVal[tabId].forEach(itemId => {
                const item = tab.data.find(d => d.id === itemId)
                if (item) {
                  list.push({ ...item, tabId })
                }
              })
            }
          })
          this.selectedItemsList = list
        }
        this.updateDisplay()
      },
      immediate: true,
      deep: true
    }
  },

  methods: {
    openSelector() {
      this.showSelector = true
    },

    onSelectionChange(selectedItemsList) {
      this.selectedItemsList = [...selectedItemsList]
      this.updateDisplay()
      this.$emit('input', this.selectedItemsList)
      this.$emit('change', this.selectedItemsList)
    },

    onSortChange(sortedItemsList) {
      this.selectedItemsList = [...sortedItemsList]
      this.updateDisplay()
      this.$emit('input', this.selectedItemsList)
      this.$emit('sort-change', this.selectedItemsList)
      this.$emit('change', this.selectedItemsList)
    },

    onConfirm(selectedItemsList) {
      this.selectedItemsList = [...selectedItemsList]
      this.updateDisplay()
      this.showSelector = false
      this.$emit('input', this.selectedItemsList)
      this.$emit('confirm', this.selectedItemsList)
    },

    onCancel() {
      this.showSelector = false
      this.$emit('cancel')
    },

    removeItem(item) {
      const idx = this.selectedItemsList.findIndex(i => i.id === item.id && i.tabId === item.tabId)
      if (idx > -1) {
        this.selectedItemsList.splice(idx, 1)
        this.updateDisplay()
        this.$emit('input', this.selectedItemsList)
        this.$emit('change', this.selectedItemsList)
      }
    },

    updateDisplay() {
      this.selectedCount = this.selectedItemsList.length
      this.displayItems = this.selectedItemsList.slice(0, this.maxDisplayCount)
      console.log('updateDisplay displayItems', this.displayItems)
    }
  }
}
</script>

<style lang="less" scoped>
.field-select-input {
  width: 100%;
  min-height: 32px;
  border: 1px solid #dcdfe6;
  padding: 6px;
  background: #fff;
  cursor: pointer;
  position: relative;

  .input-display {
    .selected-text {
      color: #333;
      font-size: 14px;
      font-weight: 500;
    }

    .selected-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      align-items: center;

      .fx-tag {
        margin-right: 4px;     
      }

      .more-count {
        cursor: pointer;
        color: #909399;
        font-size: 12px;
        line-height: 20px;
        padding: 0 4px;
      }

      .add-btn {
        height: 24px;
        padding: 0 8px;
        margin-left: 4px;
        border: 1px dashed var(--color-primary02, #ffdda3);
        background-color: transparent;        
        font-size: 12px;

        &:hover {
          border-color: var(--color-primary05, #ff9b29);
          color: var(--color-primary05, #ff9b29);
        }

        .fx-icon-add-2 {
          font-size: 12px;
          margin-right: 2px;
        }
      }
    }
  }

  .arrow-icon {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: #c0c4cc;
    font-size: 14px;
    transition: transform 0.3s;
  }

  &:hover .arrow-icon {
    color: #909399;
  }
}
</style>