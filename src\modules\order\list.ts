import debug from 'debug';
import { LifeHooks } from '../../base/hooks';
import { OrderHooks } from './common/orderhooks';
import { add } from './action';

const log = debug('@nsail:debugger');

export const ext = _.extend(LifeHooks(), OrderHooks(), {
  options: {
    cellEdit: false,
    tableOptions: {
      showMoreBtn: !$dht.config.sail.isHidePrice, // 价格隐藏时，屏蔽掉列设置，因为这里可以放出价格相关字段
    },
  },
  /**
   * 新建操作的调用方法
   */
  addHandle(data: any) {
    _.extend(data, {
      showDetail: true,
      show_type: 'full',
      fromAdd: true,
      record_type: this.get('thirdapp_record_type') || '',
    });
    log('click add button: %o', data);
    add(data);
  },
  /**
   * 解析列表数据
   * @param obj 后台返回的列表数据结构
   */
  parseData(res: any) {
    const obj: {
      totalCount: number;
      data: ObjectDataMap[];
    } = this.$$super.parseData.call(this, res);

    _.each(obj.data || [], (item: any) => {
      item.operate = this.formatDhtButtons(item.operate, item);
    });

    return obj;
  },
  /**
   * 自定义按钮点击处理，
   * @param opts 后台返回的button配置信息
   * @param data 当前行的数据
   * @override components/objecttable/objecttable.js
   */
  _operateHandle(opts: CustomButton, data: ObjectDataMap) {
    const me = this;
    const handle = `handle${opts.action}`;
    if (this[handle]) {
      this[handle]({
        btnOpts: opts,
        data,
        success() {
          me.refresh();
          me.refreshCurView();
        },
      });
      return;
    }
    this.$$super._operateHandle.apply(this, arguments);
  },
});
