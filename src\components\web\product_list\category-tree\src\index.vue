<template>
  <div class="dhtbiz-category-tree">
    <categoryTree @select="handleSelect"></categoryTree>
  </div>
</template>

<script>
export default {
  name: 'dht_web_product_list_category_tree',
  components: {
    categoryTree: () => Fx.getBizComponent('dhtbiz', 'mallCategoryTree').then(res => res())    
  },
  props: {
    dhtPageData: {
      type: Object,
      default: () => ({})
    },
    dhtContainerApi: {
      type: Object,
      default: () => ({})
    },
  },
  methods: {
    handleSelect(category) {
      this.$emit('select', category);
    }
  }
};
</script>

<style lang="less" scoped>
.dhtbiz-category-tree {
  width: 100%;
  .dht-category-tree {
    margin: 0;
  }
}
</style> 