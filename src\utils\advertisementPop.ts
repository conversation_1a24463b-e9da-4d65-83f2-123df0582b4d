// eslint-disable-next-line max-classes-per-file
import modal from '../components/advertisement/advertisement.vue';

const Modal = Vue.extend(modal);

class AdPopRef {
  modalInstance: any;

  constructor() {
    this.modalInstance = new Modal({
      el: document.createElement('div'),
    });
    const modalContainer = this.getModalContainer();
    modalContainer.appendChild(this.modalInstance!.$el as HTMLElement);
    this.modalInstance.$on('closePopup', () => {
      this.modalInstance.show(false);
    });
  }

  getModalContainer() {
    const container = document.createElement('div');
    container.id = 'AdPopContainer';
    document.body.appendChild(container);
    return container;
  }

  close() {
    this.modalInstance.closeModal();
  }
}

export class AdService {
  create() {
    const adPopRef = new AdPopRef();
    return adPopRef;
  }
}

export const adService = new AdService();
