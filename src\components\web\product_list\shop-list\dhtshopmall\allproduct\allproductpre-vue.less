.dht-shopmall-allproduct-preview {    .content-list {        border: 1px solid var(--color-neutrals04);        .list-item {            display: flex;            padding: 15px;            border-bottom: 1px solid var(--color-neutrals04);            box-sizing: border-box;        }        .active {            background-color: var(--color-neutrals03);        }        .list-item-td {            width: 12%;            height: 10px;            background-color: var(--color-neutrals05);            border-radius: 3px;            margin-right: 50px;        }    }    .content-card {        display: flex;        overflow: hidden;        .card-item {            position: relative;            min-width: 236px;            padding: 10px;            margin-right: 10px;            border: 1px solid var(--color-neutrals05);            border-radius: 4px;            color: var(--color-neutrals19);        }        .card-item-img {            width: 100%;            height: 211px;            background: url('../../assets/images/common/defaultImg.png') no-repeat;            background-size: cover;        }        .card-item-title {            font-size: 14px;            overflow: hidden;            text-overflow: ellipsis;            display: -webkit-box;            -webkit-line-clamp: 2;            -webkit-box-orient: vertical;            margin-top: 6px;        }        .card-item-price {            font-size: 20px;            font-weight: 500;        }        .card-item-field {            font-size: 12px;            color: var(--color-neutrals15);        }         .card-item-tags {            margin-top: 8px;         }        .card-item-tag {            color: var(--color-primary06);            border: 1px solid var(--color-primary06);            padding: 2px;            border-radius: 3px;            margin-right: 5px;        }        .card-item-tag_top {            position: absolute;            top: 10px;            left: 10px;        }    }}