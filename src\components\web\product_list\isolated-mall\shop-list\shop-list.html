<div class="dht-isolated-mall-shop-list">
  <product-list
    v-show="firstDataLoad"
    :list="listData"
    :options="listOptions"
    @collectChange="collectChange">
  </product-list>
  <div class="dht-shop-pagination">
    <fx-pagination
      v-show="total > 0"
      background
      layout="prev, pager, next"
      :total="total"
      :current-page="currentPage"
      :page-size="pageSize"
      @current-change="paginationChange">
    </fx-pagination>
  </div>
  <loading class="dht-absolute-center" v-show="isLoading"></loading>
</div>


