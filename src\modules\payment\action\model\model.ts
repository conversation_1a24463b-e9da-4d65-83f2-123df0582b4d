import { LifeHooks } from '../../../../base/hooks';
import { getComponent } from '../../../../components';

export function modelExtend(Model: BackboneComponent): BackboneComponent {
  const proto = Model.prototype;

  /* eslint no-param-reassign: 0 */
  Model = Model.extend({
    submit(data: any, btn: any) {
      if (data.pay_type === '2') {
        this._submitOnline(data, btn);
      } else {
        proto.submit.apply(this, arguments);
      }
    },

    /* ***************************************私有方法*********************************************** */
    // 线上支付
    _submitOnline(data: any, btn: any) {
      const param = this.formatParam(data);
      this._getPaymentUrl(param, btn, (resUrl: any) => {
        this._showPayDialog(resUrl, () => {
          this.trigger('success', _.extend({}, this.get('data')));
        });
      });
    },
    _getPaymentUrl(param: any, btn: any, callback: any) {
      if (this.isLoading) return;

      this._setLoading(true, btn);
      $dht.services.order.payOnline(param).then((urlParams: any) => {
        callback && callback(urlParams);
        this._setLoading(false, btn);
      }, (error: any) => {
        this._setLoading(false, btn);
        CRM.util.alert(error.Result.FailureCode);
      });
    },

    _showPayDialog(url: any, cb: any) {
      // const me = this;
      const $vue = this.$$vue || {};
      getComponent('PayDialog').then((Ctor: any) => {
        if (Ctor) {
          const wrapper = document.createElement('div');
          document.body.appendChild(wrapper);
          $vue.pay = new Ctor();
          $vue.pay.urlParams = url;
          $vue.pay.$mount(wrapper);
          $vue.pay.$on('close', (e: any) => {
            document.body.removeChild($vue.pay.$el);
            $vue.pay.$destroy();
            $vue.pay = null;
          });
          $vue.pay.$on('paysuccess', (e: any) => {
            cb && cb();
          });
        }
      });
    },

    _setLoading(status: Boolean, btn: any) {
      this.isLoading = status;
      this.isLoading ? $dht.showLoaingByBtn(btn) : $dht.hideLoaingByBtn(btn);
    },
  });

  Model = Model.extend(LifeHooks());

  return Model;
}
