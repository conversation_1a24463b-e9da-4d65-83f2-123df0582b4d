<template>
  <component v-if="showComponent" :is="currentComponent" v-bind="$data"></component>
</template>

<script lang="ts">
const Component: VueComponent = {
  props: ['appId'],

  data() {
    return {
      showComponent: true,
      apiName: this.$query('apiName'),
      component: this.$query('component'),
      subComponent: this.$query('subComponent'),
    };
  },

  computed: {
    currentComponent() {
      let component = this.component;
      if (!component && this.$route.path === '/') {
        // 首页比较特殊一点，没有任何组件相关名称信息
        component = 'index';
      }
      return $dht.getModule({
        component,
        subComponent: this.subComponent,
        objectApiName: this.apiName,
      });
    },
  },

  watch: {
    /**
     * 路由守卫感觉有bug，这里暂时先用$route.params监听路由变化
     */
    '$route.params': function w(newValue: VueRoute) {
      const apiName = this.$query('apiName');
      (this as any).refresh({ apiName, ...newValue });
    },
    '$route.path': function w(newValue: string, oldValue: string) {
      console.log(newValue, oldValue);
    },
  },

  created() {
    $dht.log('FrameContentComponent created');
    this.broadcast(this.$router.currentRoute);
    this.updateGlobalEnv(this.$router.currentRoute);
  },

  destroyed() {
    $dht.log('FrameContentComponent destroyed');
  },

  beforeRouteUpdate(to: VueRoute, from: VueRoute, next: () => void) {
    $dht.log('FrameContent beforeRouteUpdate: %s, %s', to, from);
    this.broadcast(to);
    this.updateGlobalEnv(to);
    // this.updateComponent(to);
    next();
  },

  beforeRouteLeave(to: VueRoute, from: VueRoute, next: () => void) {
    $dht.log('FrameContent beforeRouteLeave: %s, %s', to, from);
    this.broadcast(to);
    this.updateGlobalEnv(to);
    // this.updateComponent(to);
    next();
  },

  methods: {
    refresh(state: any) {
      this.showComponent = false;
      Object.assign(this.$data, state);
      this.$nextTick(() => {
        this.showComponent = true;
      });
    },

    broadcast(route: VueRoute) {
      const component = this.$query!('component', route);
      const moduler = this.$query!('module', route);
      const appId = this.$query!('appId', route) || this.appId;

      const event = new CustomEvent('router.change', {
        detail: {
          component,
          moduler,
          appId,
        },
      });

      window.dispatchEvent(event);
    },

    isCoverHeaders(comp: string) {
      return !['index'].includes(comp);
    },

    updateGlobalEnv(route: VueRoute) {
      const comp = this.$query!('module', route);
      const appId = this.$query!('appId', route) || this.appId;
      const coverHeaders = this.isCoverHeaders(comp);

      $dht.updateApiContext({
        appId,
        coverHeaders,
      });
    },

    updateComponent(route: VueRoute) {
      const newState = ['component', 'subComponent', 'apiName'].reduce(
        (state: Record<string, string>, name: string) => {
          state[name] = this.$query!(name, route);
          return state;
        },
        {} as Record<string, string>,
      );
      this.refresh(newState);
    },
  },

};

export default Component;
</script>
