import runningHooks from './running-hooks';
export default function () {
    return {
        name: 'dht_web_product_list_search_box',
        displayName: '商品列表搜索',
        data: {
        },
        $$data: {
        },
        related: {
            /* attributeSettings: [
                {
                  name: 'SetterField',
                  data: {
                    setter: {
                      component: () => import('./setting.vue')
                    }
                  }
            }],
            previewDisplay: () => import(
                './display.vue'
            ) */
        },
        rules: {
        },
        hooks: runningHooks.hooks
    };
} 