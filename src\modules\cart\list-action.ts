import { getComponent } from '../../components';

/**
 * 删除购物车产品
 *
 * @param data 目标购物车产品数据
 */
export function remove(this: any, data: any): void {
  this.getCartService().batchRemove([data._id]).then(() => {
    Vue.prototype.$message({ message: $t('删除成功'), type: 'success' });
    this.refreshTable();
  });
}

/**
 * 切换赠品
 *
 * @param data 目标购物车产品数据
 */
export function changegift(this: any, data: any): void {
  getComponent('PromotionGiftSelector').then((Ctor) => {
    $dht.modal.create({
      title: $t('选择赠品'),
      width: '740px',
      component: Ctor,
      componentParams: {
        readonly: false,
        gifts: data.promotion_id__p.all_gifts,
        selected: data.promotion_id__p.new_gifts,
        optionalCount: data.promotion_id__p.gift_optional_count,
      },
      okText: $t('加入购物车'),
      onOk: ((componentRef: any) => {
        const selected = componentRef.handleSubmit();
        if (selected === null) return false;

        return this.getCartService()
          .updatePromotionGift(data._id, selected)
          .then((res: any) => {
            console.log(res);
            this.refreshTable();
          });
      }),
    });
  });
}

export const CartActionMap: Record<string, (data: any) => void> = {
  button_remove__c: remove,
  button_changegift__c: changegift,
};
