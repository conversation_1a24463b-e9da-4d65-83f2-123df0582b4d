<template>
  <div class="paas-cart-container">
    <fx-button
      v-if="isShowRecordTypeBtn"
      class="cart-recordtype-btn"
      type="primary"
      @click="selectRecordType">
      {{ $t('选择业务类型') }}
    </fx-button>
  </div>
</template>

<style lang="less">
  .paas-cart-container {
    height: 100%;
    background-color: white;
    .dht-cart-footer {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
    }
    .cart-recordtype-btn {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .dht-cart-loading.el-loading-mask.is-fullscreen .el-loading-spinner .circular {
    height: 40px;
    width: 40px;
  }
</style>

<script lang="ts">
import BaseMixin from '../mixins/base';
import { viewExtend } from './view/cart-view';
import { getCrmActionComponent } from '../../base/bridge';
import { cartModelExtend } from './extend/model-extend';

const Component: VueComponent = {
  name: 'DhtModulePaasCart',

  mixins: [BaseMixin],

  data() {
    return {
      objectApiName: 'ShoppingCartObj',
      isShowRecordTypeBtn: false,
      field: null,
      view: null,
      model: null,
      cartStartTime: new Date().getTime(),
    };
  },

  methods: {
    renderComponent(): Promise<void> {
      this.showLoading();
      if (!$dht.config.sail.isShopMallMode) {
        this._destroyLoading();
        CRM.util.alert($t('dht.cart.forbid.tip')); // 供货商已屏蔽商城模式，请直接新建销售订单！
        return new Promise(() => {});
      }
      // 获取orderform.js文件
      return getCrmActionComponent('orderform').then(() => {
        Fx.async([
          'crm-modules/action/orderform/view/view',
          'crm-modules/action/orderform/model/model',
          'crm-modules/action/field/field',
        ], (View: BackboneComponent, Model: BackboneComponent, Field: BackboneComponent) => {
          this._saveBackbone(View, cartModelExtend(Model), Field);
          this.selectRecordType();
        });
      });
    },

    /**
     * 选择业务类型才能进入购物车
     */
    selectRecordType() {
      // 如果有设置过业务类型，则默认用该业务类型
      const defaultRecordType = $dht.getMainRecordType();

      if (defaultRecordType) {
        // 进入应用需要选择业务类型的企业
        this.isShowRecordTypeBtn = false;
        this.createView(viewExtend(this.view), this.model, defaultRecordType);
        this._saveBackbone(null, null, null);
      } else {
        // 进入购物车需要选择业务类型的企业
        this._destroyLoading();

        this.field.selectType('SalesOrderObj', {}, (type: string) => {
          this.isShowRecordTypeBtn = false;
          this.createView(viewExtend(this.view), this.model, type);
          this._saveBackbone(null, null, null);
        }, () => {
          this.isShowRecordTypeBtn = true;
        });
      }
    },

    /**
     * 保存请求的backbone组件
     */
    _saveBackbone(view: any, model: any, field: any) {
      this.view = view;
      this.model = model;
      this.field = field;
    },

    /**
     * 创建销售
     */
    async createView(View: BackboneComponent, Model: BackboneComponent, recordType: string) {
      let mdData = await this.getCartData();
      const dhtDescribe = await this.getDescribeFromDht(recordType);
      if ($dht.config.bom.isEnable || $dht.config.simpleCpq.isEnable) {
        const [bomService] = await this.getVcrmComponents();
        mdData = bomService.formatBomMdData(mdData);
        await this.calculateSubLinesQuoteAndCalFields(mdData, recordType);
        if ($dht.config.simpleCpq.isEnable && $dht.config.multiUnit.isEnable) {
          // 对于固定搭配和多单位子件，需要计算子件多单位信息
          await this.supplementBomSubLineUnitData(mdData);
        }
      }

      mdData.forEach((item: any) => {
        // 如果产品没有定价周期，则补充默认值1
        if (!item.pricing_period) {
          item.pricing_period = 1;
        }
      });

      this.$$c.cartView = new View({
        el: this.$el,
        Model,
        apiname: 'SalesOrderObj',
        record_type: recordType,
        mdData: { SalesOrderProductObj: mdData },
        noPreCalculate: true,
        workFlowType: 2,
        loadTemplateLength: 1,
        notSetShipDefaultValue: true,
        dhtDescribe: JSON.stringify(dhtDescribe), // 订货通缓存的订单描述，避免再次请求
        constraintPriceBookPriority: true, // 强制走价目表优先级
        plugins: {
          domains: [
            {
              pluginApiName: 'dht_shoppingcart',
              objectApiName: 'SalesOrderObj',
              fieldApiName: '',
              resource: 'vcrm/plugin/dht_shoppingcart',
              params: {
                cartStartTime: this.cartStartTime,
                fieldMapping: {},
                details: [
                  {
                    objectApiName: 'SalesOrderProductObj',
                    detailKey: 'dht_shoppingcart_detail',
                    fieldMapping: {},
                  },
                ],
              },
            },
          ],
          fields: [],
          hooks: [],
        },
      });
      this.$$c.cartView.render();
    },

    /**
     * 获取购物车数据
     */
    async getCartData() {
      // 有可能为null
      const defaultRecordType = $dht.getMainRecordType();
      const dhtDescribe = await this.getDescribeFromDht(defaultRecordType);
      if(!dhtDescribe.detailObjectList || !dhtDescribe.detailObjectList.length) {
        CRM.util.alert($t('dht_please_contact_the_upstream_management'));
        return;
      }
      const detailOrderProductDescribe = dhtDescribe.detailObjectList && dhtDescribe.detailObjectList.length > 0 && dhtDescribe.detailObjectList.find((item: any) => item.objectApiName === 'SalesOrderProductObj');
      if(!detailOrderProductDescribe || !detailOrderProductDescribe.layoutList.length){
        CRM.util.alert($t('dht_please_contact_the_upstream_management'));
        return;
      }
      // 获取购物车数据
      return $dht.services.cart.getCartList({}).then(async () => {
        const list = await $dht.services.cart.getRenderableCartList();
        return list;
      });
    },

    getDescribeFromDht(masterRecordType: string) {
      const params = {
        object_api_name: 'SalesOrderObj',
        layout_type: 'add',
        record_type: masterRecordType,
        include_layout: true,
        include_detail_describe: true,
      };
      return window.$dht.getService('meta').getDescribeAndLayout(params);
    },

    getVcrmComponents(): Promise<any> {
      return new Promise((resolve) => {
        Fx.async(['vcrm/sdk'], (vcrmSdk: any) => {
          const p1 = vcrmSdk.widgetService.getService('bomService');
          Promise.all([p1]).then((result: any[]) => {
            resolve(result);
          });
        });
      });
    },
    /**
     * 对于固定搭配和多单位子件，需要计算子件多单位信息，如统计单位等
     * @param mdData
     */
    async supplementBomSubLineUnitData(mdData: any[]) {
      // 找出多单位子件
      const subLines = mdData.filter(
        (item) => item.parent_prod_pkg_key && item.is_multiple_unit__v,
      );
      if (subLines.length) {
        // eslint-disable-next-line max-len
        await window.$dht.services.cart.orderProductService._supplementUnitData(subLines, { modifiedPrice: false });
      }
    },
    /**
     * 计算子件的自定义计算字段和默认值
     * @param mdData
     * @param recordType
     */
    calculateSubLinesQuoteAndCalFields(mdData: any[], recordType: string) {
      return window.$dht.services.meta.calculateSubLinesQuoteAndCalFields(mdData, recordType);
    },

    showLoading() {
      window.$dht.cartLoading = FxUI.Loading.service({
        body: true,
        background: 'transparent',
        text: `${$t('加载中')}...`,
        customClass: 'dht-cart-loading',
      });
    },

    _destroyLoading() {
      if (window.$dht.cartLoading) {
        window.$dht.cartLoading.close();
        window.$dht.cartLoading = null;
      }
    },
  },
};

export default Component;
</script>
