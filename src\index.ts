import './dev';
import './styles/index.less';

import * as Global from './context/global';
import * as LifeHook from './context/lifehook';
import * as Patch from './context/patch';
import * as VueExtend from './context/vue-extend';

import { createApp, createHeader, createSidebar } from './app';

const startApp = () => {
  const options = {
    props: {
      appId: $dht.appId,
    },
  };
  createApp(options);
};

// 装载全局事件，包括审批流的一些内容
Fx.installGlobalEvents();
/**
 * 订货通独立频道初始化过程：
 *
 * + 加载国际化多语言配置；
 * + 加载相关公共依赖库；
 * + 初始化变量`$dht`，并挂载`sail-core`，和一些全局方法如创建订单；
 * + 初始化补丁，主要是：统一HACK底层相关`FHHApi`；注册全局`api hooks`；挂载特殊节点；
 * + 尝试互联身份换取；
 * + 获取全局配置：订货应用门户配置；CRM插件配置信息；订货通初始化需要数据；
 * + 初始化顶部导航栏和左侧全局导航栏；
 * + 启动应用；
 */
Fx.i18n.downLoad().then(() => {
  Fx.async([
    'module-qx',
    'module-fs',
    'module-crm',
    'module-bi',
  ], () => {
    // 以下两个必须
    Global.init();
    LifeHook.enter();

    // 以下的只适用于订货通独立频道
    Patch.init();
    VueExtend.init();

    const $alert = Vue.prototype.$alert;
    // 是否是厂商门户
    $dht.isPortal = false;

    $dht.on('err:nocart', (err: any = {}) => {
      if (err.Code === '320001400') {
        $alert($t('没有购物车列表查看权限，请联系上游厂商开通'));
      } else if (err.Code === '320002404') {
        $alert($t('还未开启购物车插件，请联系上游厂商开通'));
      } else {
        $alert($t(`购物车插件配置错误，Code = ${err.Code}`));
      }
    });

    $dht.on('err:business', (err: any = {}, config: any = {}) => {
      config.showError = config.showError === undefined ? true : config.showError;
      if (config.showError) {
        const errorMsg = err.Message || `业务请求错误，Code = ${err.Code}`;
        $alert($t(errorMsg));
        return;
      }
      console.error(err);
    });

    Patch.erlogin({ appId: $dht.appId, upstreamEa: $dht.upstreamEa })
      .then((res) => {
        return Promise.all([
          Global.getCrmAllConfig(),
          Patch.getPortalTpls({ appId: $dht.appId }),
          Patch.getDownstreamInfo(),
          $dht.init(),
        ]);
      })
      .then(() => {
        createHeader();
        createSidebar();

        startApp();
      })
      .catch((err) => {
        console.error(err);
        const $alert = Vue.prototype.$alert;
        if (FxUI.Utils.isString(err)) {
          $alert(err);
        } else if (err.Code) {
          $alert(`应用初始化失败，错误码：${err.Code}`);
        } else {
          $alert(`应用初始化失败，错误信息：${JSON.stringify(err)}`);
        }
      });
  });
});
