/**
 * 基于crm菜单
 * Created by Ys on 20201112
 */
import { getHashSchema } from '../../base/utils';
import routeData from './route';

interface Route {
  url: string;
  hash: string;
  icon: string;
  text: string;
  appId?: string;
  iconIndex: number;
  displayName: string;
  referenceApiname: string;
}

const useComponents = {
  matchs: [{
    content: /^crm\/custompage\/=\//,
    route: 'custompage',
  }],
  use(path: string) {
    return this.matchs.reduce((res, { content, route }) => {
      if (!res && content.test(path)) {
        return route;
      }
      return res;
    }, '');
  },
};

const cacheCompatibleMenu = (menus: any) => {
  window.CRM_All_MENU = Fx.util.deepClone(menus);
};

/**
 * 渲染提醒代办的路由
 */
const RouteRender = {
  remind: (appId: string, item: any, isOld: boolean) => {
    if (isOld) {
      return `#${getHashSchema(appId, `remind/${item.key.toLowerCase()}`)}`;
    }

    const parameters = { key: item.key };
    return `#${getHashSchema(appId, 'remind/list', parameters)}`;
  },
};

const CtorOptions: any = {
  routeData,

  /**
   * 获取数据
   * @param callback
   */
  _getData(callback: (params: any) => void) {
    // const params = {
    //   id: '71568_dbe60ce3fb8647efbe09941cc93b6d36',
    //   templeId: '71568_f9ca24525510462d92321feb35214515',
    // };
    const params = {
      id: window.$dht.env.portalTpls[0].webMenuId,
    };
    const headers = {
      'x-out-link-type': 1,
      'fs-out-appid': this.options.appId,
    };
    this.options
      .apiMenus(params, headers)
      .then((value: any) => {
        if (value && value.menus) {
          value.menus.forEach((menu: any) => {
            if (!menu || !menu.items) return;
            menu.items.forEach((item: any) => {
              // 订货通预设对象，将会被特殊处理
              // const presetObjects: Record<string, any> = {
              //   SalesOrderObj: { name: '订单' },
              //   SPUObj: { name: '商品' },
              //   ProductObj: { name: '商品' },
              //   PromotionObj: { name: '促销' },
              //   DeliveryNoteObj: { name: '发货单' },
              //   PaymentObj: { name: '回款' },
              // };
              // if (item.appId === 'FSAID_11490c84' && presetObjects[item.referenceApiname]) {
              //   item.hash = `/sail/${item.referenceApiname}`;
              //   // item.displayName = map[item.referenceApiname].name;
              // }
            });
            // menu.items.push({
            //   appId: 'FSAID_11490c84',
            //   displayName: 'Pass购物车',
            //   iconIndex: 0,
            //   iconPathHome: 'A_201907_02_785afaa9095f46489016dffe68eb8d5d.svg',
            //   iconPathMenu: 'A_201907_02_785afaa9095f46489016dffe68eb8d5d.svg',
            //   id: 'PaasShoppingCartObj',
            //   isHidden: false,
            //   mobileConfig: {},
            //   number: -1,
            //   privilegeAction: ['Add', 'List'],
            //   referenceApiname: 'PaasShoppingCartObj',
            //   searchWords: ['gouwuche', '购物车', 'gwc'],
            //   type: 'menu',
            // });
          });
        }
        const menus = Fx.util.deepClone({
          menus: value.menus,
        });
        this.data = this._formatData(menus);
        cacheCompatibleMenu(menus);
        callback(this.data);
      });
  },

  _extendObjProperty(route: any) {
    const apiName = route.referenceApiname;
    this.parseText(route);
    this.parseIcon(apiName, route);
    if (route.useDefaultUrl && route.url) {
      this.parseThirdUrl(apiName, route);
    } else {
      this.parseUrl(apiName, route);
    }

    return route;
  },

  getHash(hash: string) {
    try {
      const h = hash.split('/=/')[0];
      const p = h.split('/').slice(1);
      const appId = this.options.appId;
      // bi的需要单独处理
      if (p[0] === 'bi') {
        return `#${getHashSchema(appId, p.join('/'))}`;
      }
    } catch (error) {
      console.error(error);
    }

    const arr = hash.split('?');
    return arr[0];
  },

  getConfig() {
    const appId = this.options.appId;
    const indexHash = getHashSchema(appId, 'index');
    return {
      homeRoute: `#${indexHash}`,
      hideMenuSetting: true,
      renderTodo: (item: any, isOld: boolean) => RouteRender.remind(appId, item, isOld),
      renderRemind: (item: any, isOld: boolean) => RouteRender.remind(appId, item, isOld),
    };
  },

  parseRoute(route: Route) {
    route.hash = getHashSchema(route.appId || this.options.appId, route.hash);
    return route;
  },

  parseIcon(apiName: string, route: Route) {
    if (!apiName || route.icon) return;
    const icon = `icon-myobj-${route.iconIndex ?? 'ico-define'}`;
    route.icon = !this.isPaasObj(apiName)
      ? `${icon} ico-${apiName.toLowerCase()}`
      : icon;
  },

  parseText(route: Route) {
    route.text = route.displayName;
  },

  parseThirdUrl(apiName: string, route: Route) {
    const appId = route.appId || this.options.appId;
    const path = useComponents.use(route.url) || 'thirdapp';

    route.hash = getHashSchema(appId, path, { apiName });
  },

  parseUrl(apiName: string, route: Route) {
    const appId = route.appId || this.options.appId;
    let recordType = this.parseRecordType(route.url);
    if (recordType) {
      const params = route.url.match(/apiname=(\S+)&/);
      /* eslint-disable-next-line */
      params ? (apiName = params[1]) : (recordType = null);
    }

    recordType = recordType ? recordType[1] : undefined;
    if (!/^\/sail/.test(route.hash)) {
      route.hash = getHashSchema(
        appId,
        route.hash || 'list',
        route.hash ? undefined : { apiName, recordType },
      );
    }
  },

  parseRecordType(url: string) {
    return url?.match(/thirdapprecordtype=(\S+__c)/);
  },
};

export default function asyncLoadAside(): Promise<BackboneComponent> {
  return new Promise((resolve) => {
    const deps = [
      'crm-assets/js/layout/aside/aside',
      'crm-assets/style/all.css',
    ];
    const factory = (Aside: BackboneComponent) => {
      const options = Fx.util.deepClone(CtorOptions);
      resolve(Aside.extend(options));
    };
    Fx.async(deps, factory);
  });
}
