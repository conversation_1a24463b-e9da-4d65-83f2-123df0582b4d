export default {
  hooks: {
    created(node) {
        console.log('running hooks created', node);
        
        // if (node.data.basicInfo.state === 2) {
        //     setLayerSizeByNode(node);
        //     setLayerEditNameIconByNode(node, name); //编辑名称
        //     setLayerEditIconByNode(node, name); //编辑 保存
        //     setLayerInteractionIconByNode(node); //联动
        // }
        // 设置是否可以下钻
        // setCanDrill(node);
    },
    beforeRender(node, { query, actions }) {
        console.log('running hooks beforeRender', node, query, actions);
        // setFilterDataConfig(...arguments);
    },
    rendered(node, { query, actions }) {
        console.log('running hooks rendered', node, query, actions);
        const nodeOptions = query.getOptions();
        const id = node.id;
        const vm = query.instance(id);
        const nodeName = node.name;
        const nodeData = node.data;
        // debugger;
        console.log('running hooks rendered nodeOptions:', nodeOptions);
        // bindLinkageInitiator(...arguments);
        // bindLinkageRecevier(...arguments);
    },
  }    
}