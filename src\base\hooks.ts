import debug from 'debug';

const log = debug('@nsail:debugger');

/**
 * 这里需要返回一个对象，每一次都要生成一个新的hooks，
 * 因为每一个BackboneComponent类都需要有自己独立的hooks
 */
export function LifeHooks(): any {
  return {
    /**
     * 返回超类的原型对象，实现父类的接口调用，如：
     * ```
     * this.$super().initialize.call(this, options)
     * ```
     */
    $super() {
      return this.constructor.__super__;
    },
    /**
     * 重载构造函数，主要是创建统一的vue组件容器
     * @param options 列表配置参数
     */
    initialize(options: any) {
      log('initialize: %o', options);
      // 所有的内部vue组件对象全部缓存在这里统一回收；
      // 虽然父类提供了widget缓存，但是还是自己声明一个区分开；
      this.$$vue = {};
      // `backbone.js: __super__ = parent.prototype`
      this.$$super = this.constructor.__super__;
      this.$$super.initialize.apply(this, [options]);
    },
    /**
     * 重载销毁函数，主要是回收vue组件
     */
    destroy() {
      _.each(this.$$vue, ($vue: any) => {
        if ($vue && _.isFunction($vue.$destroy)) {
          $vue.$destroy();
        }
      });
      this.$$vue = null;
      this.$$super.destroy.apply(this);
      log('destroyed instance');
    },
  };
}
