import { LifeHooks } from '../../../../base/hooks';

export function modelExtend(Model: BackboneComponent): BackboneComponent {
  const proto = Model.prototype;
  const moment = CRM.util.moment;

  /* eslint no-param-reassign: 0 */
  Model = Model.extend({
    // 这个方法在crm中是写在orderform中的，这里把它挪进来
    parse(res: any) {
      // 将订单产品数据 加上 订单产品的业务类型字段
      const detailObject = res.detailObjectList && res.detailObjectList[0];
      const detailLayout = detailObject && detailObject.layoutList && detailObject.layoutList[0];
      const detailRecordType = detailLayout && detailLayout.record_type;
      const mdData = this.get('mdData');
      const list = mdData && mdData.SalesOrderProductObj;
      if (list && list.length) {
        _.each(mdData.SalesOrderProductObj, (item: any) => {
          !item.record_type && (item.record_type = detailRecordType);
        });
        this.set('mdData', mdData);
      }

      const obj = proto.parse.call(this, res);

      obj.data.discount = obj.data.discount || 100;
      obj.data.order_time = obj.data.order_time || moment(moment().format('YYYY-MM-DD')).unix() * 1000;
      obj.data.mc_currency = $dht.config.currency.currencyCode;

      return obj;
    },

    beforeSetData(data: any) {
      proto.beforeSetData.call(this, data);
      const fields = data.fields;
      const keys = Object.keys(fields);
      keys.forEach((key: string) => {
        switch (key) {
          case 'dynamic_amount':
          case 'price_book_id':
            if (this.isCart()) {
              fields[key].is_readonly = true;
            }
            break;
          // case 'mc_currency':
          //   fields[key].is_readonly = true;
          //   break;
          default:
            break;
        }
      });
    },

    isCart() { // 是否是购物车模式
      return this.get('source') === 'cart';
    },
  });

  Model = Model.extend(LifeHooks());

  return Model;
}
