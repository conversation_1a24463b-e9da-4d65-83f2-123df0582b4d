<template>
  <div ref="fkScroll"
       class="fk-scroll"
       :class="{
         'pull-down': (state === 0),
         'pull-up': (state === 1),
         refreshing: (state === 2),
         touching: touching,
         'no-refresh': !onRefresh
       }"
       @touchstart="onRefresh ? touchStart($event) : undefined"
       @touchmove="onRefresh ? touchMove($event) : undefined"
       @touchend="onRefresh ? touchEnd($event) : undefined"
       @mousedown="onRefresh ? mouseDown($event) : undefined"
       @mousemove="onRefresh ? mouseMove($event) : undefined"
       @mouseup="onRefresh ? mouseUp($event) : undefined"
       @scroll="(onInfinite || infiniteLoading) ? onScroll($event) : undefined"
  >
    <div class="scroll-inner"
         :style="{
        transform: 'translate3d(0, ' + top + 'px, 0)',
        webkitTransform: 'translate3d(0, ' + top + 'px, 0)'
      }"
    >
      <div class="pull-to-refresh-layer" v-if="!!onRefresh">
        <slot name="refresh">
          <div class="preloader"></div>
          <div class="pull-to-refresh-arrow"></div>
          <span class="label-down">{{ $t('i18n.common.scroll.pull') }}</span>
          <span class="label-up">{{ $t('i18n.common.scroll.release') }}</span>
          <span class="label-refresh">{{ $t('i18n.common.scroll.refreshing') }}</span>
        </slot>
      </div>
      <slot></slot>
      <div class="infinite-layer" v-if="onInfinite">
        <slot name="infinite">
          <div class="infinite-preloader"></div>
          <span class="label-loading">{{ $t('i18n.common.scroll.loading') }}</span>
        </slot>
      </div>
    </div>
  </div>
</template>

<script src="./_fk-scroll.ts" lang="ts"></script>
<style src="./_fk-scroll.less" lang="less" scoped></style>
