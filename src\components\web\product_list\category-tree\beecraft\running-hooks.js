import nodeHelper from '../../../utils/nodeHelper.js';

export default {
  hooks: {
    rendered(node, { query, actions }) {
      console.log('category-tree running hooks rendered', node, query, actions);
      
      // 获取容器组件实例
      const containerInstance = nodeHelper.getContainerInstanceByName(query, 'dht_web_container_product_list');
      // 获取当前组件实例
      const vm = query.instance(node.id);
      
      if (containerInstance && vm) {
        // 设置数据 - 只传递分类数据
        // vm.category = containerInstance.dhtPageData.category;
        // vm.dhtContainerApi = containerInstance.dhtContainerApi;
        // vm.dhtPageEventTypes = containerInstance.dhtContainerApi.getEventTypes();
        const eventTypes = containerInstance.dhtContainerApi.getEventTypes();
        
        // 建立事件监听
        vm.$on('select', (data) => {
          // 使用容器组件的事件总线触发事件
          if (containerInstance.eventBus) {            
            containerInstance.eventBus.$emit(eventTypes.CATEGORY_CHANGE, data);
          }
        });
      }
    }
  }    
}
