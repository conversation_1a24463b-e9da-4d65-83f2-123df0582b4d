<template>
  <div class="dht_web_shop_list-setting" v-if="!isLoading">
    <base-setting
      ref="baseSetting"
      :all-fields="allFields"
    />

    <card-setting
      ref="cardSetting"
      :all-fields="allFields"
    />    
  </div>
</template>

<script>
import { dhtBizModel } from '@/components/web/utils/model';
import { SAVE_KEYS } from './const';
import CardSetting from './card-setting.vue';
import BaseSetting from './base-setting.vue';

export default {
  name: 'dht_web_product_list_shop_list_beecraft_setting',
  inject: ['useInternalNode'],

  components: {
    CardSetting,
    BaseSetting
  },

  data() {
    return {
      isLoading: true,
      isCardInit: false,
      allFields: {},
    }
  },

  computed: {
    isSpuMode() {
      return dhtBizModel.isSpuMode();
    }
  },

  methods: {
    init() {
      const {
        is_spu_mode,
        card_main_info
      } = this.useInternalNode(node => {
        return node.data;
      });      

      // 设置当前商品模式
      this.updateProps('is_spu_mode', dhtBizModel.isSpuMode());

      // 已经配置过，且模式改变，发出提示
      if (is_spu_mode !== dhtBizModel.isSpuMode()) {
        CRM.util.alert($t("dht.shopmall_widget.mode_changed_tip"));
        this.resetVal();
        return;
      }      

      this.isLoading = false;
      setTimeout(() => {
        // 初始化卡片设置
        this.$refs.cardSetting && this.$refs.cardSetting.init(card_main_info, this.isCardInit);

        // 初始化基础设置
        this.$refs.baseSetting && this.$refs.baseSetting.init(card_main_info);
      }, 200);
    },

    async fetchAllFields() {
      try {
        this.allFields = await dhtBizModel.fetchObjFields(dhtBizModel.mainObjApiName());
        console.log('fetchAllFields completed, allFields:', this.allFields);
      } catch (error) {
        console.error('获取字段信息失败:', error);
        this.allFields = {};
      }
    },

    resetVal() {
      // 重置卡片设置
      this.$refs.cardSetting && this.$refs.cardSetting.resetVal();
      // 重置基础设置
      this.$refs.baseSetting && this.$refs.baseSetting.resetVal();
    },

    updateProps(key, val) {
      const { actions } = this.useInternalNode();
      actions.setCustom((data) => {
        data[key] = val;
      });
      this.$set(this, key, val);
    },
  },

  async created() {
    await this.fetchAllFields();
  },

  mounted() {
    this.init();
  }
}
</script>

<style lang="less" scoped>
.dht_web_shop_list-setting {
  width: 100%;
}
</style>