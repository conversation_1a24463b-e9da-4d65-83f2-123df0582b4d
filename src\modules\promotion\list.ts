import debug from 'debug';
import { LifeHooks } from '../../base/hooks';

const log = debug('@nsail:debugger');

export const ext = _.extend(LifeHooks(), {
  options: {
    cellEdit: false,
  },
  /**
   * 查看详情
   * @param rowData 当前行数据
   * @param idList 当前表格的ID们
   */
  // showDetail(rowData: any, idList: any[]) {
  //   log('%o, %o', rowData, idList);
  //   const importer = PAAS.get('ObjectDetail');
  //   importer().then((ObjectDetail: VueComponent) => {
  //     const Detail = Vue.extend(ObjectDetail.default);
  //     const oDiv = document.createElement('div');
  //     document.body.appendChild(oDiv);

  //     this.vm = new Detail({
  //       propsData: {
  //         apiId: '6038a2aaa6552c0001affc20',
  //         apiName: 'PromotionObj',
  //         idList: [
  //           '6038a2aaa6552c0001affc20',
  //           '6038a824a6552c0001b062d6',
  //           '6038a3a9a6552c0001b025e3',
  //           '6038a003a6552c0001afea15',
  //           '60389fcba6552c0001afe7c3',
  //           '60389cd9a6552c0001afd84b',
  //         ],
  //         isSlide: true,
  //         isCanEdit: true,
  //         showMask: false,
  //         subtab: '',
  //         top: 56,
  //         zIndex: 500,
  //         hooks: {
  //           beforeFetch: this.beforeFetch,
  //           beforeParse: this.beforeParse,
  //           beforeInitData: this.beforeInitData,
  //         },
  //       },
  //     }).$mount(oDiv);

  //     const options = {

  //     };
  //     this.vm.show('6038a2aaa6552c0001affc20', 'PromotionObj', options);
  //   });
  // },
  beforeFetch(options: Record<string, any>) {
    options.url = '/FHH/EM6HDHT/API/v1/object/promotion/service/get_by_id';
    options.postData.promotion_id = options.postData.objectDataId;
    options.postData.include_describe = true;
    options.postData.include_layout = true;
    return options;
  },
  beforeParse(data: any) {
    log('%o', data);
    return data;
  },
  beforeInitData(data: any) {
    // // 给促销规则字段添加属性
    // let fields = data.describe.fields || {}
    // if(fields.type) {
    //     fields.type.isWholeLine = true
    // }
    return data;
  },
});
