const path = require('path');
const merge = require('webpack-merge');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const ManifestPlugin = require('webpack-manifest-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const OptimizeCssAssetsPlugin = require('optimize-css-assets-webpack-plugin');
const TplConfigPlugin = require('webpack-tplconfig-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const AutoImportCssPlugin = require('./webpackImportCssPlugin');
const webpackBaseConfig = require('./webpack.base.config.js');
const TransferToCmdPlugin = require('./webpackTransferCmdPlugin');
const utils = require('./util.js');
const {
  webpackConfig,
  prodDirectory = 'build',
  projectGroup,
  projectName,
  moduleSeparator,
} = require('../project.config.js');

console.log(JSON.stringify(webpackConfig), prodDirectory, projectGroup);

function recursiveIssuer(m, entry_name) {
  if (m.issuer) {
    return recursiveIssuer(m.issuer);
  }
  for (const chunk of m._chunks) {
    if (entry_name === chunk.name) return chunk.name;
  }
  return false;
}

function getCacheGroups(entries) {
  const groups = {};
  for (const key in entries) {
    if ({}.hasOwnProperty.call(entries, key)) {
      groups[`${key}Style`] = {
        name: key,
        test: (m, c, entry = key) => m.constructor.name === 'CssModule'
          && recursiveIssuer(m, entry) === entry,
        enforce: true,
        reuseExistingChunk: true,
      };
    }
  }
  return groups;
}

module.exports = (env, argv) => {
  argv.mode = 'production';
  const webpack_config = utils.result({ webpackConfig }, 'webpackConfig', [
    env,
    argv,
  ]);
  if (projectName === 'nsail') {
    webpack_config.entry = {
      dht: './src/dht',
    };
  }
  return merge(
    webpackBaseConfig,
    {
      mode: 'production',
      devtool: 'none',
      output: {
        filename: '[name].[chunkHash:8].js',
        chunkFilename: '[name].[chunkHash:8].js',
        // filename: '[name].js',
        // chunkFilename: '[name].js',
        path: path.resolve(__dirname, `../${prodDirectory}`),
      },
      module: {
        rules: [
          {
            test: /\.less$/,
            use: [
              MiniCssExtractPlugin.loader,
              'css-loader',
              'postcss-loader',
              'less-loader',
              {
                loader: 'style-resources-loader',
                options: {
                  patterns: [
                    path.resolve(
                      __dirname,
                      '../src/less/variables.less',
                    ),
                  ],
                  injector: (source, resources) => `${resources
                    .map(({ content }) => content)
                    .join('\n')
                  }\n${
                    source}`,
                },
              },
            ],
            exclude: /node_modules/,
          },
          {
            test: /\.css$/,
            use: [
              MiniCssExtractPlugin.loader,
              'css-loader',
              'postcss-loader',
            ],
            exclude: /node_modules/,
          },
          {
            test: /\.(gif|jpg|png|svg)\??.*$/,
            use: [
              {
                loader: 'url-loader',
                options: {
                  limit: 8192,
                  name: 'images/[name].[hash:8].[ext]',
                },
              },
              /*  {
loader: 'image-webpack-loader',
options: {
  mozjpeg: {
    progressive: true,
    quality: 65
  },
  optipng: {
    enabled: false,
  },
  pngquant: {
    quality: '65-90',
    speed: 4
  },
  gifsicle: {
    interlaced: false,
  },
  webp: {
    quality: 75
  }
}
} */
            ],
          },
        ],
      },
      optimization: {
        minimizer: [
          new UglifyJsPlugin({
            uglifyOptions: {
              cache: true,
              parallel: true,
              mangle: {
                reserved: ['require', 'exports', 'module'],
              },
              output: {
                comments: false,
              },
              warnings: false,
              compress: {
                drop_console: true,
                drop_debugger: true,
              },
            },
          }),
          new OptimizeCssAssetsPlugin(),
        ],
        splitChunks: {
          chunks: 'all',
          minChunks: 3,
          cacheGroups: getCacheGroups(webpack_config.entry),
        },
        // splitChunks: {
        //   cacheGroups: {
        //     vendor: {
        //       chunks: "all",
        //       minChunks: 2,
        //       name: 'vendor',
        //       enforce: true,
        //       reuseExistingChunk: true
        //     },
        //   },
        // }
        // runtimeChunk: 'single'
      },
      plugins: [
        new TransferToCmdPlugin(),
        new CleanWebpackPlugin(['build'], {
          root: path.resolve(__dirname, '../'),
        }),
        new MiniCssExtractPlugin({
          filename: '[name].[contenthash:8].css',
        }),
        new ManifestPlugin({
          generate(seed, files) {
            return files.reduce((manifest, { name, path, isInitial }) => {
              if (isInitial) {
                let key = name.replace(/\.[A-Za-z]+$/, '');
                if (/\.css$/.test(name)) {
                  key = `css-${key}`;
                }
                manifest[key] = path;
              }
              return manifest;
            }, seed);
          },
        }),
        new TplConfigPlugin({
          prefix: projectName + moduleSeparator,
        }),
        new CopyWebpackPlugin([
          {
            from: path.resolve(__dirname, '../assets'),
            to: path.resolve(__dirname, '../build/assets'),
          },
        ]),
        new AutoImportCssPlugin({
          mainEntry: projectName === 'nsail' ? 'dht' : 'app',
          projectName,
        }),
      ],
    },
    webpack_config,
  );
};
