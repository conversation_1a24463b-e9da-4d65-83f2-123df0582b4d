import debug from 'debug';
import { bootstrap, vueInstall } from '@sail/core';

import { getModule } from '../modules/index';
import { getComponent } from '../components/index';

import { DModal } from '../components/common/fk-modal/DModal';
import { PreviewImage } from '../components/common/preview-image/PreviewImage';
import { openProductDetail } from '../components/product/product-detail/global-product-detail-modal';
import { showPromotionPopover, hidePromotionPopover } from '../components/promotion/popover/global-exporter';

import { OrderHooks } from '../modules/order/common/orderhooks';
import HtmlUtils from '../utils/html';
import * as LifeHook from './lifehook';

/**
 * 默认订货通APP-ID
 */
export const DEFAULT_APP_ID = 'FSAID_11490c84';

/**
 * 根据环境获取对应的AppId
 */
export function getAppId(): string {
  const map: Record<string, string> = {
    'fxiaoke.com': DEFAULT_APP_ID,
    'ceshi112.com': DEFAULT_APP_ID,
  };
  let n = window.location.hostname.split('.');
  n = n.splice(-2, 2);
  return map[n.join('.')] || DEFAULT_APP_ID;
}

/**
 * 订单再次购买
 * @param data 订单对象数据
 */
function c(data: any): Promise<void> {
  window.location.hash = `/portal${window.$dht.appId}/list/=/ShoppingCartObj?orderId=${data._id}`;
  return Promise.resolve();
}

/**
 * 订货通全局新建订单接口
 * @param data 初始表单数据
 */
function d(data: any): Promise<void> {
  return import(
    /* webpackChunkName: "order-action" */
    /* webpackMode: "lazy" */
    '../modules/order/action'
  ).then(({ add }) => {
    add(data);
  });
}

/**
 * 覆写CRM标准的详情显示方法，目的：更改产品、商品的详情显示页
 */
function s(param: any) {
  const { data, type, options } = param;
  const apiname = (data && data.apiName) || type;

  // 仅下游开启订货商城 才走定制订货通详情, 否则走crm标准详情
  if ( window.$dht.config.sail.isShopMallMode
   && (apiname === 'ProductObj' || apiname === 'SPUObj')) {
    window.$dht.openProductDetail({
      isSpuMode: apiname === 'SPUObj',
      product: {
        _id: data.crmId, // 产品用这个参数
        spu_id: data.crmId, // 商品用这个参数
      },
      options,
    });
    return null;
  }
  return CRM.api.show_crm_detail_standard(param);
}

/**
 * Hover产品时显示改产品相关促销信息
 * @param params { productId: string; }
 */
export function p(rendered: boolean, params?: any) {
  return rendered ? showPromotionPopover(params) : hidePromotionPopover();
}

export function handleValidationBlockMessage(messages: string[]): Promise<void> {
  if (!messages || messages.length <= 0) return Promise.resolve();
  let promise = Promise.resolve();
  for (let i = 0; i < messages.length; i += 1) {
    promise = promise.then(() => {
      Vue.prototype.$message({
        message: messages[i],
        type: 'error',
        center: true,
      });
    });
  }
  return promise;
}

export function handleValidationNonBlockMessage(messages: string[], title?: string): Promise<void> {
  if (!messages || messages.length <= 0) return Promise.resolve();
  let promise = Promise.resolve();
  for (let i = 0; i < messages.length; i += 1) {
    promise = promise.then(() => {
      return Vue.prototype.$confirm(messages[i], title || $t('提示'), {
        confirmButtonText: $t('确定'),
        cancelButtonText: $t('取消'),
      }).then(() => {
        // 确定的话，将继续下一个弹窗
      }).catch(() => {
        // 有一个取消，就直接全部取消了
        throw new Error(String(i));
      });
    });
  }
  return promise;
}

/**
 * 获取CRM插件配置信息
 */
export function getCrmAllConfig(): Promise<void> {
  return new Promise((resolve, reject) => {
    CRM.util.getCrmAllConfig(() => {
      // const cacheConfig = CRM._cache;
      // const dhtConfig = $dht.config;
      resolve();
    }, (err: any) => {
      reject(err);
    });
  });
}

/**
 * 初始化订货通应用全局变量
 */
export function init() {
  let $dht = window.$dht;
  if ($dht === undefined) {
    $dht = bootstrap({
      appId: getAppId(),
      name: 'DhtCore',
    }, window);
    window.$dht = $dht;
  }

  $dht.env = null;
  $dht.envProvider = null;
  $dht.upstreamEa = Fx.util.getQueryStringArgs().ea || $.cookie('ERUpstreamEa');

  $dht.log = debug('@nsail:logger');
  /**
   * 缓存CRM的组件
   */
  $dht._crmComponents = {};
  /**
   * 复制订单，即再次购买
   */
  $dht.copyOrder = c;
  /**
   * 新建订单
   */
  $dht.createOrder = d;
  /**
   * 显示促销详情浮层
   */
  $dht.togglePromotionPopover = p;
  /**
   * 显示CRM对象详情
   */
  $dht.showCrmDetail = s;
  /**
   * 显示商品详情
   */
  $dht.openProductDetail = openProductDetail;
  /**
   * 处理验证规则返回的阻塞消息
   */
  $dht.handleValidationBlockMessage = handleValidationBlockMessage;
  /**
   * 处理验证规则返回的非阻塞消息
   */
  $dht.handleValidationNonBlockMessage = handleValidationNonBlockMessage;
  /**
   * 挂载生命周期钩子，以便在首页组件中可以使用到
   */
  $dht.lifeHook = LifeHook;

  // 全局挂接的两个UI接口，从`Vue.prototype`移到这里来
  $dht.modal = new DModal({});
  $dht.previewImage = new PreviewImage();

  $dht.getModule = getModule;
  $dht.getComponent = getComponent;

  _.extend($dht, HtmlUtils);
  _.extend($dht, OrderHooks());

  Vue.use(vueInstall, { ignoreAppendSail: true, ignoreAppendException: true });
}

/**
 * 初始化工具栏购物车飘数
 */
export function initShopCartNumber() {
  const topNav = window.Fx.topNav;
  if (!topNav) {
    return;
  }

  const list = $dht.store.getCartList() || [];
  topNav.set('tools', 'dhtShopCart', 'number', list.length);
}
