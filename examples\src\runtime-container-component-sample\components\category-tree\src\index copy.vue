<template>
  <div class="dhtbiz-category-tree">
    <!-- 分类树头部 -->
    <div class="category-header">
      <h3 class="category-title">商品分类</h3>

    </div>

    <!-- 当前选中分类路径 -->
    <div v-if="selectedCategory.path.length > 0" class="category-breadcrumb">
      <fx-breadcrumb>
        <fx-breadcrumb-item>
          <span>全部分类</span>
        </fx-breadcrumb-item>
        <fx-breadcrumb-item
          v-for="(item, index) in selectedCategory.path"
          :key="item.id">
          <a v-if="index < selectedCategory.path.length - 1"
             @click="handleCategorySelect(item)">
            {{ item.name }}
          </a>
          <span v-else>{{ item.name }}</span>
        </fx-breadcrumb-item>
      </fx-breadcrumb>
    </div>

    <!-- 分类树 -->
    <div class="category-tree-container">
      <fx-tree
        :data="treeData"
        :props="treeProps"
        :expand-on-click-node="false"
        :highlight-current="true"
        :current-node-key="selectedCategory.id"
        @node-click="handleNodeClick">
        <template #default="{ node, data }">
          <div class="tree-node">
            <span class="node-label">{{ data.name }}</span>
            <span v-if="data.count" class="node-count">({{ data.count }})</span>
          </div>
        </template>
      </fx-tree>
    </div>
  </div>
</template>

<script>
export default {
  name: 'dht_web_product_list_category_tree',
  props: {
    // 从容器组件传入的事件总线
    eventBus: {
      type: Object,
      required: true
    },
    // 从容器组件传入的业务数据
    bizPageData: {
      type: Object,
      required: true
    },
    // 从容器组件传入的方法
    containerMethods: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      // 分类树数据
      treeData: [],
      // 树组件配置
      treeProps: {
        children: 'children',
        label: 'name'
      }
    };
  },

  computed: {
    // 当前选中的分类（从bizPageData中获取）
    selectedCategory() {
      return this.bizPageData.category;
    },

    // 事件类型常量
    EVENT_TYPES() {
      return this.containerMethods.getEventTypes();
    }
  },

  created() {
    this.initEventListeners();
    this.loadCategoryTree();
  },

  beforeDestroy() {
    this.removeEventListeners();
  },

  methods: {
    /**
     * 初始化事件监听
     */
    initEventListeners() {
      // 暂无需要监听的事件
    },

    /**
     * 移除事件监听
     */
    removeEventListeners() {
      // 暂无需要移除的事件监听
    },

    /**
     * 加载分类树数据
     */
    async loadCategoryTree() {
      try {
        // 这里应该调用实际的分类API
        // const response = await this.callCategoryApi();

        // 模拟分类数据
        this.treeData = [
          {
            id: 1,
            name: '电子产品',
            count: 150,
            children: [
              { id: 11, name: '手机', count: 50 },
              { id: 12, name: '电脑', count: 30 },
              { id: 13, name: '平板', count: 20 }
            ]
          },
          {
            id: 2,
            name: '服装鞋帽',
            count: 200,
            children: [
              { id: 21, name: '男装', count: 80 },
              { id: 22, name: '女装', count: 90 },
              { id: 23, name: '童装', count: 30 }
            ]
          },
          {
            id: 3,
            name: '家居用品',
            count: 100,
            children: [
              { id: 31, name: '家具', count: 40 },
              { id: 32, name: '家电', count: 35 },
              { id: 33, name: '装饰', count: 25 }
            ]
          }
        ];
      } catch (error) {
        console.error('CategoryTree: 加载分类树失败', error);
      }
    },

    /**
     * 处理节点点击
     * @param {Object} data - 节点数据
     * @param {Object} node - 节点对象
     */
    handleNodeClick(data, node) {
      console.log('CategoryTree: 节点点击', data, node);

      // 构建分类路径
      const path = this.buildCategoryPath(node);

      // 触发分类变更事件
      this.eventBus.$emit(this.EVENT_TYPES.CATEGORY_CHANGE, {
        id: data.id,
        name: data.name,
        path: path
      });
    },

    /**
     * 处理分类选择（面包屑点击）
     * @param {Object} category - 分类对象
     */
    handleCategorySelect(category) {
      console.log('CategoryTree: 分类选择', category);

      // 触发分类变更事件
      this.eventBus.$emit(this.EVENT_TYPES.CATEGORY_CHANGE, {
        id: category.id,
        name: category.name,
        path: this.selectedCategory.path.slice(0,
          this.selectedCategory.path.findIndex(item => item.id === category.id) + 1
        )
      });
    },



    /**
     * 构建分类路径
     * @param {Object} node - 当前节点
     * @returns {Array} 分类路径数组
     */
    buildCategoryPath(node) {
      const path = [];
      let currentNode = node;

      while (currentNode) {
        path.unshift({
          id: currentNode.data.id,
          name: currentNode.data.name
        });
        currentNode = currentNode.parent;
      }

      return path;
    }
  }
};
</script>

<style lang="less" scoped>
.dhtbiz-category-tree {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  // 分类头部
  .category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;

    .category-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
  }

  // 面包屑导航
  .category-breadcrumb {
    margin-bottom: 16px;
    padding: 8px 12px;
    background-color: #f5f5f5;
    border-radius: 4px;

    a {
      color: #1890ff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  // 分类树容器
  .category-tree-container {
    flex: 1;
    overflow-y: auto;

    // 树节点样式
    .tree-node {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .node-label {
        flex: 1;
      }

      .node-count {
        color: #999;
        font-size: 12px;
        margin-left: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dhtbiz-category-tree {
    .category-header {
      .category-title {
        font-size: 14px;
      }
    }
  }
}
</style>