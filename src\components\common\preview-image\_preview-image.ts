// @vue/component
export default {
  name: 'DhtPreviewImage',

  components: {},

  mixins: [],

  props: {
    config: Object,
  },

  data() {
    return {
      visible: false,
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {
    const me = this as any;
    me.$nextTick(() => {
      me.show(true);
    });
  },

  methods: {
    show(visible: boolean) {
      const me = this as any;
      me.visible = visible;
    },

    onHide() {
      const me = this as any;
      me.$emit('hide');
    },
  },
};
