<template>
  <div class="dht-shop-list">
    <!-- 使用 v-if 确保数据加载后再渲染 -->
    
    当前选中分类: {{ JSON.stringify(category) }}

    <div v-if="isDataReady">
      <!-- 列表控制区域 -->
      <div class="list-controls">
        <!-- 排序选择 -->
        <fx-select
          v-model="sortBy"
          @change="handleSortChange"
          placeholder="排序方式"
          :options="selectOptions"
          style="width: 120px; margin-right: 10px;">
        </fx-select>

        <!-- 视图模式切换 -->
        <fx-button-group>
          <fx-button 
            :type="viewMode === 'grid' ? 'primary' : ''" 
            @click="viewMode = 'grid'">
            网格
          </fx-button>
          <fx-button 
            :type="viewMode === 'list' ? 'primary' : ''" 
            @click="viewMode = 'list'">
            列表
          </fx-button>
        </fx-button-group>
      </div>

      <!-- 加载状态 -->
      <div v-if="listData.loading" class="loading-container">
        <fx-spin size="large">
          <div class="loading-text">正在加载商品...</div>
        </fx-spin>
      </div>

      <!-- 商品列表 -->
      <div v-else-if="listData.data && listData.data.length > 0"
           :class="['product-list', `view-mode-${viewMode}`]">
        <div
          v-for="product in listData.data"
          :key="product.id"
          class="product-item"
          @click="handleProductClick(product)">

          <!-- 网格视图 -->
          <template v-if="viewMode === 'grid'">
            <div class="product-image">
              <img :src="product.image || '/default-product.jpg'" :alt="product.name">
            </div>
            <div class="product-info">
              <h3 class="product-name">{{ product.name }}</h3>
              <div class="product-price">¥{{ product.price }}</div>
              <div class="product-actions">
                <fx-button type="primary" size="small" @click.stop="handleAddToCart(product)">
                  加入购物车
                </fx-button>
              </div>
            </div>
          </template>

          <!-- 列表视图 -->
          <template v-else>
            <div class="product-image-list">
              <img :src="product.image || '/default-product.jpg'" :alt="product.name">
            </div>
            <div class="product-info-list">
              <h3 class="product-name">{{ product.name }}</h3>
              <div class="product-desc">{{ product.description }}</div>
              <div class="product-meta">
                <span class="product-price">¥{{ product.price }}</span>
                <span class="product-sales">已售 {{ product.sales || 0 }}</span>
              </div>
            </div>
            <div class="product-actions-list">
              <fx-button type="primary" @click.stop="handleAddToCart(product)">
                加入购物车
              </fx-button>
            </div>
          </template>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-container">
        <fx-empty description="暂无商品数据">
          <fx-button type="primary" @click="handleRefresh">刷新</fx-button>
        </fx-empty>
      </div>

      <!-- 分页 -->
      <div v-if="listData.total > 0" class="pagination-container">
        <fx-pagination
          :current-page.sync="currentPage"
          :page-size="listData.pageSize"
          :total="listData.total"
          @current-change="handlePageChange"
          @size-change="handlePageSizeChange"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper">
        </fx-pagination>
      </div>
    </div>
    <div v-else>
      <!-- 加载占位符 -->
      <fx-skeleton :rows="5" animated></fx-skeleton>
    </div>
  </div>
</template>

<script>
export default {
  name: 'dht_web_product_list_shop_list',
  props: {
    dhtPageData: {
      type: Object,
      default: () => ({})
    },
    dhtContainerApi: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      // 初始化为空对象或默认值
      listData: null,
      dhtContainerApi: null,
      dhtPageEventTypes: null,
      
      // 本地状态
      viewMode: 'grid',
      sortBy: 'default',
      currentPage: 1
    };
  },
  
  computed: {
    category() {
      return this.dhtPageData?.category;
    },
    // 检查数据是否已准备好
    isDataReady() {
      return this.listData !== null && this.dhtContainerApi !== null;
    },
    selectOptions() {
      return [
        { value: 'default', label: '默认排序' },
        { value: 'price_asc', label: '价格升序' },
        { value: 'price_desc', label: '价格降序' },
        { value: 'sales', label: '销量优先' }
      ];
    }
  },
  
  watch: {
    // 'dhtPageData.category': {
    //   handler(newVal) {
    //     console.log('ShopList: dhtPageData.category 已更新', newVal);
    //   },
    //   deep: true
    // }

    // 监听listData变化
    listData: {
      handler(newVal) {
        if (newVal) {
          console.log('ShopList: listData 已更新', newVal);
          // 同步当前页码
          this.currentPage = newVal.page || 1;
        }
      },
      deep: true
    }
  },
  
  methods: {
    /**
     * 处理排序变化
     * @param {String} value - 排序方式
     */
    handleSortChange(value) {
      console.log('ShopList: 排序变化', value);
      this.sortBy = value;
      
      // 触发列表刷新事件
      this.$emit('sort-change', { sortBy: value });
    },
    
    /**
     * 处理分页变化
     * @param {Number} page - 页码
     */
    handlePageChange(page) {
      console.log('ShopList: 分页变化', page);
      this.currentPage = page;
      
      // 触发分页变更事件
      this.$emit('page-change', { page });
    },
    
    /**
     * 处理分页大小变化
     * @param {Number} pageSize - 分页大小
     */
    handlePageSizeChange(pageSize) {
      console.log('ShopList: 分页大小变化', pageSize);
      
      // 触发分页大小变更事件
      this.$emit('page-size-change', { 
        page: 1, // 重置到第一页
        pageSize 
      });
    },
    
    /**
     * 处理刷新
     */
    handleRefresh() {
      console.log('ShopList: 刷新列表');
      
      // 触发刷新事件
      this.$emit('refresh');
    },
    
    /**
     * 处理商品点击
     * @param {Object} product - 商品对象
     */
    handleProductClick(product) {
      console.log('ShopList: 商品点击', product);
      
      // 触发商品点击事件
      this.$emit('product-click', product);
    },
    
    /**
     * 处理加入购物车
     * @param {Object} product - 商品对象
     */
    handleAddToCart(product) {
      console.log('ShopList: 加入购物车', product);
      
      // 阻止事件冒泡
      event.stopPropagation();
      
      // 触发加入购物车事件
      this.$emit('add-to-cart', product);
      
      // 显示成功提示
      this.$message.success(`${product.name} 已加入购物车`);
    }
  }
};
</script>

<style scoped>
.dht-shop-list {
  padding: 15px;
}

.list-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-text {
  margin-top: 10px;
  color: #909399;
}

.product-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.view-mode-grid .product-item {
  width: calc(25% - 20px);
  margin: 10px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
}

.view-mode-grid .product-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.view-mode-grid .product-image {
  height: 200px;
  overflow: hidden;
}

.view-mode-grid .product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.view-mode-grid .product-info {
  padding: 10px;
}

.view-mode-grid .product-name {
  margin: 0 0 10px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.view-mode-grid .product-price {
  font-size: 18px;
  color: #F56C6C;
  margin-bottom: 10px;
}

.view-mode-grid .product-actions {
  display: flex;
  justify-content: center;
}

.view-mode-list .product-item {
  width: 100%;
  display: flex;
  margin-bottom: 15px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
}

.view-mode-list .product-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.view-mode-list .product-image-list {
  width: 150px;
  height: 150px;
  overflow: hidden;
}

.view-mode-list .product-image-list img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.view-mode-list .product-info-list {
  flex: 1;
  padding: 15px;
}

.view-mode-list .product-name {
  margin: 0 0 10px;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.view-mode-list .product-desc {
  margin-bottom: 10px;
  color: #606266;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.view-mode-list .product-meta {
  display: flex;
  align-items: center;
}

.view-mode-list .product-price {
  font-size: 18px;
  color: #F56C6C;
  margin-right: 15px;
}

.view-mode-list .product-sales {
  font-size: 14px;
  color: #909399;
}

.view-mode-list .product-actions-list {
  display: flex;
  align-items: center;
  padding: 15px;
}

.empty-container {
  padding: 40px 0;
  text-align: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style> 


