<script lang="ts">
  import BaseMixin from '../mixins/base';
  import { getHashQuery } from '../../utils/url';
  
  /**
   * 订货通业务组件独立页面
   */
  const Component: VueComponent = {
    name: 'DhtModuleDhtbiz',
  
    mixins: [BaseMixin],
  
    methods: {
      renderComponent() {
        console.log('DhtModuleDhtbiz renderComponent');

        const cmpName = this.getComponentNameFromPath(window.location.hash);
        if (!cmpName) {
          console.warn('DhtModuleDhtbiz renderComponent cmpName is empty');
          return;
        }
        Fx.getBizComponent('dhtbiz', cmpName).then(async (iComp: any) => {
          // 如果Comp是promise，则需要等待Comp完成后再渲染
          let Comp = iComp;
          // 如果iComp是函数(动态import返回的)，则需要执行它
          if (typeof iComp === 'function' && iComp.beecraft) {
            const module = await iComp();
            // 获取默认导出
            Comp = module.default || module;

            // 确保Comp是Vue组件
            if (!Comp.options && !Comp.template) {
              Comp = Vue.extend(Comp);
            }
          }          

          this.$el.classList.add('dht-dhtbiz-page');
          const wrapper = document.createElement('div');
          this.$el.appendChild(wrapper);
          const query = getHashQuery(window.location.hash);
          console.log('DhtModuleDhtbiz getBizComponent', Comp);
          let propsData = {};            
          
          if(['DhtProductDetail', 'dht_web_product_detail_all'].includes(cmpName) ) {
            propsData = {
              isSpuMode: window.$dht.config.sail.isSpuMode,
              isUseByModal: false,
              product: {
                spu_id: query.spu_id || '',
                _id: query._id || '',
              },
            }
          }

          propsData = Object.assign({}, propsData, query);

          this.$$c.testInstance = new Comp({
            el: wrapper,            
            propsData,
          });
        });
      },
      /**
       * 从路径中获取组件名称, 
       * hash='#/portal/depend/dht-api-dhtbiz-compName?_id=123', 
       * 则返回'compName'
       * @param path 路径
       * @returns 组件名称
       */
      getComponentNameFromPath(path: string): string {
        const match = path.match(/dht-api-dhtbiz-(\w+)/);
        return match ? match[1] : '';
      },
    },
  };
  
  export default Component;
  </script>
  <style>
    .dht-test-page {
      overflow: auto !important;
      background: white;
    }
  </style>
  