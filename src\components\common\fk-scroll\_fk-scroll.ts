// @vue/component
export default Vue.extend({
  name: 'FkScroll',

  components: {},

  mixins: [],

  props: {
    offset: {
      type: Number,
      default: 44,
    },
    onRefresh: {
      type: Function,
      default: undefined,
      required: false,
    },
    onInfinite: {
      type: Function,
      default: undefined,
      require: false,
    },
  },

  data() {
    return {
      top: 0,
      state: 0, // 0:down, 1: up, 2: refreshing
      startY: 0,
      touching: false,
      infiniteLoading: false,
    };
  },

  computed: {},

  watch: {},

  created() {},

  methods: {
    touchStart(e: TouchEvent) {
      const me = this as any;
      me.startY = e.targetTouches[0].pageY;
      me.touching = true;
    },
    mouseDown(e: MouseEvent) {
      const me = this as any;
      me.startY = e.pageY;
      me.touching = true;
    },
    touchMove(e: TouchEvent) {
      const me = this as any;
      if (me.$el.scrollTop > 0 || !me.touching) {
        return;
      }
      const diff = e.targetTouches[0].pageY - me.startY;
      if (diff > 0 && e.cancelable) e.preventDefault();
      // eslint-disable-next-line no-restricted-properties
      me.top = Math.pow(diff, 0.8) + (me.state === 2 ? me.offset : 0);

      if (me.state === 2) { // in refreshing
        return;
      }
      if (me.top >= me.offset) {
        me.state = 1;
      } else {
        me.state = 0;
      }
    },
    mouseMove(e: MouseEvent) {
      const me = this as any;
      if (me.$el.scrollTop > 0 || !me.touching) {
        return;
      }
      const diff = e.pageY - me.startY;
      if (diff > 0 && e.cancelable) e.preventDefault();
      // eslint-disable-next-line no-restricted-properties
      me.top = Math.pow(diff, 0.8) + (me.state === 2 ? me.offset : 0);

      if (me.state === 2) { // in refreshing
        return;
      }
      if (me.top >= me.offset) {
        me.state = 1;
      } else {
        me.state = 0;
      }
    },
    touchEnd() {
      const me = this as any;
      me.touching = false;
      if (me.state === 2) { // in refreshing
        me.state = 2;
        me.top = me.offset;
        return;
      }
      if (me.top >= me.offset) { // do refresh
        this.refresh();
      } else { // cancel refresh
        me.state = 0;
        me.top = 0;
      }
    },
    mouseUp() {
      const me = this as any;
      me.touching = false;
      if (me.state === 2) { // in refreshing
        me.state = 2;
        me.top = me.offset;
        return;
      }
      if (me.top >= me.offset) { // do refresh
        this.refresh();
      } else { // cancel refresh
        me.state = 0;
        me.top = 0;
      }
    },
    refresh() {
      const me = this as any;
      me.state = 2;
      me.top = me.offset;
      me.onRefresh(me.refreshDone);
    },
    refreshDone() {
      const me = this as any;
      me.state = 0;
      me.top = 0;
    },
    infinite() {
      const me = this as any;
      me.infiniteLoading = true;
      me.onInfinite(this.infiniteDone);
    },
    infiniteDone() {
      const me = this as any;
      me.infiniteLoading = false;
    },
    onScroll() {
      const me = this as any;
      if (me.infiniteLoading) {
        return;
      }
      const outerHeight = me.$el.clientHeight;
      const innerHeight = me.$el.querySelector('.scroll-inner').clientHeight;
      const scrollTop = me.$el.scrollTop;
      const ptrHeight = me.onRefresh ? me.$el.querySelector('.pull-to-refresh-layer').clientHeight : 0;
      const infiniteHeight = me.$el.querySelector('.infinite-layer').clientHeight || 40;
      const bottom = innerHeight - outerHeight - scrollTop - ptrHeight;

      if (bottom < infiniteHeight) this.infinite();
    },
    scrollToTop() {
      const me = this as any;
      me.$refs.fkScroll.scrollTop = 0;
    },
  },
});
