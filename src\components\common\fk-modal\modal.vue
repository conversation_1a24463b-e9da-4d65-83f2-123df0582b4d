<template>
  <fx-dialog
    :title="title"
    :visible="visible"
    :close-on-click-modal="maskClick"
    :append-to-body="false"
    :show-close="!forbidClose"
    :before-close="closeModal"
    :sliderPanel="sliderPanel"
    :width="innerWidth"
    :max-height="maxHeight"
    :custom-class="'dht-modal-dialog'"
    :class="{'dht-dialog__fullscreen-wrapper': sliderPanel}"
    @closed="closed">
      <template slot="title">
        <div class="dht-modal-dialog-header">
          <div class="dht-modal-dialog-title">
            <span class="el-dialog__title">{{title}}</span>
            <span class="dht-next-button-group" v-if="btnGroup.indexOf('next') !== -1">
              <fx-button-group>
                <fx-button
                  class="dht-modal-dialog-prev-btn"
                  type="primary"
                  size="mini"
                  :title="$t('上一条')"
                  icon="el-icon-arrow-left"
                  @click="modalRequestPrev"></fx-button>
                <fx-button
                  class="dht-modal-dialog-next-btn"
                  type="primary"
                  size="mini"
                  :title="$t('下一条')"
                  icon="el-icon-arrow-right"
                  @click="modalRequestNext"></fx-button>
              </fx-button-group>
            </span>
          </div>
          <div class="dht-modal-dialog-btn-group">
            <button
              v-if="btnGroup.indexOf('refresh') !== -1"
              type="button"
              class="el-dialog__headerbtn"
              :title="$t('刷新')"
              @click="onRefresh">
              <i class="el-icon-refresh-right"></i>
            </button>
            <button
              v-if="btnGroup.indexOf('fullscreen') !== -1"
              type="button"
              class="el-dialog__headerbtn"
              :title="$t('全屏')"
              style="font-size: 12px;"
              @click="toggleFullScreen">
              <i class="el-icon-full-screen"></i>
            </button>
          </div>
        </div>
      </template>
      <div class="dht-dialog-content" :style="{minHeight, height}">
        <template v-if="config.component">
          <component
            ref="componentRef"
            :is="config.component"
            v-bind="config.componentParams || {}"
            @on-close="closeModal"
            @on-ok="confirm"
            @modal-set-title="setTitle"
          ></component>
        </template>
        <template v-else>
          <div v-html="config.content"></div>
        </template>
      </div>
      <div slot="footer" class="dialog-footer" v-if="!hiddenFooter">
        <fx-button
          type="primary"
          @click.stop="confirm"
          size="small"
          :loading="loading"
          :disabled="disabled">{{okText}}</fx-button>
        <fx-button
          v-if="!forbidClose"
          @click.stop="closeModal"
          size="small"
          :disabled="disabled">{{cancelText}}</fx-button>
      </div>
  </fx-dialog>
</template>

<script lang="ts">
import { isFunction, isPromise } from './utils';

export default Vue.extend({
  data: () => {
    return {
      disabled: false,
      loading: false,
      visible: false,
      title: '',
      innerWidth: '',
      initWidth: '',
    };
  },
  props: {
    config: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    minHeight(): string {
      const me = this as any;
      return me.config.minHeight || '200px';
    },
    maxHeight(): string | null {
      const me = this as any;
      return me.config.maxHeight || null;
    },
    height(): string | null {
      const me = this as any;
      return me.config.height || null;
    },
    width(): string | null {
      const me = this as any;
      return me.config.width || '640px';
    },
    maskClick(): boolean {
      const me = this as any;
      return me.config.maskClick || false;
    },
    onOk(): Function {
      const me = this as any;
      return isFunction(me.config.onOk) ? me.config.onOk : () => true;
    },
    onClose(): Function {
      const me = this as any;
      return isFunction(me.config.onClose) ? me.config.onClose : () => true;
    },
    okText(): string {
      const me = this as any;
      return me.config.okText || $t('确 定');
    },
    cancelText(): string {
      const me = this as any;
      return me.config.cancelText || $t('取 消');
    },
    hiddenFooter(): boolean {
      const me = this as any;
      return typeof me.config.hiddenFooter === 'boolean' ? me.config.hiddenFooter : false;
    },
    forbidClose(): boolean {
      const me = this as any;
      return typeof me.config.forbidClose === 'boolean' ? me.config.forbidClose : false;
    },
    sliderPanel(): boolean {
      const me = this as any;
      return typeof me.config.sliderPanel === 'boolean' ? me.config.sliderPanel : false;
    },
    btnGroup(): Array<string> {
      const me = this as any;
      return me.sliderPanel && Array.isArray(me.config.btnGroup) ? me.config.btnGroup : [];
    },
  },

  created() {
    this.title = this.config.title || '';
    this.initWidth = this.config.width || '640px';
    this.innerWidth = this.initWidth;
  },

  mounted() {
    this.$nextTick(() => {
      this.show();
    });
  },

  methods: {
    closeModal() {
      const me = this as any;
      if (me.forbidClose) return;
      this.beforeClose('onClose');
    },
    confirm() {
      this.beforeClose('onOk');
    },
    beforeClose(type: 'onClose' | 'onOk') {
      const me = this as any;
      const componentRef = me.$refs.componentRef || null;
      const p = me[type](componentRef);
      if (isPromise(p)) {
        this.lock();
        p.then(() => {
          this.lock(false);
          me.$emit('close');
        }).catch(() => {
          this.lock(false);
        });
      } else {
        p === true && me.$emit('close');
      }
    },
    lock(bool = true) {
      const me = this as any;
      me.disabled = bool;
      me.loading = bool;
    },
    show(bool = true) {
      const me = this as any;
      me.visible = bool;
    },
    closed() {
      const me = this as any;
      me.$destroy();
      me.$el.parentNode.removeChild(me.$el);
    },
    /**
     * 设置标题
     * @param title
     */
    setTitle(title: string) {
      const me = this as any;
      me.title = title;
    },
    /**
     * 切换全屏模式
     */
    toggleFullScreen() {
      const me = this as any;
      me.innerWidth = me.innerWidth === me.initWidth ? '100%' : me.initWidth;
    },
    setWidth(width: string) {
      const me = this as any;
      if (me.innerWidth === '100%') return;
      me.initWidth = width;
      me.innerWidth = me.initWidth;
    },
    /**
     * 刷新
     */
    onRefresh() {
      const me = this as any;
      me._handlerBtnEvent('modalRequestRefresh');
    },
    /**
     * 上一条
     */
    modalRequestPrev() {
      const me = this as any;
      me._handlerBtnEvent('modalRequestPrev');
    },
    /**
     * 下一条
     */
    modalRequestNext() {
      const me = this as any;
      me._handlerBtnEvent('modalRequestNext');
    },
    /**
     * 按钮事件处理
     * @param fnName：函数名称
     * @private
     */
    _handlerBtnEvent(fnName: string) {
      const me = this as any;
      if (typeof me.$refs.componentRef[fnName] === 'function') {
        me.$refs.componentRef[fnName]();
      } else {
        console.error(`component has not function: ${fnName}`);
      }
    },
  },
});
</script>
<style lang="less">
.dht-modal-dialog {
  .el-dialog__body {
    overflow-y: auto;
  }
  &-header {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
  &-title {
    display: flex;
    align-items: center;
    flex: 1;
    .el-dialog__title {
      margin-right: 10px;
    }
  }
  &-btn-group {
    display: flex;
    align-items: center;
    .el-dialog__headerbtn {
      position: relative;
      right: 0;
      top: 0;
      color: black;
      font-weight: 700;
      margin-right: 10px;
      &:hover {
        color: #ff8000;
      }
    }
  }
  .el-dialog__header .el-icon-close{
    font-size: 18px!important;
  }
}

.dht-modal-dialog-prev-btn,
.dht-modal-dialog-next-btn {
  width: 24px;
  height: 24px;
  color: #181c25;
  background-color: #f2f4fb;
  border-color: #f2f4fb;
  padding: 5px !important;
  margin: 0 !important;
}

.dht-dialog__fullscreen-wrapper {
  overflow: initial;
  .dht-modal-dialog {
    display: flex;
    flex-direction: column;
    overflow: initial;
  }
  .el-dialog__body {
    height: 100%;
  }
}
</style>
