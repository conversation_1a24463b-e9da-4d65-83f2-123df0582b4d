export default {
  name: 'DhtPromotionGiftSelector',
  props: {
    readonly: {
      type: Boolean,
      default: false,
    },
    gifts: {
      type: Array,
      default: () => [],
    },
    selected: {
      type: Array,
      default: () => [],
    },
    optionalCount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      items: [],
    };
  },
  computed: {
    isShowPictureMode() {
      return $dht.config.sail.isShowPictureMode;
    },
    checkedCount(): number {
      return this.items.filter((o: any) => o.checked).length;
    },
    captionText(): string {
      return this.readonly
        ? `任选${this.optionalCount}种`
        : `可选 ${this.optionalCount} 种，已选 ${this.checkedCount} 种`;
    },
  },
  created() {
    this.items = [];
    this.getDataList();
  },
  methods: {
    getDataList() {
      const skuGiftIds: string[] = [];
      const spuGiftIds: string[] = [];
      _.each(this.gifts, (gift: any) => {
        if (gift.gift_type === '1') {
          // 赠SKU
          skuGiftIds.push(gift.gift_product_id);
        } else if (gift.gift_type === '3') {
          // 赠SPU
          spuGiftIds.push(gift.gift_spu_id);
        } else {
          // 赠本品不做处理
        }
      });
      Promise.all([
        $dht.services.product.getSkusBySkuIds(skuGiftIds),
      ]).then((values: any[]) => {
        $dht.log('%o, %o', values[0], values[1]);
        const skuGiftMap = _.object(_.map(values[0].items, (item: any) => [item._id, item]));
        // const spuGiftMap = _.object(_.map(values[1], (item: any) => [item.id, item]));
        this.items = _.map(this.gifts, (gift: any) => {
          const result = { ...gift };

          if (gift.gift_type === '1') {
            const found = skuGiftMap[gift.gift_product_id];
            if (found) {
              result.picture = found.picture;
            }
          } else if (gift.gift_type === '3') {
            // const found = spuGiftMap[gift.gift_spu_id];
            // if (found) {
            //   result.productImages = found.picture;
            //   result.skus = this.readonly ? [] : _.map(found.productVos,
            //     (productVo: ObjectDataMap) => {
            //       return {
            //         id: productVo.id,
            //         name: productVo.name,
            //         price: productVo.price,
            //         pricebookPrice: productVo.pricebookPrice,
            //         quantity: 0,
            //       };
            //     });
            //   result.selectedSkuNum = 0;
            //   if (result.skus.length > 0) {
            //     // 默认将所有赠品数量都分配给第一个SKU
            //     result.skus[0].quantity = result.giftNum;
            //     result.selectedSkuNum = result.giftNum;
            //   }
            // }
          }

          // 是否被选中
          result.checked = this.selected.findIndex((item: any) => item._id === gift._id) >= 0;
          return result;
        });
      });
    },
    /**
     * 处理SKU数量输入变化，重新计算求和
     */
    handleSkuQuantityChange(item: any) {
      item.selectedSkuNum = 0;
      _.each(item.skus, (sku: any) => {
        item.selectedSkuNum += Number(sku.quantity);
      });
    },
    handleSubmit(): any[] | null {
      if (this.readonly) return [];

      if (this.checkedCount < this.optionalCount) {
        FxUI.Message(`当前可选 ${this.optionalCount} 种赠品，请完成勾选`);
        return null;
      }
      const selected = this.items.filter((o: any) => o.checked);
      let notMatched = 0;
      _.each(selected, (item: any) => {
        if (item.gift_type !== '3') return;
        if (item.selectedSkuNum !== item.gift_num) {
          notMatched += 1;
          FxUI.Message('当前选择的赠品数量不一致，请重新选择');
        } else {
          // 添加SKU的配置比，方便外部计算
          item.skuRatio = {};
          _.each(item.skus, (sku: any) => {
            item.skuRatio[sku.id] = sku.quantity;
          });
        }
      });
      if (notMatched > 0) return null;

      return selected;
    },
    getAmount(item: any) {
      // const quantityPrecision = $dht.config.meta.orderProduct.quantityPrecision;
      // return formatDigit(item.giftProductNum, '', quantityPrecision, true);
      return item.gift_num;
    },
    getImageAddr(item: any) {
      if (!item.picture || item.picture.length <= 0) {
        return this.$npath('');
      }
      const npath = item.picture[0] || '';
      return this.$npath(npath, '100*100');
    },
    toggleCheck(data: any) {
      // this.$set(data, 'checked', !data.checked);
      if (this.optionalCount === 1 && data.checked) {
        // 只能选一个赠品时，切换掉其他的选择状态
        this.items.forEach((item: any) => {
          if (item._id !== data._id) {
            item.checked = false;
          }
        });
      }
    },
  },
} as VueComponent;
