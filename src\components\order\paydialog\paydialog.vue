<template>
  <fx-dialog
    :title="$t('付款')"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="1200px"
    :before-close="closeBefore"
    @close="close"
  >
    <div class="dht-pay-dialog">
      <iframe :src="payUrl" class="iframe-wrap"></iframe>
    </div>
  </fx-dialog>
</template>

<script lang="ts">
export default {
  name: 'DhtPayDialog',

  data() {
    return {
      visible: true,
      isLoading: false,
      urlParams: {},
    };
  },
  computed: {
    payUrl() {
      const host = window.location.origin.replace('local', 'www');
      const url = `${host}/XV/Home/Index?noheader=1#app/entwallet/pay/=/param-`;
      const urlcomp = `${url}${JSON.stringify(this.urlParams)}`;
      return urlcomp;
    },
  },

  created() {},

  destroyed() {},

  methods: {
    closeBefore(done: any) {
      if (this.isLoading) return;
      this.setLoaing(true);
      this.queryPayStatus(done);
    },

    close() {
      this.visible = false;
      this.$emit('close');
    },

    // 查询付款状态
    queryPayStatus(done: any) {
      $dht.services.order.queryPayStatus({
        orderNo: this.urlParams.orderNo,
      }).then((res: any) => {
        if (res.payStatus === 1) { // 支付成功
          this.$emit('paysuccess');
        } else { // 没有支付
          this.$emit('payfail');
        }
        this.setLoaing(false);
        done();
      }, (res: any) => {
        this.setLoaing(false);
        done();
        let msg = res.Message;
        if (res.Code === '61900') msg = $t('i18n.order.cancel_payment');
        CRM.util.remind(3, msg);
      });
    },

    setLoaing(status: Boolean) {
      this.isLoading = status;
      this.isLoading ? CRM.util.showLoading_new() : CRM.util.hideLoading_new();
    },
  },
} as VueComponent;
</script>

<style lang="less">
.dht-pay-dialog {
  height: 800px;
  .iframe-wrap {
    border:none;
    width:100%;
    height:100%;
  }
}
</style>
