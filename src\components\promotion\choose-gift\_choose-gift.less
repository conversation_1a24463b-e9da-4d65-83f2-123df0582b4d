.dht-choose-gift {
  .gift-table-hd {
    line-height: 16px;
    background-color: #f0f2f5;
    color: #545861;
  }

  .gift-table-tr {
    display: flex;
    padding: 10px;
    align-items: center;
  }

  .gift-table-tr-sku {
    padding: 3px 10px;
  }

  .gift-table-td:nth-child(1) {
    width: 70%;
  }

  .gift-item {
    display: flex;
    align-items: center;
    box-sizing: border-box;

    .gift-check {
      font-size: 16px;
      margin-right: 15px;
    }

    .gift-img {
      margin-right: 10px;
      width: 48px;
      height: 48px;
      background: gray;
      line-height: 48px;
      text-align: center;
    }
  }

  .gift-item-sku {
    padding-left: 90px;
  }

  .gift-number {
    width: 100px;
    border: none;
    text-align: center;
    vertical-align: middle;
    color: #666;
    font-size: 13px;
    height: 28px;
    line-height: 28px;
    -webkit-appearance:none;
    -moz-appearance: textfield;
    outline:none;
    border-radius: 3px;
  }

  .gift-exceed-tip {
    color: #FA6400;
  }

  .gift-tip {
    position: absolute;
    left: 30px;
    bottom: 13px;
    font-size: 14px;
  }
}
