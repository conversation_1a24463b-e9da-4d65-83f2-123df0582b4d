import {
  ChannelRelation,
} from '@sail/core/esm/model/distribution/distribution.dto';
import IDistributionService from '@sail/core/esm/service/distribution/distribution.service';
import {
  getVcrmComponents,
} from './common';

class DistributionService {
  getService(): IDistributionService {
    return $dht.getService('distribution');
  }

  /**
   * 更新订货芯多级订货用户信息
   * @param relationList 渠道交易关系列表
   * @param supplierValue 所选供货商
   */
  updateUserInfo(relationList: any[] = [], supplierValue: string = '') {
    this.getService().updateUserInfo(relationList, supplierValue);
  }

  /**
   * 选择供货商
   * @param relationList 渠道交易关系
   * @param selectedPartnerId 已选择的合作伙伴
   * @returns
   */
  async selectSupplier(
    relationList: ChannelRelation[] = [],
    storedSupplier = '',
  ): Promise<string | undefined> {
    /**
     * NOTE: 组织直供类型在非多组织情况下只有一条
     * 支持多组织后会有多条
     */

    // 没有渠道交易关系，退出
    if (!relationList.length) {
      return Promise.reject(new Error($t('dht.multi_level_order.no_supplier_tip')));
    }

    // 只有一条渠道交易关系，默认选择
    if (relationList.length === 1) {
      const storedSupplierValue = this.getService().getSupplierValue(relationList[0]);

      return Promise.resolve(storedSupplierValue);
    }

    // 已经有选择的供货商
    const isInRelation = this.getService().isInRelation(storedSupplier, relationList);
    if (isInRelation) {
      return Promise.resolve(storedSupplier);
    }

    const options = this.getService().getFormatOptions(relationList);
    const [{ modalService }, singleSelect] = await getVcrmComponents(
      ['modalService'],
      ['singleSelect'],
    );

    return new Promise((resolve, reject) => {
      modalService.create({
        title: $t('dht.multi_level_order.select_supplier'),
        width: '500px',
        maxHeight: '476px', // 10 条
        forbidClose: true,
        component: singleSelect,
        componentParams: {
          list: options,
          noDataTips: $t('dht.multi_level_order.no_supplier_tip'),
        },
        onOk: (componentRef: any) => {
          const value = componentRef.save();
          if (value) {
            resolve(value);
          }

          // 关闭弹框 true
          return !!value;
        },
      });
    });
  }

  filterDistributionRecordType(recordTypeList: any[] = []) {
    return this.getService().filterDistributionRecordType(recordTypeList);
  }

  getChannelRelationList() {
    return this.getService()
      .getChannelRelationList()
      .then((res) => res.dataList);
  }
}

export default new DistributionService();
