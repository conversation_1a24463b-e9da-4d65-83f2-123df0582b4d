/* eslint-disable */
class TransferToCmdPlugin {


  apply(compiler){
    compiler.plugin('emit',(compilation, callback) => {
      // 检索每个（构建输出的）chunk：
      compilation.chunks.forEach((chunk) => {
        //入口文件都是默认首次加载的，即 canBeInitial为true 和 require.ensure 按需加载是完全不一样的

        // if(options.entryOnly && !chunk.canBeInitial()) return;
        // 检索由 chunk 生成的每个资源(asset)文件名：
        chunk.files.forEach(function(filename) {
          // 只处理入口js文件
          if(/dht(\.[^.]+)?\.js$/.test(filename) && !/hot-update\.js$/.test(filename)){
            let js_source = compilation.assets[filename].source();
            let cmdSource = `define(function(require, exports, module){${js_source}\n})`;
            compilation.assets[filename] = {
              source: () => {
                return cmdSource;
              },
              size: () => {
                return cmdSource.length;
              }
            }
          }
        });
      });
      callback();
    })
  }
}

module.exports = TransferToCmdPlugin;
