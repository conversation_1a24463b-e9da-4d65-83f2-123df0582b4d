<template>
    <div class="dht_web_shop_list-preview" v-loading="isLoading">
        <div class="layout-left" v-if="filter_layout == '2'">
            <FilterDisplay direction="column" :lineCount="6" />
        </div>
        <div class="layout-right">
            <FilterDisplay v-if="filter_layout == '1'" direction="row" :lineCount="6" />
            <div class="content-list" v-if="view_mode === 'list'">
                <div class="list-item" :class="{active: index === 1}" v-for="index in 10" :key="index">
                    <p class="list-item-td" v-for="index in 5" :key="index"></p>
                </div>
            </div>
            <div class="content-card" v-else-if="view_mode === 'card'">
                <div class="card-item" v-for="index in 4" :key="index">
                    <p class="card-item-img" v-show="isShowImg"></p>
                    <p class="card-item-title" v-show="isShowName">{{ $t('dht.shopmall_widget.spu_sku_name') }} ({{ $t('dht.shopmall_widget.example')}})</p>
                    <p class="card-item-price" v-show="isShowPrice">¥88.88 ({{ $t('dht.shopmall_widget.example')}})</p>
                    <p class="card-item-field" v-for="item in showFields" :key="item">
                        {{ formatShowField(item) }}
                    </p>
                    <p class="card-item-tags" v-show="isShowTag">
                        <span class="card-item-tag">{{$t('dht.shopmall_widget.tag_area')}}</span>
                        <span class="card-item-tag">{{$t('dht.shopmall_widget.tag_area')}}</span>
                    </p>
                    <div v-if="isShowTag && tagVal.length > 0" class="card-item-tag_top">
                        <span class="card-item-tag" v-for="(item, index) in tagVal" :key="index">{{ item }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { dhtBizModel } from '@/components/web/utils/model';
import FilterDisplay from './filter-display.vue';

export default {
    name: "ShopMallProductPre",
    inject: ['useInternalNode'], 

    components: { FilterDisplay },

    props: {
        api_name: String,
        view_mode: String,
        is_card_init: Boolean,
        card_main_info: {
            type: Object,
            default: () => {}
        },
        filter_layout: {
            type: String,
            default: "1"
        },
    },

    data() {
        return {
            isLoading: true,
            allFields: {},
            // 订货管理开启适配商品选产品
            dhtChooseSku: true,
        }
    },

    computed: {
        // 当前企业的是 sku?spu
        isSpuMode() {
            // productOpenSpu：是否开启spu
            // spuStatus：基于商品选择产品
            const { productOpenSpu, spuStatus } = CRM._cache;
            // 开启适配商品选产品，则判断商品选产品；
            // 未开启适配商品选产品，则不判断是否开启商品选产品，直接显示商品列表
            const isSpuChooseSku = !this.dhtChooseSku || spuStatus;
            // 是否开启商品模式，并开启商品选产品
            const isSpuMode = productOpenSpu && isSpuChooseSku;
            return isSpuMode;
        },
        productApiName() {
            return this.isSpuMode ? 'SPUObj' : 'ProductObj';
        },
        isShowImg() {
            return this.is_card_init || this.card_main_info.picture_apiname;
        },
        isShowName() {
            return this.is_card_init || this.card_main_info.name_apiname;
        },
        isShowPrice() {
            return this.is_card_init || this.card_main_info.price_apiname;
        },
        isShowTag() {
            return this.is_card_init || this.card_main_info.is_tag_show;
        },
        tagVal() {
            let field = this.allFields.commodity_label;
            let options = field && field.options || [];
            let arr = [];
            if(this.card_main_info.tag_apiname === 'null') {
                this.card_main_info.tag_apiname = [];
            } else {
                if (typeof(this.card_main_info.tag_apiname) === 'string') {
                    this.card_main_info.tag_apiname = [this.card_main_info.tag_apiname]
                }
            }
            _.each(options, a => {
                this.card_main_info.tag_apiname.forEach(b => {
                    if(b === a.value) {
                        arr.push(a.label);
                    }
                });
            });
            return arr;
        },
        showFields() {
            return this.is_card_init
                    ? ['unit', 'virtual_available_stock']
                    : this.card_main_info.show_fields || [];
        }
    },

    watch: {
        // card_main_info: {
        //     handle() {},
        //     deep: true
        // }
    },

    created() {
        this.initFetch();
    },

    methods: {
        async initFetch() {
            const config = await this.fetchConfigValue();
            this.dhtChooseSku = config['dht_choose_sku'] === '2';

            try {
                this.allFields = await dhtBizModel.fetchObjFields(dhtBizModel.mainObjApiName());
            } catch (error) {
                console.error('获取字段信息失败:', error);
                this.allFields = {};
            }
            this.render && this.render();
        },

        render() {
            this.isLoading = false;
        },

        formatShowField(apiname) {
            let field = this.allFields[apiname];
            if (!field) return;
            let text = '';
            switch (field.type) {
                case 'currency': text = '¥88.88'; break;
                case 'number':
                case 'count': text = '99999'; break;
                case 'date': text = 'YYYY-MM-DD'; break;
                case 'date_time':
                case 'time': text = 'YYYY-MM-DD mm:ss'; break;
                default: text = $t('文本'); break;
            }
            return `${field.label}: ${text} (${ $t('dht.shopmall_widget.example')})`;
        },

        fetchConfigValue() {
            const keys = ['dht_choose_sku'];
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: '/EM1HDHT/API/v1/object/dht_config/service/get_config_values',
                    data: {
                        isAllConfig: false,
                        keys: keys
                    },
                    success: res => {
                        if (res.Result.StatusCode === 0) {
                            const list = res.Value.values || [];
                            const obj = {};

                            // 将数组转换成对象
                            list.forEach(({ key, value }) => {
                                obj[key] = value;
                            });

                            resolve(obj);
                        } else {
                            CRM.util.error(res.Result.FailureMessage);
                            reject();
                        }
                    }
                });
            });
        },
    }
}
</script>

<style lang="less" scoped>
.dht_web_shop_list-preview {
    display: flex;
    box-sizing: border-box;
    .layout-left {
        width: 120px;
        padding-right: 12px;
    }
    .layout-right {
        flex: 1;
    }
    .content-list {
        border: 1px solid var(--color-neutrals04);
        .list-item {
            display: flex;
            padding: 15px;
            border-bottom: 1px solid var(--color-neutrals04);
            box-sizing: border-box;
        }
        .active {
            background-color: var(--color-neutrals03);
        }
        .list-item-td {
            width: 12%;
            height: 10px;
            background-color: var(--color-neutrals05);
            border-radius: 3px;
            margin-right: 50px;
        }
    }
    .content-card {
        display: flex;
        overflow: hidden;
        .card-item {
            position: relative;
            min-width: 236px;
            padding: 10px;
            margin-right: 10px;
            border: 1px solid var(--color-neutrals05);
            border-radius: 4px;
            color: var(--color-neutrals19);
        }
        .card-item-img {
            width: 100%;
            height: 211px;
            /* background: url('../../assets/images/common/defaultImg.png') no-repeat; */
             background: url('@img/defaultImg.png') no-repeat;
            background-size:cover;
        }
        .card-item-title {
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            margin-top: 6px;
        }

        .card-item-price {
            font-size: 20px;
            font-weight: 500;
        }

        .card-item-field {
            font-size: 12px;
            color: var(--color-neutrals15);
        }

         .card-item-tags {
            margin-top: 8px;
         }

        .card-item-tag {
            color: var(--color-primary06);
            border: 1px solid var(--color-primary06);
            padding: 2px;
            border-radius: 3px;
            margin-right: 5px;
        }

        .card-item-tag_top {
            position: absolute;
            top: 10px;
            left: 10px;
        }
    }
}
</style>