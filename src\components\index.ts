import debug from 'debug';
import { imports } from '../base/utils';

const log = debug('@nsail:loader');

const componentCache: Record<string, CachedVueComponent> = {};

/**
 * 注册Vue组件到Vue和本地cache里
 * @param component 组件配置
 */
function registComponent(component: { name: string; component: CachedVueComponent }) {
  log(`Load component: name = ${component.name}, %s`, typeof component.component);

  if (component.name) {
    Vue.component(component.name, component.component);
    if (_.isFunction(component.component)) {
      // 说明是异步组件
      componentCache[component.name] = component.component;
    } else {
      // 这里是为了方便给Backbone组件使用，否则直接使用Vue的标签语法即可
      componentCache[component.name] = Vue.extend(component.component);
    }
  } else {
    throw new Error('Component definition must has name');
  }
}

imports(
  (require as any).context('./', true, /\w+\/index\.ts$/), // 需要排除自身这个文件
  ({ default: component }: any) => {
    if (_.isArray(component)) {
      component.forEach((item: any) => {
        registComponent(item);
      });
    } else {
      registComponent(component);
    }
  },
);

// function isPromise(p: any): boolean {
//   return p && Object.prototype.toString.call(p) === '[object Promise]';
// }

/**
 * 从缓存获取组件对象
 * @param name 组件名称
 */
export function getComponent(name: string): Promise<VueComponent | undefined> {
  const target = componentCache[name];
  if (!target) {
    throw new Error(`Miss component: ${name}`);
  }
  if (_.isFunction(target)) {
    const isVueExtend = String(target).indexOf('_init') >= 0;
    if (isVueExtend) {
      return Promise.resolve(target as VueComponent);
    }
    const func = target as () => Promise<VueComponent>;
    return func().then((comp) => comp.default);
  }
  return Promise.resolve(target as VueComponent);
}
