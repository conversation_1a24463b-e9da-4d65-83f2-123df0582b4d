// @vue/component
export default {
  name: 'CartRecordType',

  components: {},

  mixins: [],

  props: {
    defaultVal: {
      type: String,
      default: '',
    },
    field: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {};
  },

  computed: {
    defaultLabel() {
      const me = this as any;
      const obj = _.find(me.field.options || [], (item: any) => item.api_name === me.defaultVal);
      return obj && obj.label;
    },
  },

  watch: {},

  created() {

  },

  methods: {
    selectHandle() {
      const me = this as any;
      CRM.util.waiting();
      $dht.getRecordTypeData().then(async (options: any) => {
        const [{ modalService }, RecordType] = await this.getVcrmComponents();
        CRM.util.waiting(false);
        modalService.create({
          title: $t('选择') + me.field.label,
          width: '500px',
          forbidClose: false,
          component: RecordType,
          componentParams: {
            list: options,
            defaultValue: me.defaultVal,
          },
          onOk: (componentRef: any) => {
            const value = componentRef.save();
            if (value && value !== me.defaultVal) {
              return $dht.setMainRecordType({
                api_name: value,
              }).then(() => {
                // 刷新页面
                window.location.reload();
              });
            }
            return Promise.resolve();
          },
        });
      });
    },

    getVcrmComponents(): Promise<any> {
      return new Promise((resolve) => {
        Fx.async(['vcrm/sdk'], (vcrmSdk: any) => {
          const p1 = vcrmSdk.widgetService.getService('modalService');
          const p2 = vcrmSdk.widgetService.getWidget('recordType');
          Promise.all([p1, p2]).then((result: any[]) => {
            resolve(result);
          });
        });
      });
    },
  },
};
