import { LifeHooks } from '../../../../base/hooks';

export function orderamountExtend(OrderAmount: BackboneComponent): BackboneComponent {
  const proto = OrderAmount.prototype;

  /* eslint no-param-reassign: 0 */
  OrderAmount = OrderAmount.extend({
    // ****************** 覆写 *****************
    render() {
      proto.render.apply(this, arguments);
      this.disable();
    },
    matchPromotion(e: any, options: any) {
      proto.matchPromotion.apply(this, arguments);
      this.disable();
    },
  });

  OrderAmount = OrderAmount.extend(LifeHooks());

  return OrderAmount;
}
