<template>
  <div class="dht-attach-preview">
    <div class="dht-attach-preview__attach" v-for="(attach, index) in attaches" :key="index">
      <span class="dht-attach-icon" :class="createFileIcon(attach)"></span>
      <span class="dht-attach-preview__attach-name" :title="attach.name">
        {{ attach.name }}
      </span>
      <span class="dht-attach-preview__attach-size">{{ attach.size }}</span>
      <a class="dht-attach-preview__attach-handle"
            style="right: 30px;"
            @click.prevent="previewAttach(attach)">{{ $t('预览') }}</a>
      <a class="dht-attach-preview__attach-handle" :href="attach.downUrl">{{ $t('下载') }}</a>
    </div>
  </div>
</template>

<script src="./_attach-preview.ts" lang="ts"></script>
<style src="./_attach-preview.less" lang="less" scoped></style>
