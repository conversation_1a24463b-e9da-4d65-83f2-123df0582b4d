export const getQuery = (path: string): {[key: string]: any} => {
  const search = path.substring(path.indexOf('?') + 1);
  const result: any = {};
  if (search.length) {
    const items = search.split('&');
    items.forEach((item) => {
      const temp = item.split('=');
      const key = decodeURIComponent(temp[0]);
      const value = temp.length === 2 ? decodeURIComponent(temp[1]) : true;
      if (result[key]) {
        let oldValue = result[key];
        if (!Array.isArray(oldValue)) {
          oldValue = [oldValue];
        }
        oldValue.push(value);
        result[key] = oldValue;
      } else {
        result[key] = value;
      }
    });
  }
  return result;
};

export const getHashQuery = (hash: string) => {
  return getQuery(hash);
};
