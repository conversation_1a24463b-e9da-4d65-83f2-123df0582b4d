<template>
  <fx-dialog
    :title="$t('取消订单')"
    width="500px"
    :close-on-click-outside="false"
    :visible.sync="visible"
    @close="cancel"
  >
    <fx-form ref="form" :model="form" label-width="150px" label-position="top">
      <fx-input
        required
        :label="$t('填写取消意见')"
        v-model="form.cancelReason"
        prop="cancelReason"
        type="textarea"
        :placeholder="$t('请填写取消意见（2000字以内）')"
        maxlength="2000"
        show-word-limit
        rows="4"
      ></fx-input>
    </fx-form>
    <div slot="footer" class="dialog-footer">
      <fx-button type="primary" @click="confirm" size="small" :loading="isLoading">
        {{ $t('确 定') }}
      </fx-button>
      <fx-button @click="cancel" size="small">{{ $t('取 消') }}</fx-button>
    </div>
  </fx-dialog>
</template>

<script lang="ts">
export default {
  name: 'DhtCancelOrder',

  data() {
    return {
      visible: true,
      isLoading: false,
      orderData: null,
      form: {
        cancelReason: '',
      },
    };
  },

  created() {
    $dht.log('VueComponent cartfooter created');
  },

  destroyed() {
    $dht.log('VueComponent cartfooter destroyed');
  },

  methods: {
    cancel() {
      this.visible = false;
      this.$emit('close');
    },
    confirm() {
      if (this.isLoading) return;

      this.isLoading = true;
      $dht.services.order.cancelOrder({
        cancelReason: this.form.cancelReason,
        entityId: 'SalesOrderObj',
        objectId: this.orderData._id,
        triggerType: 'Create',
      }).then((res: any) => {
        $dht.log('res: %o', res);
        this.$emit('confirm');
        this.isLoading = false;
        this.visible = false;
        this.$emit('close');
      }, (res: any) => {
        CRM.util.alert(res.Message);
        this.isLoading = false;
      });
    },
  },
} as VueComponent;
</script>
