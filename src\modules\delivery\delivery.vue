<script lang="ts">
import { getCrmListComponent } from '../../base/bridge';
import { parseParams } from '../../base/utils';
import BaseMixin from '../mixins/base';
import { ext } from './list';

const blackList = ['fktest6012', 'wjl20240901'];
let canUseNewPlugin = true;
if ($dht && blackList.includes($dht.upstreamEa)) {
  canUseNewPlugin = false;
}

const Component: VueComponent = {
  name: 'DhtModuleDelivery',

  mixins: [BaseMixin],

  data() {
    return {
      objectApiName: 'DeliveryNoteObj',
    };
  },

  methods: {
    renderComponent(): Promise<void> {
      return getCrmListComponent(this.objectApiName, 'DeliveryNoteObj', ext)
        .then((Ctor: BackboneComponent | null) => {
          if (!Ctor) {
            this.isEmpty = true;
            return;
          }

          if (canUseNewPlugin && CRM.util.isGrayListLayout(this.objectApiName)) {
            this.renderNewList(Ctor);
          } else {
            this.renderOldList(Ctor);
          }
        })
        .catch((err) => {
          console.error(err);
          this.isError = true;
        });
    },

    renderNewList(List: BackboneComponent) {
      const params = parseParams();
      const options = {};
      const listParam = [this.objectApiName, params[1]];
      const container = document.createElement('div');
      $(this.$el).empty().append(container);

      Fx.async('paas-appcustomization/runsdk', (appsutom: any) => {
        if (!appsutom || !appsutom.runningobjectlist) {
          throw new Error('未找到模块: paas-appcustomization/sdk');  //[ignore-i18n]
        }
        appsutom.runningobjectlist().then((ObjectList: any) => {
          if (params) {
            Object.assign(options, {
              param: listParam,
              source: 'list',
              apiname: this.objectApiName,
              apiName: this.objectApiName,
              recordType: params[1],
              jsPath: null,
              listModule: List,
            });
          }
          this.$$c.objectlist = ObjectList.init(container, options);
        });
      });
    },

    renderOldList(List: BackboneComponent) {
      const params = parseParams();

      this.$$c.list = new List({
        wrapper: $(this.$el),
        isEdit: false,
      });
      this.$$c.list.render([this.objectApiName, params[1]]);
    },
  },
};

export default Component;
</script>
