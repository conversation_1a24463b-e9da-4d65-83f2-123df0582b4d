// @vue/component
export default Vue.extend({
  name: 'ProductPreview',

  components: {},

  mixins: [],

  props: {
    imgList: {
      type: Array,
      default() {
        return [[
          {
            min: 'https://www.ceshi113.com/FSC/N/FileShare/ShowImage?fileId=6AA905C78D85C91DEA2363750A6B600DF712CF4D7F61EFB231336EA26BD5612A0F53264EE1D756DC70250C41626E96C6E5A5068D96D9BC873F058AA4480F13D6B4244386BF36DC26',
            max: 'https://www.ceshi113.com/FSC/N/FileShare/ShowImage?fileId=6AA905C78D85C91DEA2363750A6B600DF712CF4D7F61EFB231336EA26BD5612A0F53264EE1D756DC70250C41626E96C6E5A5068D96D9BC873F058AA4480F13D6B4244386BF36DC26',
          },
          {
            min: 'https://gd3.alicdn.com/imgextra/i1/0/TB1jAr3c8USMeJjSszcXXbnwVXa_!!0-item_pic.jpg_50x50.jpg',
            max: 'https://gd3.alicdn.com/imgextra/i1/0/TB1jAr3c8USMeJjSszcXXbnwVXa_!!0-item_pic.jpg',
          },
          {
            min: 'https://gd3.alicdn.com/imgextra/i2/1053392527/TB2bb7plXXXXXbQXXXXXXXXXXXX_!!1053392527.jpg_400x400.jpg',
            max: 'https://gd3.alicdn.com/imgextra/i2/1053392527/TB2bb7plXXXXXbQXXXXXXXXXXXX_!!1053392527.jpg',
          },
          {
            min: 'https://gd3.alicdn.com/imgextra/i1/0/TB1jAr3c8USMeJjSszcXXbnwVXa_!!0-item_pic.jpg_400x400.jpg',
            max: 'https://gd3.alicdn.com/imgextra/i1/0/TB1jAr3c8USMeJjSszcXXbnwVXa_!!0-item_pic.jpg',
          },
        ], [
          {
            min: 'static/carousel/5.jpg',
            max: 'static/carousel/5.jpg',
          },
          {
            min: 'https://img.alicdn.com/imgextra/i4/1875244423/TB26PkSXPzyQeBjy1zdXXaInpXa_!!1875244423.jpg_430x430q90.jpg',
            max: 'https://img.alicdn.com/imgextra/i4/1875244423/TB26PkSXPzyQeBjy1zdXXaInpXa_!!1875244423.jpg',
          },
        ]];
      },
    },
  },

  data() {
    return {
      x: 0,
      y: 0,
      zooming: true,
      falseValue: false,
      selectedIndex: {
        i: 0,
        j: 0,
      },
    };
  },

  computed: {
    curImageSrc() {
      const me = this as any;
      return me.imgList[me.selectedIndex.i][me.selectedIndex.j].min;
    },

    curBigImageSrc() {
      const me = this as any;
      return me.imgList[me.selectedIndex.i][me.selectedIndex.j].max;
    },

    maskPosition() {
      const me = this as any;
      return {
        left: `${me.x}px`,
        top: `${me.y}px`,
      };
    },

    previewPosition() {
      const me = this as any;
      const top = `${-2 * me.y}px`;
      const left = `${-2 * me.x}px`;
      return {
        transform: `translate3d(${left},${top},0)`,
      };
    },

    onlyOnePicture() {
      const me = this as any;
      return me.imgList.length === 1 && me.imgList[0].length === 1;
    },
  },

  watch: {},

  created() {},

  mounted() {
    this.$mask = this.$el.querySelector('.dht-product-preview-mask');
    this.$wrap = this.$el.querySelector('.dht-product-preview-poster');
    this.maskSize = this.$mask.offsetWidth;
    this.wrapSize = this.$wrap.offsetWidth;
    this.minPos = 0;
    this.maxPos = this.wrapSize - this.maskSize - 1; // -1保证遮罩不会挡住边框
    this.halfMaskSize = 0.5 * this.maskSize;
    this.maxPosBoundary = this.wrapSize - this.maskSize * 0.5;
    this.zoom = this.wrapSize / this.maskSize;

    this.$nextTick(() => {
      this.zooming = false;
    });
  },

  methods: {
    selectSpec(i: number, j: number) {
      const me = this as any;
      me.selectedIndex = { i, j };
    },

    getScrollPosition(w?: any) {
      const context = w || window;
      if (context.pageXOffset != null) {
        return { x: context.pageXOffset, y: context.pageYOffset };
      }
      return { x: 0, y: 0 };
    },

    offset(element: HTMLElement) {
      const me = this as any;
      const box = element.getBoundingClientRect();
      const scrollPos = me.getScrollPosition();
      return {
        top: box.top + scrollPos.y,
        left: box.left + scrollPos.x,
      };
    },

    doZoom(evt: MouseEvent) {
      const me = this as any;
      me.initPos();
      const x = evt.pageX - me.$posterX;
      const y = evt.pageY - me.$posterY;
      me.zooming = true;
      if (x < me.halfMaskSize) {
        me.x = me.minPos;
      } else if (x > me.maxPosBoundary) {
        me.x = me.maxPos;
      } else {
        me.x = x - me.halfMaskSize;
      }
      if (y < me.halfMaskSize) {
        me.y = me.minPos;
      } else if (y > me.maxPosBoundary) {
        me.y = me.maxPos;
      } else {
        me.y = y - me.halfMaskSize;
      }
    },

    stopZoom() {
      const me = this as any;
      me.zooming = false;
    },

    initPos() {
      const me = this as any;
      const pos = me.offset(me.$wrap);
      me.$posterX = pos.left;
      me.$posterY = pos.top;
    },

    goNext() {
      const me = this as any;
      me.$refs.specCarousel.next();
    },

    goPrev() {
      const me = this as any;
      me.$refs.specCarousel.prev();
    },
  },
});
