/**
 * 订单列表页和详情页下的通用逻辑
 * 如操作：取消订单、查询物流、确认收货、付款
 */
import debug from 'debug';
import { getComponent } from '../../../components';
import { add } from '../../payment/action';

const log = debug('@nsail:debugger');

/**
 * 订单列表页|订单详情页 通用方法
 */
export function OrderHooks(): any {
  // const crmUtil = window.CRM.util;

  return {
    formatDhtButtons(btns: any, orderData: any) {
      const outUserId = ($.cookie('EROuterUid'));
      const outOwner = orderData.out_owner && orderData.out_owner[0];
      // 该订单的外部负责人是否等于outeruid，不等于的话，没有【取消订单】的权限
      if (outOwner !== outUserId) {
        /* eslint no-param-reassign: 0 */
        btns = _.filter(btns, (a: any) => { return a.action !== 'DhtCancel__c'; });
      }

      return btns;
    },
    /**
     * 取消订单
     * @param params.btnOpts 后台返回的button配置信息
     * @param params.data 当前行的数据
     * @param params.success 回调
     */
    handleDhtCancel__c(params: any) {
      log('cancel order: %o', params);
      const $vue = this.$$vue || {};

      getComponent('CancelOrder').then((Ctor: any) => {
        if (Ctor) {
          const wrapper = document.createElement('div');
          document.body.appendChild(wrapper);
          $vue.cancelorder = new Ctor();
          $vue.cancelorder.orderData = params.data;
          $vue.cancelorder.$mount(wrapper);
          $vue.cancelorder.$on('close', (e: any) => {
            document.body.removeChild($vue.cancelorder.$el);
            $vue.cancelorder.$destroy();
            $vue.cancelorder = null;
          });
          $vue.cancelorder.$on('confirm', (e: any) => {
            params.success && params.success();
          });
        }
      });
    },
    /**
     * 付款
     * @param params.btnOpts 后台返回的button配置信息
     * @param params.data 当前行的数据
     * @param params.success 回调
     */
    handleDhtPay__c(params: any) {
      const { data } = params;
      add({
        displayName: $t('回款'),
        nonEditable: true,
        SettleType: '1',
        data: {
          CustomerID: data.account_id,
          CustomerID__r: data.account_id__r,
          CustomerTradeID: data._id,
          CustomerTradeID__r: data.name || data.TradeCode, // 兼容订单编号页面不可见的时候
          PaymentMoney: data.order_amount,
          PaymentType: '10000',
          WaitPaymentMoney: data.order_amount,
          TradeMoney: data.order_amount,
          PaymentMoneyToConfirm: 0,
          account_id: data.account_id,
          account_id__r: data.account_id__r,
          order_data_id: data._id,
          order_id: data.name || data.TradeCode,
          payment_amount: data.receivable_amount, // 将订单的待付款金额带入
          from_sales_order: true,
          order_detail_data: data,
        },
        success(type: any, paymentData: any) {
          params.success && params.success();
        },
      });
    },
    /**
     * 再次购买
     * @param params.btnOpts 后台返回的button配置信息
     * @param params.data 当前行的数据
     * @param params.success 回调
     */
    handleDhtBuyAgain__c(params: any) {
      // window.$dht.copyOrder(params.data); // 方案一：去购物车
      // 方案二：就是复制逻辑
      this._clone({
        apiname: 'SalesOrderObj',
        dataId: params.data._id,
        pageApiname: '',
        success() {
          params.success && params.success();
        },
      });
    },

    // 复制
    _clone(param: any) {
      CRM.util.waiting();
      CRM.util.FHHApi({
        url: `/EM1HNCRM/API/v1/object/${param.apiname}/action/Clone`,
        data: {
          objectDataId: param.dataId,
        },
        success(res: any) {
          CRM.util.waiting(false);
          if (res.Result.StatusCode === 0) {
            $dht.createOrder({
              isCopy: true,
              apiname: param.apiname,
              dataId: param.dataId,
              data: res.Value.objectData,
              _staticData: res.Value.objectData,
              mdData: res.Value.details,
              showDetail: true,
              title: $t('再次购买'),
              showMask: true,
              show_type: 'full',
              pageApiname: param.pageApiname,
              _from: 'clone',
              success(type: any, data: any) {
                param.success && param.success(data);
              },
              error() {
                param.error && param.error.apply(null, arguments);
              },
            });
            return;
          }
          CRM.util.alert(res.Result.FailureMessage || $t('操作失败'));
        },
        error() {
          CRM.util.waiting(false);
        },
      }, {
        errorAlertModel: 1,
      });
    },
  };
}
