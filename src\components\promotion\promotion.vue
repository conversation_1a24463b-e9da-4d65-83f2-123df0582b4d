<template>
  <object-detail
    class="faci-detail_preset"
    :apiName="apiName"
    :dataId="dataId"
    :isSlide="isSlide"
    :dataIds="dataIds"
    :top="top"
    :postData="postData"
    :extendData="extendData"
  >
  </object-detail>
</template>

<script lang="ts">
export default {
  name: 'DhtPromotionDetail',
  data() {
    return {
      apiName: 'PromotionObj',
      dataId: '6038a2aaa6552c0001affc20',
      dataIds: ['6038a2aaa6552c0001affc20', '6038a824a6552c0001b062d6', '6038a3a9a6552c0001b025e3'],
      extendData: {},
      isSlide: true,
      postData: undefined,
      top: 56,
    };
  },
};
</script>
