/**
 * 加入购物车动画
 */
export interface Position {
  top: number;
  left: number;
}

class CartAnimation {
  play(quantity: string | number, startPosition: Position): Promise<void> {
    // 返回一个promise作为动画结束的标识
    return new Promise((resolve) => {
      window.requestAnimationFrame(() => {
        const dom = this.createAnimationDom(quantity, startPosition);
        const cartButtonPosition = this.getCartButtonPosition();
        const len = Math.sqrt((cartButtonPosition.top - startPosition.top) ** 2
          + (cartButtonPosition.left - startPosition.left) ** 2);
        let duration = (len / 2000) * 1000;
        duration = Math.max(Math.min(duration, 400), 200);
        dom.style.transitionDuration = `${duration}ms`;
        document.body.appendChild(dom);

        window.requestAnimationFrame(() => {
          dom.style.top = `${cartButtonPosition.top}px`;
          dom.style.left = `${cartButtonPosition.left}px`;
          // 移除
          const timer2 = setTimeout(() => {
            clearTimeout(timer2);
            document.body.removeChild(dom);
            resolve();
          }, duration + 1000);
        });
      });
    });
  }

  createAnimationDom(quantity: string | number, start: Position) {
    const dom = document.createElement('span');
    dom.className = 'dht-cart-animation';
    dom.style.top = `${start.top}px`;
    dom.style.left = `${start.left}px`;
    dom.innerText = `+${quantity}`;
    return dom;
  }

  getCartButtonPosition(): Position {
    const cartDomButton = document.querySelector('#dht-carts-entrance');
    if (!cartDomButton) {
      throw new Error("can't find cart button.");
    }
    const clientRect = cartDomButton.getBoundingClientRect();
    return {
      top: clientRect.top - 2,
      left: clientRect.left + 77,
    };
  }
}

export const cartAnimation = new CartAnimation();
