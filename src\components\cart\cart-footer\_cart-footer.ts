// @vue/component
export default {
  name: 'DhtCartFooter',

  components: {},

  mixins: [],

  props: {},

  data() {
    return {
      selectAll: false,
      isIndeterminate: false,
      isShowDelete: false,
      invalidCount: 0,
      total: 0,
      amount: 0,
      salesAmount: 0,
    };
  },

  computed: {
    i18nSummary() {
      const me = this as any;

      const isHidePrice = window.$dht.config.sail.isHidePrice;
      if (isHidePrice) return '';

      const currency = window.$dht.config.currency.currencyFlag;
      const data:any = {
        total: `<span class="summary-total">${me.total}</span>`,
        amount: `<span class="summary-amount">${me.formatMoney(me.amount, currency)}</span>`,
        salesAmount: `<span class="summary-amount">${me.formatMoney(me.salesAmount, currency)}</span>`,
      };
      return $t('已选 {{total}} 种商品，金额总计：{{amount}}，成交金额总计：{{salesAmount}}', { data });
    },
  },

  watch: {},

  created() {},

  methods: {
    setSelected(isSelected: boolean, isIndeterminate = false) {
      const me = this as any;
      me.selectAll = isSelected;
      me.isIndeterminate = isIndeterminate;
    },

    onSelectChange(value: boolean) {
      const me = this as any;
      me.isIndeterminate = false;
      me.$emit('select-change', value);
    },

    createOrder() {
      const me = this as any;
      me.$emit('order-create');
    },

    deleteSelected() {
      const me = this as any;
      me.$emit('delete-selected');
    },

    updateCartSummary(data: {
      total: number,
      amount: number,
      salesAmount: number,
      isCheckedAll: boolean,
      isIndeterminate: boolean
    }) {
      const me = this as any;
      me.total = data.total;
      me.amount = data.amount;
      me.salesAmount = data.salesAmount;
      me.isShowDelete = !!data.total;
      me.setSelected(data.isCheckedAll, data.isIndeterminate);
    },

    formatMoney(money: string | number, currency: string): string {
      const $global: any = global;
      return $global.CRM.util.formatMoneyForCurrency(money, currency);
    },
  },
};
