import {FilterRule} from './FilterRule';
import { Widget } from './widget';

/**
 * 表格单格列的配置
 */
export interface Column {
  // 列的id
  id: string;
  // 读取数据的字段名称
  data: string;
  // 字段类型，3：金额，4：时间，10：日期，6：单选，7：多选，
  // 9：图片，11：二级及联，operate：操作列
  dataType: string;
  // 用于时间的格式字符串 默认YYYY-MM-DD HH：mm
  formatStr: string;
  // 表头名称
  title: string;
  // 宽度
  width: string;
  // 自定义类名
  className: string;
  // 是否隐藏，默认不隐藏 隐藏的会在更多列表中展示
  isHidden: boolean;
  // 是否固定
  fixed: boolean;
  // 固定列顺序，默认1
  fixedIndex: number;
  // 支持筛选
  isFilter: boolean;
  // 支持排序
  isOrderBy: boolean;
  // 排序类型值升降序
  orderValues: ['asc', 'desc'];
  // 为空时的默认值，默认--
  defaultValue: string;
  // 默认值
  placeHolder: string;
  // 自定义渲染方式，data：当前单元格，type：fixed | column，fulldata：整行数据，
  // 支持返回dom字符串
  render: (data: any, type: string, fulldata: any) => string;
  // 不支持批量编辑，默认false
  noSupportBatchEdit: boolean;
  // 未知
  returnType: number
}

/**
 * 表格配置
 */
interface TableOptions {
  // 表格挂载地方，jquery对象，默认$('body')
  $el?: Object;
  // 用户自定义表格class
  custom_className?: string;
  // 层级，默认1000
  zIndex?: number;
  // 是否自适应高度 * 不做高度限制  和 数据同高，默认false
  autoHeight?: boolean;
  // 最大高度, 默认none
  maxHeight?: string;
  // 示必填*项标示，默认false
  showRequiredTip?: boolean;
  api_name?: string;
  // 默认false
  showMask?: boolean;
  // 头部标题
  title?: string;
  // 搜索的提示文案
  searchTip?: string;
  // 搜索 批量位置，默认'T'
  termBatchPos?: string;
  // 是否显示检索区域，默认true
  showTermBatch?: boolean;
  // 是否显示过滤按钮，默认false
  showFilerBtn?: boolean;
  // 是否显示标签筛选，默认false
  showTagBtn?: boolean;
  // 是否支持搜索或关系，默认false
  filterOr?: boolean;
  // 是否显示更多列按钮，默认false
  showMoreBtn?: boolean;
  // 是否显示复选框，默认false
  showMultiple?: boolean;
  // 配置checked 选项会打开check的记忆功能，分页时会记录上一页的选择
  // {idKey: '', data: [{}..]}
  // idKey 表示 表示数据中唯一的值的key data 可配置以选中的数据
  checked?: null | any;
  // 批量区域始终展现，默认false
  showBatchBtns?: boolean;
  // 显示表格尺寸配置，默认true
  showSize?: boolean;
  // 是否为单选，默认false
  single?: boolean;
  // 单选是否允许切换，默认false
  arrowResetSingle?: false;
  // 点击行选中，默认false
  lineCheck?: boolean;
  // 允许改变列宽度，默认true
  colResize?: boolean;
  // 表格大小  暂未支持 目前默认2
  size?: '1' | '2' | '3';
  // 是否为自定义对象 业务参数 慎用，默认false
  isMyObject?: boolean;
  // 排序字段的key，如Name，多层点好拼接
  sortField?: string;
  // 排序字段值的key，如desc，多层点好拼接
  sortType?: string;
  // 排序列 {name: '', orderby: 'asc'}
  orderColumn?: null | any;
  // 新建的默认排序
  addOrderColumn?: null | any;
  // 所有列配置信息
  columns: Array<Column>;
  // 单独筛选的列
  filterAloneColumns?: null | any;
  // 所有筛选条件的列
  filterColumns?: null | any;
  // 默认筛选的数据 {aloneValues: {}, filters: []}
  filterDatas?: null | any;
  // 新建时的所有的列
  addColumns?: null | any;
  // 始终显示检索区域 由于扩展配置条件，默认false
  alwaysShowTermBatch?: boolean;
  // 表格检索配置项  参数说明 查看termbatch文件
  searchTerm?: null | any;
  // 批量操作按钮    参数说明 查看termbatch文件，默认[]
  batchBtns?: Array<any>;
  // 其他按钮位置在批量操作后 参数说明 查看termbatch文件，默认[]
  otherBtns?: Array<any>;
  // 操作按钮对象    参数说明 查看operate文件
  operate?: null | any;
  // 关键字搜索对象  参数说明 查看search文件
  search?: null | any;
  // 是否显示分页配置，默认true
  showPage?: boolean;
  // 默认总显示分页，默认false
  noAlwaysShowPage?: boolean;
  // 分页类型 pageNumber or pageTime
  pageType?: 'pageNumber';
  pageTimeKey?: 'PageTime';
  page?: {
    pageSize: number;
    pageNumber: number;
    // 默认[20, 50, 100]
    pageSizeOption: Array<number>;
  };
  // 数据请求url
  url?: string;
  // 请求类型，默认'api'
  requestType?: 'FHHApi' | 'api' | 'define';
  // 请求方法，默认'get'
  method?: string;
  // 请求错误key，默认'errorCode'
  requestCodeKey?: string;
  // 请求参数
  postData?: {[key: string]: any};
  // 不允许选中
  // {idKey: '', data: [{}..]}
  // idKey 表示 表示数据中唯一的值的key data 可配置以选中的数据
  disabledcfg?: null | any;
  // 滚动加载，默认false
  scrollLoad?: boolean;
  // 开启Y轴加载，默认false
  scrollLoadY?: boolean;
  // 开启后不会直接请求数据需要手动调用start方法开始请求，默认false
  openStart?: boolean;
  // 开启处理静态数据，默认false
  doStatic?: boolean;
  // tr上面是否显示小手，默认true
  trHandle?: boolean;
  // 默认最小列宽度，默认145
  colMinWidth?: number;
  // 不允许换行 有固定列时无效，默认true
  noAllowedWrap?: boolean;
  // 无数据提示，默认'没有数据'
  noDataTip?: string;
  // 列为空时的默认显示全局，默认'--'
  defaultValue?: string;
  // 行的回调函数
  rowCallBack?: null | Function;
  // 表格渲染完成函数
  initComplete?: null | Function;
  // 筛选和批量区域完成函数
  termBatchComplete?: null | Function;
  // 格式化数据
  formatData?: (data: any)=> ({totalCount: number; data: any});
  // 数据请求回来的回调，data：请求的数据，$el当前组件根元素
  getDataBack?: null | ((data: any, $el: HTMLElement) => any);
  // 请求表格数据结果回调方法 ( fun )
  requestResult?: null | Function;
  // ajax 请求是否弹出提示框 ，默认弹出，默认2
  errorAlertModel?: number;
  // 是否显示全选复选框，默认true
  isShowAllChecked?: boolean;
  // 是否显示加号。配合描述使用，默认false
  isAddPlusSign?: boolean;
  // 标记当前表格是否是编辑类型的，默认false
  editable?: boolean
  // lookup字段编辑，刷新监听事件
  refreshDetail?: null | any;
  // 不支持列锁定 如果你的表格业务对列的需求改造较多，建议传入true，默认false
  noSupportLock?: boolean
  // 是否显示水印，默认false
  showWaterMask?: boolean;
  // 分页显示个数，默认7
  visiblePageNums?: number;
}

/**
 * 表格类
 *
 * 事件
 * saveColumns 保存更多列事件 参数 所有数据
 * term.change 检索触发事件   参数 id 元素 事件对象
 * otherbtn.change 点击其他按钮时触发的事件 参数 元素 事件对象
 * dt.search  搜索事件
 * checkbox.click 复选框点击事件
 */
export interface Table extends FilterRule, Widget {
  options: TableOptions;

  /**
   * @desc 初始化
   */
  setup (): Table;

  /**
   * 设置请求参数，对外调用
   * @param config：要设置的参数
   * @param status: 为true时请求数据
   * @param resetPage：为true是刷新为第一页
   */
  setParam (config: {[key: string]: any}, status: boolean, resetPage: boolean): void;

  /**
   * 获取全部请求参数,第一次请求时把搜索信息 分页信息参数拼接进去
   */
  getParam (): {[key: string]: any};

  /**
   * 设置指定 key postData的值
   * @param key：参数的可以 支持 QueryInfo.FilterMainID 以.间隔
   * @param value: 值
   */
  setParamByKey (key: string, value: any): void;

  /**
   * 获取指定key的 postData的值
   * @param key 参数的可以 支持 QueryInfo.FilterMainID 以.间隔
   */
  getParamByKey (key: string): any | null;

  /**
   * 开启请求
   * @param postData：请求的数据
   */
  start (postData?: {[key: string]: any}): void;

  /**
   * 变更表格高度宽度
   */
  resize (): void;

  /**
   * 获取选中的复选按钮的数据
   */
  getCheckedData (): Array<any> | null;

  /**
   * 设置表格默认选中数据
   * @param data：要默认选择的数据，一个数组或者一个数据
   */
  setCheckedData (data: Array<any> | any): void;

  /**
   * 刷新列表, 检索默认状态选中第一项
   * @param postData: 请求的数据
   */
  refresh (postData?: {[key: string]: any}): void;

  /**
   * 获取保存的数据，用于分页前后保存选择的数据，和开启记忆选择项有关
   */
  getRemberData (): Array<any>;

  /**
   * 清空保存的数据
   */
  clearRemberData (): void;

  /**
   * 在记录选中的数据中增加选项，和开启记忆选择项有关
   * @param data: 数据
   */
  addRemberData (data: any): void;

  /**
   * 在记录选中的数据中减少选项，和开启记忆选择项有关
   * @param data：要删除的数据，
   * @param noTrigger：是否促发事件，默认false不促发
   */
  reduceRemberData (data: any, noTrigger: boolean): void;
}

