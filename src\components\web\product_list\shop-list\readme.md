## 商品列表组件
运行态对应 \vcrm\src\widgets\isolated-mall\shop-list\shop-list.vue

你可以参考 @f:\work\FS\fs-sail\dhtbiz\src\components\web\product_detail\key_info\beecraft中的文件 使用inject: ['useInternalNode'] 来代替 updateProps， 
参考旧版本文件 @f:\work\FS\fs-sail\dhtbiz\src\components\web\product_list\shop-list\dhtshopmall\allproduct 中的文件来实现。

### 预览态
设计图为：@f:\work\FS\fs-sail\dhtbiz/src\components\web\product_list\shop-list\dhtshopmall\allproduct\img\display.png
请在 @f:\work\FS\fs-sail\dhtbiz\src\components\web\product_list\shop-list\beecraft\display.vue 中实现它


### 设置态
设计图为：@f:\work\FS\fs-sail\dhtbiz/src\components\web\product_list\shop-list\dhtshopmall\allproduct\img\setting.png
请参考 @f:\work\FS\fs-sail\dhtbiz\src\components\web\product_detail\key_info\beecraft\setting.vue中的折叠收起样式实现部分
在 @f:\work\FS\fs-sail\dhtbiz/src\components\web\product_list\shop-list\beecraft\setting.vue 中，将卡片内容设置部分改为折叠样式，并按照设计稿还原卡片设置部分的样式，其余js代码逻辑保持不变，设计稿中出现,但代码中没有的部分先不添加。设计稿如下：


在setting中按照设计稿，添加基本信息，筛选，排序三部分,数据参考card_main_info获取和保存，字段设置，使用fieldselect组件实现

props: {
  source: {
    type: String,
    default: ''
  },
  level1ImgShow: {
    type: String,
    default: '1'
  },
  showAll: {
    type: String,
    default: '1'
  }
}
