import component from './content.vue';

const ROUTE_REGEXPS = [
  '/sail/:component',
  '/sail/:component/=/:params(.*?)',
  '/sail/:component/:subComponent',
  '/sail/:component/:subComponent?(/=)?/:params(.*)?',
  '/portal:appId?/:component',
  '/portal:appId?/:component/=/:params(.*)?',
  '/portal:appId?/:component/:subComponent',
  '/portal:appId?/:component/:subComponent?(/=)?/:params(.*)?',
  '*',
];

const createRoute = (path: string) => ({
  path,
  component,
});

export const createRouter = () => new VueRouter({
  routes: ROUTE_REGEXPS.map((path) => createRoute(path)),
});
