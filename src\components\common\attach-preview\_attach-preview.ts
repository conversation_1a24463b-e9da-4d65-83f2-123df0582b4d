// @vue/component
export default {
  name: 'AttachPreview',

  components: {},

  mixins: [],

  props: {
    attaches: {
      type: Array,
      default() {
        return [];
      },
    },
  },

  data() {
    return {};
  },

  computed: {},

  watch: {},

  created() {},

  methods: {
    previewAttach(file: any) {
      const me = this as any;
      // eslint-disable-next-line no-useless-escape
      const imgSuffix = (file && file.path && file.path.match(/[^\.]\w*$/) && file.path.match(/[^\.]\w*$/)[0]) || '';
      const imgPrefix = file && file.path && file.path.split('.')[0];

      if (FS.util.getUploadType && FS.util.getUploadType(`.${imgSuffix}`) === 'img') {
        const smallUrl = me.$npath(file.path, '80*80', imgPrefix);
        const bigUrl = me.$npath(file.path, '0*0', imgPrefix);
        const list = [{
          label: file.name,
          url: bigUrl,
          thumbUrl: smallUrl,
          downloadUrl: file.downUrl,
        }];
        $dht.previewImage.create({ list, active: 0 });
      } else {
        Fx.async('base-modules/file-preview/index', (FilePreview: any) => {
          FilePreview.preview({
            fileId: 1,
            fileName: file.name || file.path,
            filePath: file.path,
            zIndex: 10000,
          });
        });
      }
    },

    createFileIcon(attach: any) {
      const iconClass = CRM.util.getFileIco(attach.name);
      return {
        [attach.icoClass]: true,
        [iconClass]: true,
      };
    },
  },
};
