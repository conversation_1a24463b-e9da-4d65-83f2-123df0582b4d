import Frame from './frame.vue';
import Header from './header.vue';
import Sidebar from './sidebar.vue';

import { createRouter } from './router';

export function createSidebar(): void {
  // const Sidebar = Fx.getComponent('nheader');
  // eslint-disable-next-line no-new
  // new Sidebar({ el: $('.hd')[0] });
  new Sidebar().$mount(document.querySelector('.hd'));
}

/**
 * 创建顶栏
 */
export function createHeader(): void {
  const CHeader = Vue.extend(Header);
  new CHeader().$mount(document.querySelector('.f-g-hd'));
}

/**
 * 创建应用
 * @param provide
 * @param renderOptions
 */
export function createApp(renderOptions?: any) {
  return new Vue({
    router: createRouter(),
    render: (h: any) => h(Frame, { ...renderOptions }),
  }).$mount('#sub-tpl');
}
