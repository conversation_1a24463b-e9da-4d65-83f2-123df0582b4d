<template>
    <div class="portal-navbar">
        <div class="avatar">
            <img :src="defaultAvatar" @mouseenter="enter" @mouseleave="leave" class="profile">
        </div>
        <div class="avatar-card" v-if="showCard"  @mouseenter="enterCard" @mouseleave="leaveCard">
            <div class="top">
                <img :src="defaultAvatar" class="header profile">
                <div class="info">
                    <div class="name">{{userInfo.employeeName}}</div>
                    <div class="number">{{userInfo.mobile}}</div>
                </div>
            </div>
            <div class="agency-name">{{userInfo.downstreamEnterpriseName}}</div>
            <ul @click="attachBtns" class="action-list">
                <li v-for="items in actionList" class="action-li">
                    <ul >
                        <li v-for="item in items" :data-action = item.action>{{item.name}}</li>
                    </ul>
                </li>
            </ul>
        </div>
        <ul class="app-list">
            <li data-action="portal" class="portal-btn active">
                <a :href="portalUrl"><img src="../assets/images/<EMAIL>" alt=""></a>
                <div class="header-nav-tooltip">
                    <div class="text-container">
                        <span>{{toolbarTips[0].name}}</span>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</template>

<script lang="ts">

    export default Vue.extend(<VueComponent>{
        data() {
            return {
                showCard: false,
                defaultAvatar: Fx.util.getAvatarPath(),
                actionList: [
                    [
                        {
                            name: $t('切换企业'),
                            action: 'switchEnt'
                        },{
                            name: $t('切换语言'),
                            action: 'switchI18n'
                        }
                    ],
                    [
                        {
                            name: $t('退出登录'),
                            action: 'logout'
                        }
                    ]
                ],
                toolbarTips: [
                    {
                        key: 'portal',
                        name: $t('订货通')
                    }
                ],
                portalUrl: window.location.href
            }
        },

        computed: {
            userInfo() {
                const portalEnv = $dht.downstream;
                return {
                    mobile: portalEnv.mobile,
                    employeeName: portalEnv.employeeName,
                    downstreamEnterpriseName: portalEnv.downstreamEnterpriseName
                }
            }
        },
        methods: {
            enter(evt: any) {
                this.showCard = true;
                evt && evt.stopPropagation();
            },
            leave(evt: any) {
                this.timer = setTimeout(() => {
                    this.showCard = false;
                }, 50);
                evt && evt.stopPropagation();
            },
            enterCard(evt: any) {
                clearTimeout(this.timer);
                evt && evt.stopPropagation();
            },
            leaveCard(evt: any) {
                this.showCard = false;
                evt && evt.stopPropagation();
            },
            attachBtns(evt: any) {
                let action = $(evt.target).data('action');
                this.attachThridModule(action);
            },
            attachThridModule(action: string) {
                $("#app-portal").append($("<div class='f-g-third-dialog'></div>"));
                Fx.async('app-apperlogin/app.js', (mod: any) => {
                    mod.render({
                        el: $('.f-g-third-dialog')[0],
                        data: {
                            action,     // Fx.util.getCurLocale() 这个字段拿到的应该是有租户信息的本地语言
                            currI18n: (<any>Fx).store && (<any>Fx).store.getLocal('userLauguage') || 'zh-CN',
                            redict: undefined
                        },
                        complete: (res: any) => {
                            if(action == 'switchEnt' || action == 'switchI18n') {
                                (<any>Fx).store && (<any>Fx).store.setLocal('userLauguage', res.currI18n || 'zh-CN');
                                window.location.reload(); // 刷新页面
                            }
                        },
                        error: (err: any) => {
                            console.log('操作失败:' + JSON.stringify(err));
                        }
                    }).then((res: any) => {
                        console.log(res);
                    });
                });
            }
        },

        mounted() {
        }
    });
</script>

<style scoped lang="less">
    .portal-navbar {
        position: absolute;
        top: 0;
        left: 0;
        width: 64px;
        height: 100%;
        background: #272B34;
    }
    .avatar {
        position: absolute;
        left: 10px;
        top: 24px;
        cursor: pointer;
    }

    // .avatar:before {
    //     position: absolute;
    //     content: '';
    //     left: 0px;
    //     top: -2px;
    //     width: 40px;
    //     height: 40px;
    //     border-radius: 40px;
    //     border: 3px solid #272B34;
    //     pointer-events: none;
    // }
    .avatar img {
        display: block;
        width: 42px;
        height: 42px;
        margin: 0 auto;
        background-size: contain;
        border-radius: 42px;
    }

    .avatar-card {
        position: absolute;
        top: 0px;
        left: 64px;
        width: 180px;
        background: #FFFFFF;
        box-shadow: 0px 1px 8px 0px rgba(33, 43, 54, 0.15), 0px 2px 4px 0px rgba(33, 43, 54, 0.05);
        border-radius: 3px;
        padding: 20px;
        z-index: 99;
        .top {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        .header {
            width: 48px;
            height: 48px;
            display: inline-block;
            margin-right: 8px;
            background-size: contain;
            border-radius: 48px;
        }
        .info {
            display: inline-block;
        }
        .number {
            color: #91959E;
        }
        .name {
            color: #181C25;
            font-size: 16px;
            overflow: hidden;
            text-overflow:ellipsis;
            white-space: nowrap;
            max-width: 125px;
        }
        .agency-name {
            color: #91959E;
            padding: 12px 0;
            border-bottom: 1px solid #DEE1E6;
        }
        .action-list {
            .action-li {
                padding: 9px 0;
                border-bottom: 1px solid #DEE1E6;
                color: #181C25;
                cursor: pointer;
            }
            :last-child {
                border: none;
            }
            li {
                line-height: 22px;
            }
        }
    }

    .app-list {
        position: absolute;
        top: 90px;
        li {
            width: 64px;
            height: 56px;
            line-height: 56px;
            text-align: center;
            position: relative;
            a {
                text-decoration: none;
                display: inline-block;
                img {
                    width: 24px;
                    height: 24px;
                    vertical-align: middle;
                }
            }
            .header-nav-tooltip {
                position: absolute;
                top: 10px;
                left: 70px;
                z-index: 999;
                display: none;
                .text-container {
                    width: 62px;
                    height: 35px;
                    line-height: 35px;
                    word-break: break-all;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    border-radius: 4px;
                    background-color: rgba(68, 75, 102, 0.9);
                    color: #FFF;
                    font-size: 12px;
                    user-select: text;
                    -webkit-user-select: text;
                }
                &:before {
                    width: 0;
                    height: 0;
                    content: close-quote;
                    position: absolute;
                    top: 10px;
                    left: -12px;
                    border-style: solid;
                    border-width: 6px;
                    border-color: rgba(68, 75, 102, 0.9) transparent transparent;
                    transform: rotateZ(90deg);
                }
            }
            &:hover {
                .header-nav-tooltip {
                    display: block;
                }
                background-color: #181C25;
            }
        }
        .active {
            background-color: #181C25;
        }
    }
</style>
