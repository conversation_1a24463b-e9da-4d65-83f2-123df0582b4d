<template>
  <div v-if="visible && adImages.length > 0" class="ad-popup">
    <div class="ad-popup-content">
      <fx-carousel ref="rightCarousel" indicator-position="outside" arrow="never" :autoplay="true">
        <fx-carousel-item v-for="(item, index) in adImages" :key="index">
          <fx-image
            @click="bannerJump(item)"
            class="carousel-img"
            :src="imageUrl(item.ad_pictures[0].path)"
            :style="{ 'margin-left': item.name === 'web' ? '48px' : '0' }"
          ></fx-image>
        </fx-carousel-item>
      </fx-carousel>
    </div>
    <div class="operate-close" @click="handleClose">
      <span class="fx-icon-close operate-close-icon"></span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdPopup',
  data() {
    return {
      visible: false,
      adImages: [],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.apiGetPromotionBanner().then((res) => {
        this.adImages = res.Value.dataList;
        this.showPopup();
      });
    });
  },
  methods: {
    imageUrl(path) {
      return CRM.util.getImgPath(path);
    },
    apiGetPromotionBanner() {
      return Fx.util
        .FHHApi({
          url: '/EM6HNCRM/API/v1/object/new_advertisement/service/list',
          data: {
            offset: 0,
            limit: 10,
            type: 1,
            terminal: '1', // 1表示web端，2表示移动端
          },
        })
        .then((res) => {
          return res;
        });
    },
    // 跳转处理
    bannerJump(obj) {
      const type = obj.jump_type;
      const apiName = obj.jump_object_api_name;
      const applicableTerminal = obj.applicable_terminal


      if (type === '1') {
        // 跳转的是内部数据
        // eslint-disable-next-line default-case
        switch (apiName) {
          case 'SPUObj':
            this.openProductDetail({
              object_describe_api_name: obj.jump_object_api_name,
              spu_id: obj.jump_object_data_id,
            });
            break;
          case 'ProductObj':
            this.openProductDetail({
              object_describe_api_name: obj.jump_object_api_name,
              _id: obj.jump_object_data_id,
            });
            break;
          case 'PricePolicyObj':
            this.showPromotionDetail(obj.jump_object_data_id);
            break;
        }
      } else if (type === '2') {
        // 跳转的是外部数据
        let url = obj.external_address;
        const hasHttp = url.substr(0, 7).toLowerCase() === 'http://';
        const hasHttps = url.substr(0, 8).toLowerCase() === 'https://';
        if (!hasHttp && !hasHttps) {
          url = `http://${url}`;
        }
        obj.external_address && window.open(url);
      } else if(type === "3" && applicableTerminal[0] === '1'){
        let host = FS.BASE_PATH;
        if (!host) {
          host = 'https://www.fxiaoke.com';
        } else {
          if (host.indexOf('ceshi112') > -1) {
              host = 'https://www.ceshi112.com';
          } else if (host.indexOf('www.fxiaoke') > -1) {
              host = 'https://www.fxiaoke.com';
          }
        }
        this.handleClose();
        window.location.hash = `#/portal/custompage/=/${obj.custom_page_id}`;
      }
    },

    /**
     * 打开商品详情页
     * @param product
     */
    openProductDetail(product) {
      // 替换为订货通接口
      const isSpu = product.object_describe_api_name === 'SPUObj';
      window.$dht.openProductDetail({
        isSpuMode: isSpu,
        product: {
          spu_id: isSpu ? product.spu_id : '',
          _id: !isSpu ? product._id : '',
        },
      });
    },

    /**
     * 打开价格政策详情页
     */
    showPromotionDetail(promotionId) {
      Fx.async('crm-components/showdetail/showdetail', (Detail) => {
        // 替换为订货通接口
        if (!this.detailInstance) {
          this.detailInstance = new Detail({
            apiName: 'PricePolicyObj',
            apiId: promotionId,
            idList: this.allPromotionId,
            isCanEdit: true,
          });
        }
        this.detailInstance.show(promotionId, 'PricePolicyObj', {
          idList: this.allPromotionId,
          zIndex: 1000, // 模块层级
          showMask: true, // 是否显示遮罩
          top: 0,
        });
      });
    },
    showPopup(bool = true) {
      this.visible = bool;
    },
    closePopup() {
      this.visible = false;
    },
    handleClose() {
      this.visible = false;
    },
  },
};
</script>
<style lang="less">
.ad-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}
.carousel-img {
  height: 450px;
}

.ad-popup-content {
  padding: 20px;
  border-radius: 8px;
  position: relative;
  float: right;
  width: 1000px;
  height: 450px;
  box-sizing: border-box;
  border-top-right-radius: 16px;
  border-bottom-right-radius: 16px;
  text-align: center;
  .el-carousel__container {
    height: 450px;
  }
}

.operate-close {
  position: absolute;
  right: 48.5%;
  bottom: 100px;
  z-index: 10;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  cursor: pointer;
  border: 1px solid #fff;
  &-icon {
    font-size: 13px;
    color: #FFFFFF;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  &:hover {
    opacity: 0.8;
  }
  .fx-icon-close:before{
    color: #FFFFFF;
  }
}
</style>
