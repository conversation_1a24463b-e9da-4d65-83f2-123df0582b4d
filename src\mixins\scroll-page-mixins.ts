/**
 * 滚动分页配合底部分页的mixins，主要用于在商品卡片列表,
 * 每滚动3页的时候在底部显示分页栏
 */
export default {
  data() {
    return {
      // 总条数
      total: 0,
      // 底部分页栏的当前页
      currentPage: 1,
      // 滚动每屏的条数，实际上就是请求的pageSize
      scrollPageSize: 60,
      // 滚动的当前页数
      scrollCurrentPage: 1,
      // 滚动的最大页数，例如这里滚动加载3次之后停止滚动加载，然后显示底部分页栏
      scrollMaxPage: 1,
    };
  },

  computed: {
    onlyOnePage() {
      const me = this as any;
      return me.total <= (me.scrollMaxPage * me.scrollPageSize);
    },
    /**
     * 底部导航每页条数
     * @return {number}
     */
    pageSize(): number {
      const me = this as any;
      return me.scrollPageSize * me.scrollMaxPage;
    },
    /**
     * 是否已全部加载完成
     * @return {boolean}
     */
    isAllLoaded(): boolean {
      const me = this as any;
      // 计算当前页
      const page = (me.currentPage - 1) * me.scrollMaxPage + me.scrollCurrentPage;
      // 是否已经所有加载完成
      return page * me.scrollPageSize >= me.total;
    },
    /**
     * 禁止滚动加载，在所有数据已经加载完成或者当前滚动页>=最大滚动页时返回true，否则返回false
     * @return {boolean}
     */
    infiniteScrollDisabled(): boolean {
      const me = this as any;
      // 在所有数据已经加载完成或者当前滚动页>=最大滚动页时返回true，否则返回false
      return me.isAllLoaded || me.scrollCurrentPage >= me.scrollMaxPage;
    },
    /**
     * 滚动的一页完成
     */
    isScrollFinished(): boolean {
      const me = this as any;
      return me.scrollCurrentPage >= me.scrollMaxPage;
    },
  },

  methods: {
    /**
     * 底部滚动加载回调
     * @return {void}
     */
    infiniteScroll(done: Function): void {
      const me = this as any;
      if (me.infiniteScrollDisabled) {
        done();
        return;
      }
      // 滚动页+1
      me.scrollCurrentPage += 1;
      // 计算实际请求页
      // const page = (me.currentPage - 1) * me.scrollMaxPage + me.scrollCurrentPage;
      // 传入请求页和每页请求条数
      this.getData().then(done).catch(() => {
        me.scrollCurrentPage -= 1;
        done();
      });
    },
    /**
     * 底部分页栏切换分页
     * @param currentPage {number} 当前分页
     * @return {void}
     */
    currentPageChange(currentPage: number): void {
      const me = this as any;
      // 重置滚动分页为1
      me.scrollCurrentPage = 1;
      // 计算实际请求页
      // const page = (currentPage - 1) * me.scrollMaxPage + me.scrollCurrentPage;
      // 传入请求页和每页请求条数
      me.$refs.fkScroll.scrollToTop();
      this.getData();
    },
    /**
     * 发送请求，供子类实现
     * @param page {number} 请求页
     * @param pageSize {number} 每页条数
     * @return {any}
     */
    getData(): any {
      // 处理请求逻辑
    },
  },
};
