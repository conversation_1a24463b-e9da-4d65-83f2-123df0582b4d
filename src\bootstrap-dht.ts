import * as Global from './context/global';
import * as LifeHook from './context/lifehook';

export function initDHT() {
  Global.init();

  const $alert = Vue.prototype.$alert;
  // 是否是厂商门户
  $dht.isPortal = true;
  $dht.version = '1.0.1';

  $dht.on('err:nocart', (err: any = {}) => {
    if (err.Code === '320001400') {
      $alert($t('没有购物车列表查看权限，请联系上游厂商开通'));
    } else if (err.Code === '320002404') {
      $alert($t('还未开启购物车插件，请联系上游厂商开通'));
    } else {
      $alert($t(`购物车插件配置错误，Code = ${err.Code}`));
    }
  });

  $dht.on('err:business', (err: any = {}, config: any = {}) => {
    config.showError = config.showError === undefined ? true : config.showError;
    if (config.showError) {
      const errorMsg = err.Message || `业务请求错误，Code = ${err.Code}`;
      $alert($t(errorMsg));
      return;
    }
    console.error(err);
  });

  window.dht = {};
  window.dht.api = function c(module: string) {
    console.log('***********************Dht module ', module);
    // 错误的模块名：=-SalesOrderObj-record_klPnz__c
    // 正确的模块名：SalesOrderObj-record_klPnz__c
    const array = module.split('-');
    return $dht.getModule({
      component: 'list',
      objectApiName: /^=-/.test(module) ? array[1] : array[0],
      objectRecordType: /^=-/.test(module) ? array[2] : array[1],
    });
  };

  window.dht.lifeHookEnter = function enter($router: any, $route: any, next: any) {
    console.log('==========================Dht lifehook enter');
    LifeHook.enter();
    Promise.all([
      // Global.getCrmAllConfig(),
      $dht.init(),
    ]).then((res: any) => {
      console.log('Dht init success: %o', res);

      Global.initShopCartNumber();
      next();
    }).catch((err: any) => {
      console.error('Dht init failure: %o', err);
      next();
    });
  };

  window.dht.lifeHookLeave = function leave() {
    console.log('==========================Dht lifehook leave');
    LifeHook.leave();
  };
}
