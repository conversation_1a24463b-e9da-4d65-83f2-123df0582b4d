@skeleton-text-background-alpha: .065;
@skeleton-text-background: #F2F3F5;
@skeleton-text-background-animated-alpha:   .135;
@skeleton-text-background-animated: rgba(0, 0, 0, @skeleton-text-background-alpha);

.dht-skeleton {
  position: relative;
  background: @skeleton-text-background;
  display: block;
  width: 100%;
  height: inherit;
  margin-top: 4px;
  margin-bottom: 4px;
  line-height: 10px;
  user-select: none;
  pointer-events: none;
  &.dht-skeleton-text-animated {
    position: relative;

    background: linear-gradient(to right, @skeleton-text-background 8%, @skeleton-text-background-animated 18%, @skeleton-text-background 33%);
    //background-size: 800px 104px;
    background-size: 1920px 104px;
    animation-duration: 1s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-name: shimmer;
    animation-timing-function: linear;
  }
}

@keyframes shimmer {
  0% {
    //background-position: -468px 0
    background-position: -1000px 0
  }

  100% {
    background-position: 1000px 0
  }
}
