// 不外露展示的字段
export const HIDE_FIELDS = [
  'extend_obj_data_id',
  'lock_rule', // 锁定规则
  'object_describe_api_name',
  'package',
  'tenant_id',
  '_id',
  'commodity_label',
  'out_tenant_id',
];

// 要展示的系统定义字段
export const SHOW_SYSTEM = [
  'name',
  'created_by',
  'create_time',
  'last_modified_by',
  'last_modified_time',
  'out_owner',
  'owner',
  'life_status',
];

// 要隐藏的字段
export const HIDE_PACKAGE = [
  'relevant_team',
  'lock_rule',
  'life_status_before_invalid',
  'lock_user',
  'extend_obj_data_id',
  'mc_exchange_rate_version',
  'mc_functional_currency',
];

// 不宜在卡片上展示的字段类型
export const HIDE_FIELD_TYPES = [
  'html_rich_text', // 富文本
  'image', // 图片
  'file_attachment', // 附件
  'big_file_attachment', // 大附件
];

// 保存字段的key常量
export const SAVE_KEYS = {
  VIEW: 'view_mode',
  PICTURE: 'picture_apiname',
  NAME: 'name_apiname',
  PRICE: 'price_apiname',
  TAG: 'tag_apiname',
  IS_TAG_SHOW: 'is_tag_show',
  SHOW_FIELDS: 'show_fields',
  FILTER_FIELDS: 'filter_fields',
  SORT_FIELDS: 'sort_fields',
  DEFAULT_SORT: 'default_sort',
};
