// import debug from 'debug';
import { LifeHooks } from '../../base/hooks';

// const log = debug('@nsail:debugger');

export const ext = _.extend(LifeHooks(), {
  options: {
    cellEdit: false,
  },
  /**
   * 获取给表格使用的配置选项
   */
  getOptions() {
    const options = this.constructor.__super__.getOptions.apply(this);

    // 从销售订单的确认收货/查看物流操作过来时，默认带有订单编号
    const orderName = CRM._cache.dht_ordernamefordelivery;
    if (orderName) {
      options.filterDatas = {
        filters: [{
          Comparison: 1,
          FieldName: 'sales_order_id',
          FieldType: 1,
          FilterValue: orderName,
        }],
      };
      delete CRM._cache.dht_ordernamefordelivery;
    }

    return options;
  },
});
