<template>
  <div class="dhtbiz-shop-list">
    <shopList :category-node="category" ></shopList>
  </div>
</template>

<script>
export default {
  name: 'dht_web_product_list_shop_list',
  components: {
    shopList: () => Fx.getBizComponent('dhtbiz', 'mallShopList').then(res => res())    
  },
  props: {
    dhtPageData: {
      type: Object,
      default: () => ({})
    },
    dhtContainerApi: {
      type: Object,
      default: () => ({})
    },
  },
  computed: {
    category() {
      return this.dhtPageData?.category;
    }
  },
  methods: {
    
  },
  created() {
    // this.initTable();
  },
  beforeDestroy() {
  },
};
</script>

<style lang="less" scoped>
.dhtbiz-shop-list {
  width: 100%;
}
</style> 