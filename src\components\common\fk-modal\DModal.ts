// eslint-disable-next-line max-classes-per-file
import modal from './modal.vue';

const Modal = Vue.extend(modal);

class ModalRef {
  modalInstance: any = null;

  constructor(options: any, store?: any, router?: any) {
    this.modalInstance = new Modal({
      el: document.createElement('div'),
      store,
      router,
      propsData: {
        config: options,
      },
    });
    const modalContainer = this.getModalContainer();
    modalContainer.appendChild(this.modalInstance!.$el as HTMLElement);
    this.modalInstance.$on('close', () => {
      this.modalInstance.show(false);
      // const timer = setTimeout(() => {
      //   clearTimeout(timer);
      //   this.modalInstance.$destroy();
      //   modalContainer.removeChild(this.modalInstance.$el);
      // }, 300);
    });
  }

  getModalContainer() {
    let container = document.getElementById('DModalContainer');
    if (!container) {
      container = document.createElement('div');
      container.id = 'DModalContainer';
      document.body.appendChild(container);
    }
    return container;
  }

  close() {
    this.modalInstance.closeModal();
  }
}

export class DModal {
  router = null;

  store = null;

  constructor(options: {router?: any, store?: any}) {
    const { router, store } = options;
    this.router = router;
    this.store = store;
  }

  create(options: any) {
    const modalRef = new ModalRef(options, this.store, this.router);
    return modalRef;
  }
}
