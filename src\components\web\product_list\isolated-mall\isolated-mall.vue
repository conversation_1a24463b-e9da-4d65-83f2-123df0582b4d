<script lang="ts">
import 'reflect-metadata';
import { Vue, Prop, Component } from 'vue-property-decorator';
import ShopList from './shop-list/shop-list.vue';
import CategoryTree from "./category-tree/category-tree.vue";


@Component({
  name: 'IsolatedMall',
  components: {
    ShopList,
    CategoryTree
  }
})
export default class IsolatedMall extends Vue {
  categoryNode: any = null;

  onCategoryChange(categoryNode: any, level: number) {
    this.categoryNode = categoryNode;
  }
}
</script>

<template src="./isolated-mall.html"></template>
<style src="./isolated-mall.less" scoped lang="less"></style>
