import { getVcrmComponents, getUserConfigByKey, USER_CONFIG_KEYS } from './common';

// 缓存个人级config数据
export function setUserConfigCache(configs: any[]) {
  const recordType = getUserConfigByKey(configs, USER_CONFIG_KEYS.RECORD_TYPE);
  const filterCondition = getUserConfigByKey(configs, USER_CONFIG_KEYS.AVAILABLE_RANGE);
  // 将个人级设置过的业务类型取出来缓存
  if (recordType && recordType.success) {
    const value = recordType.value;
    $dht.setMainRecordType({
      api_name: value,
    }, true);
  }

  const rangeFilter = $dht.config.newAvailableRangeFilter;
  const isRangeFilterEnable = rangeFilter.isEnable && rangeFilter.filter_field === 'record_type';
  // 开启了【基于订单类型、项目过滤可售范围】，且过滤条件是【业务类型】
  // 有个人级缓存，将个人级设置过的可售范围取出来缓存
  if (isRangeFilterEnable && filterCondition && filterCondition.success) {
    const condition = JSON.parse(filterCondition.value || '{}');
    $dht.setMainFilterData({
      apiname: condition.filter_field,
      value: condition.filter_value,
    }, true);
  }
}

// 获取销售订单的描述
export function getSalesOrderDes() {
  return $dht.getService('meta').getDescribeAndLayout({
    object_api_name: 'SalesOrderObj',
    include_layout: false,
    include_detail_describe: false,
  }).then((res: any) => {
    const fields = res.objectDescribe.fields;
    return fields;
  });
}

/**
 * 业务类型和单选的可售范围过滤弹窗选择
 * @param field 字段的描述
 * @param options 字段的可选列表
 * @param source 操作来源：业务类型选择 | 可售范围过滤选择
 */
export function selectOneModal(field: any, options: any[], source?: any) {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    const apiname = field.api_name;
    const filterData = $dht.getMainFilterData();
    const recordType = $dht.getMainRecordType();
    let selectedItem = null;
    if (source === 'recordtype') {
      selectedItem = options.find((item: any) => {
        return recordType === item.api_name;
      });
    } else {
      selectedItem = options.find((item: any) => {
        return filterData.apiname === apiname && filterData.value === (item.api_name || item.value);
      });
    }
    if (selectedItem) { // 已选择过，直接返回
      resolve('');
    } else if (options.length === 1) { // 一个默认选择
      $dht.setMainFilterData({
        apiname,
        value: options[0].api_name || options[0].value,
      });
      resolve('');
    } else if (options.length > 1) { // 多个弹窗选择
      const [{ modalService }, RecordType] = await getVcrmComponents(['modalService'], ['recordType']);
      modalService.create({
        title: $t('选择') + field.label,
        width: '500px',
        forbidClose: true,
        component: RecordType,
        componentParams: {
          list: options,
        },
        onOk: (componentRef: any) => {
          const value = componentRef.save();
          if (value) {
            if (source === 'recordtype') {
              $dht.setMainRecordType({ api_name: value });
            } else {
              $dht.setMainFilterData({ apiname, value });
            }
            resolve('');
          }
          return !!value;
        },
      });
    } else {
      Vue.prototype.$alert($t('dht.select_record_type.no_data_tip'), $t('dht.select_record_type.data_error'), {
        confirmButtonText: $t('确定'),
        type: 'warning',
      });
      reject();
    }
  });
}

/**
 * 查找关联字段的弹窗选择
 * @param field 字段的描述
 */
export function selectLookup(field: any) {
  return new Promise((resolve, reject) => {
    const apiname = field.api_name;
    const filterData = $dht.getMainFilterData();
    const isSelected = filterData.apiname === apiname;
    if (isSelected) { // 已选择过，直接返回
      // 根据id，获取可显示的label
      const objectApiName = field.target_api_name;
      const id = filterData.value;
      $dht.getService('paas').getDataItem(objectApiName, id).then((res: any) => {
        $dht.config.customer[`${apiname}__r`] = res?.data?.display_name;
        resolve('');
      });
    } else {
      Fx.async([
        'crm-modules/components/pickselfobject/pickselfobject',
        'crm-assets/style/all.css',
      ], (Picker: any) => {
        const picker = new Picker();
        picker.render({
          apiname: field.target_api_name,
          target_related_list_name: field.target_related_list_name,
          relatedname: field.target_related_list_name,
          isMultiple: false,
          tableOptions: {
            searchTerm: null, // 选择场景
          },
        });
        picker.on('select', (obj: any) => {
          $dht.setMainFilterData({
            apiname: field.api_name,
            value: obj._id,
          });
          $dht.config.customer[`${apiname}__r`] = obj.display_name;
          resolve('');
        });
      });
    }
  });
}

/**
 * 选择可售范围
 * @param configs 订货芯config
 * @param recordTypeList 业务类型列表
 * @returns
 */
export async function selectAvailableRange(configs: any, recordTypeList: any) {
  const filterField = $dht.config.newAvailableRangeFilter.filter_field;
  return getSalesOrderDes().then((fields: any) => {
    const field = (fields && fields[filterField]);
    if (!field) {
      const tips = $t('可售范围设置的{{apiname}}字段已被删除请联系管理员', { apiname: filterField });
      CRM.util.alert(tips);
      return configs;
    }
    // 将从描述接口获取的该字段详情信息缓存下来，用于右上角【过滤可售范围】按钮使用
    $dht.config.newAvailableRangeFilter.field_info = field;
    if (field.type === 'record_type') { // 业务类型
      return selectOneModal(field, recordTypeList);
    }
    if (field.type === 'select_one') { // 单选
      return selectOneModal(field, field.options).then(() => {
        return selectOneModal({ api_name: 'record_type', label: $t('业务类型') }, recordTypeList, 'recordtype');
      });
    }
    if (field.type === 'object_reference') { // 查找关联
      return selectLookup(field).then(() => {
        return selectOneModal({ api_name: 'record_type', label: $t('业务类型') }, recordTypeList, 'recordtype');
      });
    }
    return configs;
  });
}
