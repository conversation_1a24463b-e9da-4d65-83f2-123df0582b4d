/**
 * copy 帝迈二开代码
 * http://git.firstshare.cn/fe/crm2k/-/blob/bugfix/src/action/733767_sandbox/add/salesorderobj/view/view.js
 */
// @ts-nocheck
export function dimaiMD(proto) {
  return {
    // 过滤掉单选eq  选择IN多选 以及 已选择的过滤条件
    parseParamForSaleContract(param, table) {
      proto.parseParamForSaleContract.apply(this, arguments);
      if (table.apiname == "SalesOrderProductObj") {
        let search_query_info = JSON.parse(param.search_query_info),
          _newArr = [];
        search_query_info.filters.forEach(i => {
          if ((i.field_values.length > 0 && i.operator == 'IN') || i.operator == 'NIN') {
            _newArr.push(i);
          }
        });
        search_query_info.filters = _newArr;
        param.search_query_info = JSON.stringify(search_query_info);
      }
    },
    // 修改选择验证
    beforePickObject() {
      if (arguments[0].api_name == 'sale_contract_line_id') {
        if (!this.getData('account_id')) {
          CRM.util.alert('请先选择客户');
          return false
        }
        if (!this.getData('field_a7JfY__c') || this.getData('field_a7JfY__c').length == 0) {
          CRM.util.alert('请先选择销售合同多选');
          return false
        }
      }
    },
    // 添加过滤条件
    pickObject() {
      if (arguments[0] == 'sale_contract_line_id') {
        let _value = this.getData('field_a7JfY__c');
        arguments[2].filters = [{ "field_name": "sale_contract_id", "field_values": _value, "operator": "IN" }];

      }
      proto.pickObject.apply(this, arguments);
    },
    // 自定义【从销售合同产品添加】
    batchAddDataHandleNew(table) {
      proto.batchAddDataHandle.apply(this, arguments);
    },
    parseBtnList() {
      let _btn = proto.parseBtnList.apply(this, arguments),
        _btnNew = [];
      _btn.forEach(i => {
        if (i.fieldName == 'sale_contract_line_id') {
          _btnNew.push({
            action: "batchAddDataHandleNew",
            fieldName: "sale_contract_line_id",
            name: "从销售合同产品添加"
          })
        } else {
          _btnNew.push(i);
        }
      });
      return _btnNew;
    },
  };
}
