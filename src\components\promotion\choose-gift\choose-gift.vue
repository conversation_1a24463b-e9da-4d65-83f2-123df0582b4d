<template>
  <div class="dht-choose-gift gift-table">
    <!-- 表格主体 -->
    <div class="gift-table-hd">
      <div class="gift-table-tr">
        <div class="gift-table-td">{{ $t('i18n.promotion.gift.name') }}</div>
        <div class="gift-table-td">{{ $t('i18n.promotion.gift.quantity') }}</div>
      </div>
    </div>
    <div class="gift-table-bd">
      <template v-for="(item, idx) in items">
        <div class="gift-table-tr" :key="idx">
          <div class="gift-table-td">
            <div class="gift-item">
              <fx-checkbox v-if="!readonly"
                class="gift-check"
                v-model="item.checked"
                :disabled="!item.checked && optionalCount > 1 && checkedCount >= optionalCount"
                @change="toggleCheck(item)"
              />
              <div class="gift-img" v-if="isShowPictureMode">
                <fx-image :src="getImageAddr(item)" :fit="'contain'" />
              </div>
              <div class="gift-info">{{ item.gift_name }}</div>
            </div>
          </div>
          <div v-if="!item.skus" class="gift-table-td">x {{ getAmount(item) }}</div>
          <div v-else class="gift-table-td">
            {{ $t('i18n.promotion.gift.optional') }}{{ item.gift_num }}{{ $t('i18n.promotion.gift.unit') }}{{ $t('i18n.promotion.gift.selected') }}{{ item.selectedSkuNum }}{{ $t('i18n.promotion.gift.unit') }}
            <span v-if="item.selectedSkuNum > item.giftNum" class="gift-exceed-tip">{{ $t('i18n.promotion.gift.insufficient') }}</span>
          </div>
        </div>
        <!-- 如果促销商品是SPU，需要列出SKU进行选择 -->
        <template v-if="item.skus && item.skus.length > 0">
          <div v-for="(sku) in item.skus" :key="`sku-${sku.id}`"
            class="gift-table-tr gift-table-tr-sku"
          >
            <div class="gift-table-td">
              <div class="gift-item gift-item-sku">
                <div class="gift-info">{{ sku.name }}</div>
              </div>
            </div>
            <div class="gift-table-td">
              <fx-input type="number"
                :min="0"
                :max="10"
                v-model="sku.quantity"
                :placeholder="$t('i18n.promotion.gift.input')"
                size="small"
                @input="handleSkuQuantityChange(item)"
              />
            </div>
          </div>
        </template>
      </template>
    </div>
    <!-- 底部提示 -->
    <div class="gift-tip">{{ captionText }}</div>
  </div>
</template>

<script src="./_choose-gift.ts" lang="ts"></script>
<style src="./_choose-gift.less" lang="less"></style>
