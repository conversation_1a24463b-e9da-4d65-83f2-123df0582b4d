<script lang="ts">
import 'reflect-metadata';
import { Component, Vue, Emit, Prop } from 'vue-property-decorator';
import Loading from '../loading/loading.vue';
import ProductCard from '../product-card/product-card.vue';
import ZdProductCard from '../guest-open/zd/double-input-card/double-input-card.vue';
import { Product } from '../types';
import {
  Options,
  ActionType,
  ProductConfig,
  ProductFieldsMap
} from '../product-card/options';
import { PaginationOptions } from './options';
import { Position } from '../services/CartAnimationService';
import { CartService } from '../services/CartService';
import { MultiSpecService } from '../services/MultiSpecService';
import { BomService } from '../services/BomService';
import AttributeService from '../services/AttrService';
import { collectionService } from '../services/CollectionService';
import { convertSpuList, getProductMinNum, isSpuObj } from '../utils/product-util';
import { openProductDetail } from '../product-detail/product-detail-modal';
import {isZd} from '@/widgets/utils/check-ea';

const crmUtil = CRM.util;
const FHHApi = crmUtil.FHHApi;
const isSpuMode = $dht.config.sail.isSpuMode;
// 模式切换是否已提醒，标记位防止多次提醒
let isModeChangeAlreadyPrompted = false;

@Component({
  name: 'ProductList',
  components: {
    Loading,
    ProductCard,
    ZdProductCard
  }
})
export default class ProductList extends Vue {
  @Prop({default: () => []}) readonly list!: Product[];
  @Prop({default: () => ({})}) readonly options!: Options;
  @Prop({default: null}) readonly pageOptions!: PaginationOptions | null;
  @Prop({default: false}) readonly isLoading!: boolean;
  @Prop({default: 'dht-product-cardlist'}) readonly prefixClass!: string;

  // 是否已经配置商品卡片
  isConfigured = false;
  // 是否在加载配置
  isConfigLoading = false;
  productConfig: ProductConfig = { card_main_info: {} } as ProductConfig;
  productFields: ProductFieldsMap = {} as ProductFieldsMap;

  get precision() {
    const value = $dht.config.meta.SalesOrderProductObj.quantityPrecision;
    return typeof value === 'number' ? value : null;
  }

  get iList(): Product[] {
    this.list.forEach(item => {
      // 补充 收藏字段 is_in_collection
      collectionService.updateProductCollectionStatus(item);
    });
  
    if (this.list && this.list.length) {
      if (isSpuObj(this.list[0])) {
        // 商品对象
        return convertSpuList(this.list);
      } else {
        // 产品对象
        this.list.forEach(item => {
          Vue.set(item, 'quantity', getProductMinNum(item) || 1);
        });
      }
    }
    // 产品对象
    return this.list;
  }

  // 确定使用哪个卡片组件
  get cardComponent() {
    // 如果存在自定义的 pwcCard 组件，则优先使用
    if (this.options?.pwcCard) {
      return this.options.pwcCard;
    } else if (isZd()) {
      return 'ZdProductCard';
    } else {
      return 'ProductCard';
    }
  }

  // 是否在加载数据和配置
  get isDataLoading() {
    return this.isLoading || this.isConfigLoading;
  }

  created() {
    // 如果存在自定义的 pwcCard 组件，则注册它
    // if (this.options?.pwcCard) {
    //   if (this.$options?.components) {
    //     this.$options.components['customPwcCard'] = this.options.pwcCard;
    //   }
    // }

    collectionService.registerSubscriber(this as any); // 注册为收藏事件监听者
    collectionService.getCollectionIds({pageNumber: 1, pageSize: 500}).then((ids: any) => {});

    this.isConfigLoading = true;
    this.getConfig();
  }

  mounted() {
    this.$nextTick(() => {
     FS.util.addWatermark('.dht-product-cardlist');
    });
  }

  beforeDestroy() {
    collectionService.deregisterSubscriber(this as any);
  }

  /**
   * 获取自定义页面配置
   */
  getUserCustomerLayout() {
    const data = {
      appType: 6,
      layoutApiName: "FSAID_11490c84-shopping"
    };

    return new Promise((resolve, reject) => {
      FHHApi({
        url: '/EM6HWebPage/UserHomePage/getUserCustomerLayout',
        data,
        success(res: any) {
          if (res.Result.StatusCode === 0) {
            resolve(res.Value)
          } else {
            reject(res.Value);
          }
        },
        fail(err: any) {
          reject(err)
        }
      });
    });
  }

  /**
   * 获取对象描述
   */
  getFields() {
    const data = {
      describe_apiname: isSpuMode ? 'SPUObj' : 'ProductObj',
      include_layout: false,
      include_related_list: false,
      get_label_direct: true,
      includeDescribeExtra: false // 是否包含扩展字段
    };

    return new Promise((resolve, reject) => {
      FHHApi({
        url: '/EM6HNCRM/API/v1/object/describe/service/findDescribeByApiName',
        data,
        success(res: any) {
          if (res.Result.StatusCode === 0) {
            resolve(res.Value);
          } else {
            reject(res.Value);
          }
        },
        fail(err: any) {
          reject(err)
        }
      });
    });
  }

  getConfig() {
    Promise.all([this.getUserCustomerLayout(), this.getFields()])
      .then(([layouts, fields]: any[]) => {
        const components = layouts.homePageLayout.customerLayout.components;
        const allProductComponent = Object.values<any>(components).find(
          (comp) => comp.type === 'dht_shopmall_allproduct'
        );
        const { props = {} } = allProductComponent;
        const { is_spu_mode, is_card_init } = props;

        this.isConfigLoading = false;
        // 已配置卡片，且商品模式已经改变
        if (!is_card_init && is_spu_mode !== isSpuMode) {
          this.isConfigured = false;

          if (!isModeChangeAlreadyPrompted) {
            isModeChangeAlreadyPrompted = true;
            crmUtil.alert($t('dht.product_card.mode_changed_tip'));
          }
          return;
        }

        this.productConfig = Object.freeze(props);
        this.productFields = Object.freeze(fields.objectDescribe.fields);
        // 非初始化状态
        this.isConfigured = !is_card_init;
      });
  }

  onAction(data: {type: ActionType, product: Product, position?: Position}) {
    const { type,  product, position} = data;
    switch (type) {
      case 'CART':
        CartService.getInstance().addProductToCart(product, {animationPosition: position}).then(() => {
          (product as any).quantity = 1;
        });
        break;
      case 'DETAIL':
        this.openProductDetail(product);
        break;
      case 'SPEC':
        let comp = new MultiSpecService();
        comp.initMultiSpec({
            spuId: product._id
        })
        break;
      case 'COLLECTION':
        this.collectionAction(product);
        break;
      case 'BOM':
        let bomComp = new BomService();
        bomComp.initBom(product);
        break;
      case 'ATTR':
        const attrService = new AttributeService();
        attrService.initAttrComp(product);
        break;
      default:
        console.error('unhandle action type!');
        break;
    }
  }

  @Emit('onPageChange')
  onPageChange(currentPage: number) {}

  @Emit('collectChange')
  collectChange(data: any) {}

  collectionAction(product: any) {
    const me = this as any;
    let params = {
      apiName: product.object_describe_api_name,
      objId: product._id,
    }
    let isCollect = product.is_in_collection

    crmUtil.showLoading_tip((!isCollect ? $t('收藏中') : $t('取消收藏中')) + '...');
    let Action = !isCollect ? collectionService.addToCollectionList(params) : collectionService.removeFromCollection(params);
    Action.then(() => {
      me.$message({
        message: !isCollect ? $t('收藏成功') : $t('已取消收藏'),
        type: 'success',
        center: true,
      });
      crmUtil.hideLoading_tip();
    });
  }

  // 收藏状态改变时，调用这里的方法
  onCollectionChange(action: 'add' | 'remove', product: any, collectionIds: any) {
    console.log('===================== liseten change');
    this.collectChange({id: product.objId, isCollect: action === 'add', collectionIds});
  }

  /**
   * spu：_id是spuId, product: _id是产品id
   * @param product
   */
  private openProductDetail(product: any) {
    const isSpu = isSpuObj(product);
    openProductDetail({
      isSpuMode: isSpu,
      product: {
        spu_id: isSpu ? product._id : '',
        _id: isSpu ? (product.product_id || '') : product._id ,
        is_package: product.is_package,
      },
      allProducts: this.iList.map(item => {
        return {
          spu_id: isSpu ? item._id : '',
          _id: isSpu ? (item.product_id || '') : item._id ,
          is_package: item.is_package,
        };
      }),
    });
  }
}
</script>

<template src="./product-list.html"></template>
<style src="./product-list.less" lang="less"></style>
