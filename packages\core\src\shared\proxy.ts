import { deepMerge, isSimpleType } from '@beecraft/shared';

export type AnyObject = {[key: string]: any}
export type AnyArray = Array<any>
export type AnySet = Set<any>
export type AnyMap = Map<any, any>
export type Objectish = AnyObject | AnyArray | AnyMap | AnySet;

/**
 * var obj = {
 *     name: 'name',
 * }
 * var obj2 = {
 *     age: 'age',
 * }
 * var obj3 = {
 *     adress: {
 *         city: 'city',
 *     }
 * }

 * var proxy = createProxyProxy(obj, obj2, obj3);

 * console.log(proxy.name);
 * console.log(proxy.age);
 * console.log(proxy.adress.city);

 * let arr1 = [1, 2, 3];
 * let arr2 = [4, 5, 6, 7];
 * let arr3 = [8, 9, 10, 11, {
 *     name: 'name',
 * }];

 * var proxy2 = createProxyProxy(arr1, arr2, arr3);
 *
 */

const objectTraps = {
    get(state, prop) {
        if (prop === '__isProxy') {
            return true;
        }
		const { dataFetchers } = state;

        let nestedDataFetchers: any[] = [];
        let firstValue: any = undefined;

        // 如果Symbol.iterator，则返回数组的迭代器
        if (prop === Symbol.iterator) {
            if (Array.isArray(dataFetchers[0])) {
                return dataFetchers[0][Symbol.iterator].bind(dataFetchers[0]);
            }
        }

        for (let i = 0; i < dataFetchers.length; i++) {
            const dataFetcher = dataFetchers[i];
            
            if (dataFetcher && typeof prop === 'string' && prop in dataFetcher && dataFetcher[prop] !== undefined) {
                firstValue = dataFetcher[prop];
                nestedDataFetchers = dataFetchers.slice(i).map(item => 
                    item && typeof prop === 'string' && prop in item ? item[prop] : undefined
                ).filter(item => item !== undefined);
                
                break;
            }
        }
        
        // 如果没找到，返回undefined
        if (firstValue === undefined) {
            return undefined;
        }

        if (!isSimpleType(firstValue)) {
            return firstValue;
        }

        if (typeof firstValue === 'object' && firstValue !== null) {
            return createProxyProxy({
                parent: state,
                prop: prop
            }, ...nestedDataFetchers);
        }

        return firstValue;
	},

    has(state, prop) {
        const { dataFetchers } = state;
		return dataFetchers.some(item => prop in item);
	},

    ownKeys(state) {
        const { dataFetchers } = state;
		return dataFetchers.reduce((memo, item) => {
            const keys = Reflect.ownKeys(item);
            keys.forEach(key => {
                if (!memo.includes(key)) {
                    memo.push(key);
                }
            });
            return memo;
        }, []);
	},

    // 其实不应该设置值，但是为了兼容历史逻辑，需要进行处理
    set(state, prop, value) {
        let props = [ prop ];
        while (state) {
            if (state.data_) {
                let data = state.data_;
                props.slice(0, -1).forEach((prop) => {
                    if (!data[prop]) {
                        data[prop] = {};
                    }
                    data = data[prop];
                });
                data[props[props.length - 1]] = value;
            }
            if (state.prop_) {
                props.unshift(state.prop_);
            }
            state = state.parent_;
        }
        return true;
    },

    deleteProperty(state, prop) {
        console.warn('不支持');
        return false;
    },

    getOwnPropertyDescriptor(state, prop) {
        const { dataFetchers } = state;
        const owner = dataFetchers.find(item => prop in item);
		const desc = Reflect.getOwnPropertyDescriptor(owner, prop)
		if (!desc) return desc
		return {
			writable: true,
			configurable: !state.isArray || prop !== "length",
			enumerable: desc.enumerable,
			value: owner[prop]
		}
    },

    defineProperty() {
		console.warn('不支持');
		return false;
	},
	getPrototypeOf(state) {
		console.warn('不支持');
		return null;
	},
	setPrototypeOf() {
		console.warn('不支持');
		return false;
	}
}

const arrayTraps: ProxyHandler<any> = {};
Object.keys(objectTraps).forEach((key) => {
	// @ts-ignore
	arrayTraps[key] = function() {
		arguments[0] = arguments[0][0]
		return objectTraps[key].apply(this, arguments)
	}
});
arrayTraps.deleteProperty = function(state, prop) {
	console.log('deleteProperty尚未支持');
	return false;
}

export function createProxyProxy<T extends Objectish>(
    options: {
        parent?: any,
        prop?: string
    } = {},
    ...dataFetchers: T[]
) {
    if (!Array.isArray(dataFetchers) || dataFetchers.length === 0) {
        return dataFetchers;
    }

    try {
        const firstData = dataFetchers[0];
        const isArray = Array.isArray(firstData);
        const data_ = isArray ? [...firstData] : {};

        const state: any = {
            dataFetchers: data_ ? [data_, ...dataFetchers] : dataFetchers,
            isArray,
            draft_: null,
            data_: data_,
            parent_: options.parent,
            prop_: options.prop
        };

        let target: T = state;
        let traps: ProxyHandler<object | Array<any>> = objectTraps;

        if (isArray) {
            target = [ target ] as T;
            traps = arrayTraps;
        }

        const proxy = new Proxy(target, traps);

        state.draft_ = proxy;
        return proxy as any;
    }catch(error) {
        
    }
}

// @ts-ignore
window.createProxyProxy = createProxyProxy;