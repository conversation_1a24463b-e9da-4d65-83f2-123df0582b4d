import { LifeHooks } from '../../base/hooks';

export const ext = _.extend(LifeHooks(), {
  options: {
    cellEdit: false,
  },
  /**
   * 获取给表格使用的配置选项
   */
  getOptions() {
    const options = this.$$super.getOptions.apply(this);
    return options;
  },
  /**
   * 列表页【新建】按钮的调用方法
   */
  addHandle(data: any) {
    _.extend(data, {
      showDetail: true,
      show_type: 'full',
      fromAdd: true,
    });

    this.trigger('renderAction', data);
  },
});
