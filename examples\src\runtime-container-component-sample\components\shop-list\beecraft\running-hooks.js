import nodeHelper from '../../utils/nodeHelper.js';

export default {
  hooks: {
    created(node) {
      console.log('shop-list running hooks created', node);        
    },
    
    beforeRender(node, { query, actions }) {
      console.log('shop-list running hooks beforeRender', node, query, actions);
    },
    
    rendered(node, { query, actions }) {
      console.log('shop-list running hooks rendered', node, query, actions);
      
      // 获取容器组件实例
      const containerInstance = nodeHelper.getContainerInstanceByName(query, 'dht_web_container_product_list');
      console.log('containerInstance', containerInstance);
      
      // 获取当前组件实例
      const vm = query.instance(node.id);
      
      if (containerInstance && vm) {
        // 设置数据 - 只传递列表数据
        vm.listData = containerInstance.dhtPageData.list;
        vm.dhtContainerApi = containerInstance.dhtContainerApi;
        
              
        // 建立事件监听
        vm.$on('sort-change', (data) => {          
          // 使用容器组件的事件总线触发列表刷新事件
          if (containerInstance.eventBus) {
            const eventTypes = containerInstance.dhtContainerApi.getEventTypes();
            containerInstance.eventBus.$emit(eventTypes.LIST_REFRESH, { page: 1 });
          }
        });
        
        vm.$on('page-change', (data) => {
          // 使用容器组件的事件总线触发列表刷新事件
          if (containerInstance.eventBus) {
            const eventTypes = containerInstance.dhtContainerApi.getEventTypes();
            containerInstance.eventBus.$emit(eventTypes.LIST_REFRESH, { page: data.page });
          }
        });
        
        vm.$on('page-size-change', (data) => {          
          // 使用容器组件的事件总线触发列表刷新事件
          if (containerInstance.eventBus) {
            const eventTypes = containerInstance.dhtContainerApi.getEventTypes();
            containerInstance.eventBus.$emit(eventTypes.LIST_REFRESH, { 
              page: data.page,
              pageSize: data.pageSize
            });
          }
        });
        
        vm.$on('refresh', () => {
          // 使用容器组件的事件总线触发列表刷新事件
          if (containerInstance.eventBus) {
            const eventTypes = containerInstance.dhtContainerApi.getEventTypes();
            containerInstance.eventBus.$emit(eventTypes.LIST_REFRESH);
          }
        });
        
        vm.$on('product-click', (product) => {
          // 可以在这里处理商品点击事件，例如跳转到商品详情页
          console.log('Product clicked:', product);
        });
        
        vm.$on('add-to-cart', (product) => {
          // 可以在这里处理加入购物车事件
          console.log('Add to cart:', product);
        });
      }
    }
  }    
}