<script lang="ts">
import { getCrmListComponent } from '../../base/bridge';
import { parseParams } from '../../base/utils';
import BaseMixin from '../mixins/base';
import { ext } from './list';

const Component: VueComponent = {
  name: 'DhtModuleOrder',

  mixins: [BaseMixin],

  data() {
    return {
      objectApiName: 'SalesOrderObj',
    };
  },

  methods: {
    renderComponent(): Promise<any> {
      // 雷士照明转单demo
      const key = 'dht_crm_salesorderobj_data';
      const orderData = localStorage.getItem(key);
      if (orderData) {
        localStorage.removeItem(key);
        $dht.createOrder(JSON.parse(orderData));
      }

      return getCrmListComponent(this.objectApiName, 'SalesOrderObj', ext)
        .then((Ctor: BackboneComponent | null) => {
          if (!Ctor) {
            this.isEmpty = true;
            return;
          }
          if (CRM.util.isGrayListLayout(this.objectApiName)) {
            this.renderNewList(Ctor);
          } else {
            this.renderOldList(Ctor);
          }
        })
        .catch((err) => {
          console.error(err);
          this.isError = true;
        });
    },

    renderNewList(List: BackboneComponent) {
      const params = parseParams();
      const options = {};
      const listParam = [this.objectApiName, params[1]];
      const container = document.createElement('div');
      $(this.$el).empty().append(container);

      Fx.async('paas-appcustomization/runsdk', (appsutom: any) => {
        if (!appsutom || !appsutom.runningobjectlist) {
          throw new Error($t('i18n.fe_sail_v2.order.module_not_found'));
        }
        appsutom.runningobjectlist().then((ObjectList: any) => {
          if (params) {
            Object.assign(options, {
              param: listParam,
              source: 'list',
              apiname: this.objectApiName,
              apiName: this.objectApiName,
              recordType: params[1],
              jsPath: null,
              listModule: List,
            });
          }
          this.$$c.objectlist = ObjectList.init(container, options);
        });
      });
    },

    renderOldList(List: BackboneComponent) {
      const params = parseParams();

      this.$$c.list = new List({
        wrapper: $(this.$el),
        isEdit: false,
      });
      this.$$c.list.render([this.objectApiName, params[1]]);
    },
  },
};

export default Component;
</script>
