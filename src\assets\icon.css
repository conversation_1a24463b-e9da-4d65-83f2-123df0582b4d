@font-face {
  font-family: 'fsfont';
  src:  url('fonts/fsfont.eot?wnhzfw');
  src:  url('fonts/fsfont.eot?wnhzfw#iefix') format('embedded-opentype'),
    url('fonts/fsfont.ttf?wnhzfw') format('truetype'),
    url('fonts/fsfont.woff?wnhzfw') format('woff'),
    url('fonts/fsfont.svg?wnhzfw#fsfont') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="fsc-icon--"], [class*=" fsc-icon--"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'fsfont' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fsc-icon--cart:before {
  content: "\e905";
}
.fsc-icon--help:before {
  content: "\e901";
  color: #737c8c;
}
.fsc-icon--service:before {
  content: "\e902";
  color: #737c8c;
}
