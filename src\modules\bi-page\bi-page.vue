<script lang="ts">
import adapterModule from '../mixins/index.vue';
import { getBiModuleComponent, getBiCssComponent } from '../../base/utils';

const Component: VueComponent = {
  name: 'PortalBiPage',

  mixins: [adapterModule],

  data() {
    return {
      source: 'bi',
      dirname: 'page',
    };
  },

  methods: {
    getComponent(): string {
      if (this.subComponent === 'board') {
        return 'dashboard';
      }
      return this.subComponent;
    },

    getCtorOptions() {
      return {
        wrapper: $(this.$el),
        jsPath: this.jsdeps,
        modClassName: this.classNames,
        customRoute: `/portal${this.$query('appId')}/bi`,
        opt: {
          customRoute: `/portal${this.$query('appId')}/bi`,
        },
      };
    },

    async getModuleCtor() {
      await getBiCssComponent('all');
      const Ctor = await getBiModuleComponent(this.getComponent(), this.getComponentOptions());
      return Ctor;
    },
  },
};

export default Component;
</script>
