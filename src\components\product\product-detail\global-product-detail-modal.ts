interface ProductInterface {
  // 如果是商品详情需要传入商品Id
  spu_id?: string;
  // 商品详情和产品详情都需要传入产品Id，若是商品详情不传，则默认取第一个产品规格
  _id?: string;
}

interface ProductDetailOptions {
  // 标题
  title?: string;
  // 显示商品详情还是产品详情
  isSpuMode: boolean;
  // 当前的产品或商品信息
  product: ProductInterface;
  // 所有的Id，用于详情内切换浏览, 目前还不支持
  allProducts?: Array<ProductInterface>;
  // 关闭回调
  onClose?: Function;
}

export async function openProductDetail(options: ProductDetailOptions) {
  return new Promise((resolve) => {
    Fx.async([
      'vcrm/sdk',
    ], async (vcrmSdk: any) => {
      const productDetailService = await vcrmSdk.widgetService.getService('productDetailService');
      resolve(productDetailService.openProductDetail(options));
    });
  });
}
