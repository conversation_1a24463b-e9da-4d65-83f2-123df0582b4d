<template>
    <Editor ref="runtime" v-bind="options">
        <Frame></Frame>
    </Editor>
</template>
<script>
import Demo from '../components/Demo.vue';
import { Editor, Frame } from '@beecraft/core'; // 导入编辑器和页面框架组件
import { builtinPlugins } from '@beecraft/engine'; // 导入内置插件

import dht_web_container_product_list from './components/container/index.js';
import dht_web_product_list_shop_list from './components/shop-list/index.js';
import dht_web_product_list_category_tree from './components/category-tree/index.js';


export default {
    components: {
        Editor,
        Frame
    },
    created() {
        this.options = {
            resolver: {
                Demo,                
                dht_web_container_product_list,
                dht_web_product_list_shop_list,
                dht_web_product_list_category_tree,
            },
            import: [{
                name: 'FxTabs',
                data: {
                    value: 'label1'
                },
                children: [{
                    name: 'FxTabPane',
                    data: {
                        label: '页签1',
                        name: 'label1'
                    },
                    children: [{
                        name: 'dht_web_container_product_list',
                        data: {
                            style: {
                                justifyContent: 'center',
                                alignItems: 'center',
                            }
                        },
                        children: [
                            {
                                name: 'dht_web_product_list_category_tree',
                                data: {
                                    text: '分类树'
                                }
                            },
                            {
                                name: 'dht_web_product_list_shop_list'
                            }
                        ]
                    }]
                }, {
                    name: 'FxTabPane',
                    data: {
                        label: '页签2',
                        name: 'label2'
                    },
                    children: [{
                        name: 'FxTabs',
                        data: {
                            value: 'label1',
                            type: 'border-card'
                        },
                        children: [{
                            name: 'FxTabPane',
                            data: {
                                label: '页签1',
                                name: 'label1'
                            },
                            children: []
                        }, {
                            name: 'FxTabPane',
                            data: {
                                label: '页签2',
                                name: 'label2'
                            },
                            children: []
                        }]
                    }]
                }]
            }],
            enabled: false,
            plugins: [
                builtinPlugins.BuiltinComponentLibs
            ]
        }
    }
}
</script>