<template>
  <div class="dht-order-addsummary">
    <div class="wrap-flex">
      <div class="flex-left">
        <p>
          <span class="highlight">{{ kind }}</span> {{ $t('类商品') }}，
          {{ $t('共') }} <span class="highlight">{{ quantity }}</span> {{ $t('件') }}
        </p>
      </div>
      <div class="flex-right">
        <div class="item">
          <div class="item-label">{{ $t('商品金额') }}：</div>
          <div class="item-value">{{ iProductAmount }}</div>
        </div>
        <div class="item" v-show="isShowPolicyDynamicAmount">
          <div class="item-label">{{ $t('整单优惠金额') }}：</div>
          <div class="item-value">{{ iPolicyDynamicAmount }}</div>
        </div>
      </div>
    </div>
    <div class="summary">
      <div class="item">
        <div class="item-label">{{ $t('订单实付金额') }}：</div>
        <div class="item-value highlight bigsize">{{ iOrderAmount }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">

export default {
  name: 'DhtAddSummary',

  data() {
    return {
      isMultiRecordType: false, // 是否有多个订单产品业务类型，有多个表格就不显示订单相关金额
      kind: 0, // 种类
      quantity: 0, // 数量
      orderAmount: 0, // 订单实付金额
      productAmount: 0, // 产品金额
      policyDynamicAmount: 0, // 价格政策优惠金额
    };
  },

  computed: {
    iOrderAmount(): string {
      return `${$dht.config.currency.currencyFlag}${CRM.util.toMoney(this.orderAmount)}`;
    },

    iProductAmount(): string {
      return `${$dht.config.currency.currencyFlag}${CRM.util.toMoney(this.productAmount)}`;
    },

    iPolicyDynamicAmount(): string {
      return `${$dht.config.currency.currencyFlag}${CRM.util.toMoney(this.policyDynamicAmount)}`;
    },

    isShowPolicyDynamicAmount(): boolean {
      return this.policyDynamicAmount && +this.policyDynamicAmount !== 0;
    },
  },

  methods: {
    updateSummary(data: any) {
      const me = this as any;
      me.isMultiRecordType = data.isMultiRecordType;
      me.kind = data.kind;
      me.quantity = data.quantity;
      me.orderAmount = data.orderAmount;
      me.productAmount = data.productAmount;
      me.policyDynamicAmount = data.policyDynamicAmount;
    },
  },
} as VueComponent;
</script>

<style lang="less">
.dht-order-addsummary {
  font-size: 12px;
  color: #666666;
  padding: 16px;
  border: 1px solid #DEE1E6;
  border-top: none;
  .wrap-flex {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }
  .item {
    margin-bottom: 8px;
    text-align: right;
    &-label {
      display: inline-block;
    }
    &-value {
      display: inline-block;
      width: 100px;
      text-align: right;
      color: #333;
      font-weight: bolder;
    }
  }
  .summary {
    border-top: 1px solid #DEE1E6;
    padding-top: 10px;
  }
  .highlight {
    color: #FC5C08;
  }
  .bigsize {
    font-size: 16px;
  }
}
</style>
