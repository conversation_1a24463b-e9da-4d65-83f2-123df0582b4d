import nodeHelper from '../../utils/nodeHelper.js';

export default {
  hooks: {
    created(node) {
      console.log('search-box running hooks created', node);        
    },
    
    beforeRender(node, { query, actions }) {
      console.log('search-box running hooks beforeRender', node, query, actions);
    },
    
    rendered(node, { query, actions }) {
      console.log('search-box running hooks rendered', node, query, actions);
      
      // 获取容器组件实例
      const containerInstance = nodeHelper.getContainerInstanceByName(query, 'dht_web_container_product_list');
      console.log('containerInstance', containerInstance);
      
      // 获取当前组件实例
      const vm = query.instance(node.id);
      
      if (containerInstance && vm) {
        // 设置数据 - 只传递搜索数据
        vm.search = containerInstance.dhtPageData.search;
        vm.dhtContainerApi = containerInstance.dhtContainerApi;
        vm.dhtPageEventTypes = containerInstance.dhtContainerApi.getEventTypes();
        
        // 建立事件监听
        vm.$on('search', (data) => {
          // 使用容器组件的事件总线触发搜索事件
          if (containerInstance.eventBus) {
            const eventTypes = containerInstance.dhtContainerApi.getEventTypes();
            containerInstance.eventBus.$emit(eventTypes.SEARCH_CHANGE, {
              keyword: data.keyword
            });
          }
        });
      }
    }
  }    
}