<template>
    <div class="fk-input-number"
      :class="{
        'fk-input-number-focused': isFocus,
        'fk-input-number-disabled': disabled,
    }">
      <div class="fk-input-number-handler-wrap" @mousedown.prevent>
        <span
          class="fk-input-number-handler fk-input-number-handler-up"
          :class="{'fk-input-number-handler-disabled': upDisabled}"
          unselectable="unselectable"
          role="button"
          aria-label="Increase Value"
          @click="upClick">
          <i class="el-icon-arrow-up fk-input-number-handler-up-inner"></i>
        </span>
        <span class="fk-input-number-handler fk-input-number-handler-down"
              :class="{'fk-input-number-handler-disabled': downDisabled}"
              unselectable="unselectable"
              role="button"
              aria-label="Decrease Value"
              @click="downClick">
          <i class="el-icon-arrow-down fk-input-number-handler-down-inner"></i>
        </span>
      </div>
      <div class="fk-input-number-input-wrap">
        <input
          ref="fkInputNumber"
          autocomplete="off"
          step="1"
          :disabled="disabled"
          class="fk-input-number-input"
          v-model.trim="innerValue"
          @focus="onfocus($event)"
          @blur="onblur($event)"
          @keydown.enter="onEnter($event)">
      </div>
    </div>
</template>

<script src="./_fk-input-number.ts" lang="ts"></script>
<style src="./_fk-input-number.less" lang="less" scoped></style>
