<template>
    <div class="dht-shopmall-common-setting">
        <div class="content-list">
            <div class="list-item" :class="{active: index === 1}" v-for="index in 10" :key="index">
                <p class="list-item-td" v-for="index in 5" :key="index"></p>
            </div>
        </div>
    </div>
</template>

<script>
module.exports = Vue.extend({
    name: "ShopMallCommonPre",

    template: '__template__',
})
</script>

<style lang="less" scoped>
.dht-shopmall-common-setting {
    .content-list {
        border: 1px solid var(--color-neutrals04);
        .list-item {
            display: flex;
            padding: 15px;
            border-bottom: 1px solid var(--color-neutrals04);
            box-sizing: border-box;
        }
        .active {
            background-color: var(--color-neutrals03);
        }
        .list-item-td {
            width: 12%;
            height: 10px;
            background-color: var(--color-neutrals05);
            border-radius: 3px;
            margin-right: 50px;
        }
    }
}
</style>