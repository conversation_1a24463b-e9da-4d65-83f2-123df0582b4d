.dht-unit-selector {
  display: inline-block;
  cursor: pointer;
  font-size: 14px;
  &-list {
    &-item {
      display: flex;
      justify-content: space-between;
      cursor: pointer;
      padding: 4px 0 4px 19px;
      &:hover {
        background-color: #f0f4fc;
      }
      &.checked {
        padding-left: 0;
        .el-icon-check {
          display: inline-block;
        }
      }
      &-unit {
        .el-icon-check {
          margin-right: 5px;
          color: #3487E2;
          display: none;
        }
      }
      &-tip {
        color: #999;
      }
    }
  }
}

.dht-popover-no-padding {
  padding: 0;
}
