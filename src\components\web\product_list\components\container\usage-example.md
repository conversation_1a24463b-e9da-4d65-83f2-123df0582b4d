# 商品列表容器组件使用指南

## 概述

商品列表容器组件是一个低代码设计器友好的容器组件，它提供了统一的事件总线和数据管理机制，支持多个子组件之间的协调工作。

## 核心特性

### 1. 统一的事件总线 (EventBus)
- 基于Vue实例的事件系统
- 标准化的事件类型定义
- 子组件间解耦通信

### 2. 响应式数据对象 (bizPageData)
- 集中式状态管理
- 自动响应数据变化
- 清晰的数据结构

### 3. 容器方法 (containerMethods)
- 提供通用工具方法
- 统一的查询参数构建
- 组件重置功能

## 数据结构

### bizPageData 结构说明

```javascript
{
  // 分类信息
  category: {
    id: null,           // 分类ID
    name: '',           // 分类名称
    path: []            // 分类路径数组
  },
  
  // 搜索信息
  search: {
    keyword: '',        // 搜索关键字
    timestamp: 0        // 搜索时间戳
  },
  
  // 筛选条件
  filters: {
    price: { min: null, max: null },  // 价格范围
    brand: [],                        // 品牌筛选
    attributes: {},                   // 属性筛选
    custom: {}                        // 自定义筛选
  },
  
  // 列表状态
  list: {
    loading: false,     // 加载状态
    data: [],           // 列表数据
    total: 0,           // 总数
    page: 1,            // 当前页
    pageSize: 20,       // 分页大小
    error: null         // 错误信息
  },
  
  // 组件状态
  components: {
    categoryTree: { expanded: [], selected: null },
    shopList: { viewMode: 'grid', sortBy: 'default' },
    filters: { visible: true, collapsed: false }
  }
}
```

### 事件类型定义

```javascript
const EVENT_TYPES = {
  // 分类相关
  CATEGORY_CHANGE: 'category:change',
  CATEGORY_RESET: 'category:reset',
  
  // 搜索相关  
  SEARCH_CHANGE: 'search:change',
  SEARCH_RESET: 'search:reset',
  
  // 筛选相关
  FILTER_CHANGE: 'filter:change', 
  FILTER_RESET: 'filter:reset',
  
  // 列表相关
  LIST_REFRESH: 'list:refresh',
  LIST_LOADING: 'list:loading',
  LIST_ERROR: 'list:error',
  LIST_DATA_UPDATE: 'list:data:update',
  
  // 全局重置
  GLOBAL_RESET: 'global:reset'
}
```

## 子组件开发规范

### 1. Props 定义

每个子组件都应该接收以下props：

```javascript
props: {
  // 事件总线
  eventBus: {
    type: Object,
    required: true
  },
  // 业务数据
  bizPageData: {
    type: Object,
    required: true
  },
  // 容器方法
  containerMethods: {
    type: Object,
    required: true
  }
}
```

### 2. 事件监听

```javascript
created() {
  this.initEventListeners();
},

beforeDestroy() {
  this.removeEventListeners();
},

methods: {
  initEventListeners() {
    const EVENT_TYPES = this.containerMethods.getEventTypes();
    this.eventBus.$on(EVENT_TYPES.SOME_EVENT, this.handleSomeEvent);
  },
  
  removeEventListeners() {
    const EVENT_TYPES = this.containerMethods.getEventTypes();
    this.eventBus.$off(EVENT_TYPES.SOME_EVENT, this.handleSomeEvent);
  }
}
```

### 3. 事件触发

```javascript
methods: {
  handleUserAction() {
    const EVENT_TYPES = this.containerMethods.getEventTypes();
    
    // 触发事件
    this.eventBus.$emit(EVENT_TYPES.CATEGORY_CHANGE, {
      id: categoryId,
      name: categoryName,
      path: categoryPath
    });
  }
}
```

## 在beecraft中的使用

### 1. 容器组件配置

```javascript
// beecraft/index.js
export default function () {
  return {
    name: 'dht_web_container_product_list',
    displayName: '商品列表容器',
    $$data: {
      template: {
        name: 'dht_web_container_product_list',
        children: [
          {
            name: 'dht_web_product_list_category_tree',
            data: { /* 分类树配置 */ }
          },
          {
            name: 'dht_web_product_list_search_box',
            data: { /* 搜索框配置 */ }
          },
          {
            name: 'dht_web_product_list_shop_list',
            data: { /* 商品列表配置 */ }
          }
        ]
      },
      isCanvas: true
    }
  }
}
```

### 2. 子组件在容器中的使用

```vue
<template>
  <dht-web-container-product-list>
    <!-- 分类树 -->
    <dht-web-category-tree
      v-bind="$attrs"
      :eventBus="eventBus"
      :bizPageData="bizPageData"
      :containerMethods="containerMethods">
    </dht-web-category-tree>
    
    <!-- 搜索框 -->
    <dht-web-search-box
      v-bind="$attrs"
      :eventBus="eventBus"
      :bizPageData="bizPageData"
      :containerMethods="containerMethods">
    </dht-web-search-box>
    
    <!-- 商品列表 -->
    <dht-web-shop-list
      v-bind="$attrs"
      :eventBus="eventBus"
      :bizPageData="bizPageData"
      :containerMethods="containerMethods">
    </dht-web-shop-list>
  </dht-web-container-product-list>
</template>
```

## 扩展第三方组件

### 1. 创建适配器组件

```javascript
// third-party-adapter.vue
export default {
  name: 'ThirdPartyAdapter',
  props: {
    eventBus: Object,
    bizPageData: Object,
    containerMethods: Object
  },
  
  created() {
    // 监听容器事件
    this.eventBus.$on('list:data:update', this.updateThirdPartyComponent);
  },
  
  methods: {
    updateThirdPartyComponent(data) {
      // 更新第三方组件
      this.$refs.thirdPartyComponent.updateData(data);
    },
    
    handleThirdPartyEvent(eventData) {
      // 将第三方组件事件转换为容器事件
      this.eventBus.$emit('filter:change', eventData);
    }
  }
}
```

### 2. 注册到beecraft

```javascript
// 第三方组件的beecraft配置
export default function () {
  return {
    name: 'third_party_filter',
    displayName: '第三方筛选组件',
    related: {
      previewDisplay: () => import('./third-party-adapter.vue')
    }
  }
}
```

## 最佳实践

### 1. 数据流向
- 容器 → 子组件：通过 bizPageData 传递
- 子组件 → 容器：通过 eventBus 事件
- 子组件 ↔ 子组件：通过容器中转

### 2. 错误处理
- 统一在容器中处理API错误
- 子组件只处理UI相关错误
- 提供友好的错误提示

### 3. 性能优化
- 使用计算属性监听数据变化
- 及时清理事件监听器
- 避免频繁的事件触发

### 4. 调试技巧
- 在控制台查看事件流
- 使用Vue DevTools监控数据变化
- 添加详细的日志输出

## 常见问题

### Q: 如何添加新的筛选条件？
A: 在 bizPageData.filters 中添加新字段，然后在相关组件中监听和处理。

### Q: 如何实现组件间的复杂交互？
A: 通过容器方法提供复杂的业务逻辑，子组件调用容器方法而不是直接通信。

### Q: 如何处理异步数据加载？
A: 在容器中统一处理异步操作，通过 bizPageData.list.loading 状态通知子组件。
