<template>
    <div class="bc-editor w-100 h-100" v-if="loaded">
        <slot></slot>
    </div>
</template>
<script lang="ts">
import invariant from 'tiny-invariant';
import { ERROR_RESOLVER_NOT_AN_OBJECT, ERROR_INIT_CONTEXT, deepMerge } from '@beecraft/shared';
import { useEditorStore, normalizeResolver } from './store';
import { createProxyProxy } from '../shared/proxy';

const editorManager = new Map();
let rawSetAttribute = null;

export default {
    inheritAttrs: false,
    props: {
        import: {
            type: String
        },
        resolver: {
            type: Object,
            default(){
                return {};
            }
        },
        enabled: {
            type: Boolean,
            default: true
        },
        plugins: {
            type: Array
        }
    },

    provide() {
        return {
            useInternalEditor: this.useInternalEditor.bind(this),
            useEventHandler: this.useEventHandler.bind(this)
        }
    },

    data() {
        return {
            loaded: false
        }
    },

    created() {
        this.__effectDOM();
        this.__editorId = this.$props.id;
        editorManager.set(this.__editorId, this);


        const options = { ...this.$props, ...this.$attrs };

        const context = this.useContext({
            plugins: this.plugins,
            enabled: true
        });

        context.pluginService.run('beecraft.init.before', { options }).then((rst) => {
            rst = rst.StatusCode === 0 ? rst.Value : {};
            // rst是插件扩展的值，options为初始化的状态，优先级大于插件扩展
            rst = deepMerge(rst, options);

            if (rst.resolver) {
                invariant(
                    typeof rst.resolver === 'object' && !Array.isArray(rst.resolver),
                    ERROR_RESOLVER_NOT_AN_OBJECT
                );
                rst.resolver = normalizeResolver(rst.resolver);
            }

            context.actions.history.ignore().setOptions((options) => {
                let normalizeNode;
                if (rst.normalizeNodes?.length) {
                    normalizeNode = function (node) {
                        rst.normalizeNodes.forEach((normalizeNode) => {
                            normalizeNode(node, context)
                        })
                    }
                }
                deepMerge(options, rst, { normalizeNode });
            })

            this.loaded = true;
            const editor = this.editor = window.editor = context;
            this.$emit('onCreated', editor);
        });
    },

    methods: {

        useInternalEditor(collector = (state, query) => state) {
            const store = this.useContext();
            // const proxyState = createProxyProxy(store.query.getState());
            const proxyState = store.query.getState();
            const collected = collector(proxyState, store.query);
            const handler = this.useEventHandler();

            if (!this.connectorsUsage) {
                this.connectorsUsage = handler && handler.createConnectorsUsage();
                this.connectorsUsage.register();
            }

            return {
                ...collected,
                connectors: this.connectorsUsage.connectors,
                ...store,
                destroy() {
                    store.destroy();
                }
            }
        },

        useContext(options) {
            if (!this.context) {
                this.context = useEditorStore(options);
                invariant(this.context, ERROR_INIT_CONTEXT);
            }

            return this.context
        },

        useEventHandler() {
            if (!this.handler) {
                const store = this.useContext();

                this.handler = store.query.getOptions().handlers(store);
            }

            return this.handler;
        },

        //副作用修改dom
        // 代理对象作为属性传递给组件时，会被vue框架挂在到dom属性上，但是代理对象无法往dom的属性上设置，因此需要通过修改原型方法的方式来变更数据
        __effectDOM() {
            if (!editorManager.size) {
                rawSetAttribute = HTMLElement.prototype.setAttribute;
                HTMLElement.prototype.setAttribute = function(prop, value, ...rest){
                    if (typeof value === 'object' && value.__isProxy){
                        try {
                            value = JSON.parse(JSON.stringify(value));
                        } catch (error) {
                            console.error(error);
                        }
                    }
                    return rawSetAttribute.apply(this, [prop, value, ...rest]);
                }
            }
        },

        __uneffectDOM() {
            if(!editorManager.size) {
                HTMLElement.prototype.setAttribute = rawSetAttribute;
                rawSetAttribute = null;
            }
        }
    },

    beforeDestroy() {
        const editor: any = useEditorStore();
        editorManager.delete(this.__editorId);

        this.__uneffectDOM();

        editor.destroy();
    }

}
</script>