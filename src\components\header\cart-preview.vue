<template>
  <div class="dht-carts-layer">
    <div class="carts-hd">{{ $t('最近加入的商品') }}</div>
    <div class="carts-bd">
      <div v-for="(item, idx) in list" :key="idx" class="carts-item">
        <div class="item-wrapper">
          <img class="item-img" :src="getImageUrlByNPath(item.image)"/>
        </div>
        <div class="item-detail">
          <div class="item-name">{{ item.product_id__r || '--' }}</div>
          <!-- <div class="item-spec">{{ item.spec }}</div> -->
        </div>
        <div class="item-price">
          <em class="w1">{{ formatMoney(item.price) }}</em> x
          <span class="w2">{{ item.quantity }}</span>
        </div>
        <div class="item-oper" @click="removeCartItem(item._id)">{{ $t('删除') }}</div>
      </div>
    </div>
    <div class="carts-ft">
      <div class="carts-count">{{ $t('共') }}<em>{{ list.length }}</em>{{ $t('种商品') }}</div>
      <div class="carts-button">
        <fx-button size="small" type="primary" @click="go2cart">{{ $t('去购物车') }}</fx-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'DhtHeaderCarts',
  data() {
    return {
      list: [],
    };
  },
  created() {
    this._onUpdateCart = () => {
      console.log('Update cart count event');
      this.updateCart();
    };
    const store = window.$dht?.store;
    if (store) {
      store.on('store:cart.list.reset', this._onUpdateCart);
      store.on('store:cart.list.batchUpdate', this._onUpdateCart);
    }
  },
  mounted() {
    // 需要预判购物车简单数据已经提前加载
    this.updateCart();
  },
  beforeDestroy() {
    const store = window.$dht?.store;
    store.off('store:cart.list.reset', this._onUpdateCart);
    store.off('store:cart.list.batchUpdate', this._onUpdateCart);
    this._onUpdateCart = null;
  },
  methods: {
    formatMoney(money: string | number, currency: string): string {
      const $global: any = global;
      const str = $global.CRM.util.formatMoneyForCurrency(money, currency);
      // 主站的代码真是蛋疼，多此一举
      return str.replace('<em>', '').replace('</em>', '');
    },
    updateCart() {
      this.list = window.$dht.store.getCartList() || [];
      this.$emit('updatecartlist', this.list);
      // TODO 强制全刷有点霸道，能不能优雅一点？？
      this.$forceUpdate();
    },
    getImageUrlByNPath(npath: string) {
      const host = global.location.hostname.replace(/^(dht|www|dev|wow)/, 'img');
      if (!npath) {
        if($dht.config.placeholderImg.path){
          const placeholderImg = window.$dht.config.placeholderImg.path;
          return `//${host}/image/o/${placeholderImg}/50*50/webp/${window.$dht.appId}`;
        }
        return 'https://a9.fspage.com/FSR/weex/avatar/object_list/images/list_default_icon.png';
      }
      return `//${host}/image/o/${npath}/50*50/webp/${window.$dht.appId}`;
    },
    removeCartItem(cartId: string) {
      window.$dht.services.cart.batchRemove([cartId]);
    },
    go2cart() {
      window.location.hash = `/portal${window.$dht.appId}/list/=/ShoppingCartObj`;
    },
  },
} as VueComponent;
</script>

<style lang="less">
.dht-carts-layer {
  position: absolute;
  width: 420px;
  background-color: #fff;
  border: 1px solid #ff8000;
  border-radius: 4px;
  border-top-right-radius: 0;

  .carts-hd {
    line-height: 16px;
    padding: 10px 16px 0;
    font-size: 12px;
    font-weight: bold;
  }

  .carts-bd {
    padding: 0 16px;
    // max-height: 354px;
    max-height: 280px;
    overflow-y: auto;
  }

  .carts-ft {
    display: flex;
    justify-content: space-between;
    line-height: 32px;
    padding: 8px 16px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    background: #f6f6f6;
  }

  .carts-item {
    display: flex;
    line-height: 18px;
    padding: 8px 0;
    border-bottom: 1px solid #F0F2F5;
    align-items: center;

    .item-img {
      display: block;
      width: 36px;
      height: 36px;
    }

    .item-detail {
      width: 100%;
      margin-left: 8px;
    }

    .item-price {
      margin-left: 8px;
      white-space: nowrap;
    }

    .w1, .w2 {
      display: inline-block;
    }

    .w1 {
      width: 90px;
      font-size: 12px;
      font-weight: bold;
      color: #FF8000;
      text-align: right;
    }

    .w2 {
      width: 30px;
    }

    .item-oper {
      margin-left: 8px;
      width: 24px;
      white-space: nowrap;
      color: #91959E;
      cursor: pointer;
    }
  }

  .carts-item:last-child {
    border-bottom: 0 none;
  }
}
</style>
