<template>
  <div :class="wrapperClass">
    <div class="filter-title">筛选</div>
    <div class="filter-lines">
      <div class="filter-line" v-for="n in lineCount" :key="n"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FilterDisplay',
  props: {
    direction: { type: String, default: 'column' }, // 'column' or 'row'
    lineCount: { type: Number, default: 6 }
  },
  computed: {
    wrapperClass() {
      return this.direction === 'row' ? 'filter-top' : 'filter-left';
    }
  }
}
</script>

<style lang="less" scoped>

.filter-left {
  width: 100%;
}
.filter-title {
    font-size: 12px;
    color: var(--color-neutrals11);
    margin-bottom: 14px;
}
.filter-lines {
    display: flex;
    flex-direction: column;
    gap: 12px;
}
.filter-line {
    width: 88px;
    height: 4px;
    background: #EAEBED;
    border-radius: 4px;
    margin-bottom: 24px;
}
.filter-top {
    display: flex;
    margin-bottom: 10px;
    .filter-title {
        width: 80px;
        margin-bottom: 0;
    }
    .filter-lines {
        flex: 1;
        flex-direction: row;
        display: flex;
        align-items: center;
        gap: 12px;
    }
    .filter-line {
        margin-bottom: 0;
    }
}


</style> 