@layer-height: 40px;
@color-text-gray: #aaa;

@keyframes preloader-spin {
  100% {
    transform: rotate(360deg);
  }
}

.preloaderMixin () {
  width: 20px;
  height: 20px;
  animation: preloader-spin 1s steps(12, end) infinite;
  &:after {
    display: block;
    width: 100%;
    height: 100%;
    content: "";
    background-image: url("data:image/svg+xml;charset=utf-8,<svg viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><defs><line id='l' x1='60' x2='60' y1='7' y2='27' stroke='%236c6c6c' stroke-width='11' stroke-linecap='round'/></defs><g><use xlink:href='%23l' opacity='.27'/><use xlink:href='%23l' opacity='.27' transform='rotate(30 60,60)'/><use xlink:href='%23l' opacity='.27' transform='rotate(60 60,60)'/><use xlink:href='%23l' opacity='.27' transform='rotate(90 60,60)'/><use xlink:href='%23l' opacity='.27' transform='rotate(120 60,60)'/><use xlink:href='%23l' opacity='.27' transform='rotate(150 60,60)'/><use xlink:href='%23l' opacity='.37' transform='rotate(180 60,60)'/><use xlink:href='%23l' opacity='.46' transform='rotate(210 60,60)'/><use xlink:href='%23l' opacity='.56' transform='rotate(240 60,60)'/><use xlink:href='%23l' opacity='.66' transform='rotate(270 60,60)'/><use xlink:href='%23l' opacity='.75' transform='rotate(300 60,60)'/><use xlink:href='%23l' opacity='.85' transform='rotate(330 60,60)'/></g></svg>");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
  }
}

.pull-to-refresh-layer {
  position: relative;
  left: 0;
  top: 0;
  width: 100%;
  height: @layer-height;
  color: @color-text-gray;

  .preloader {
    visibility: hidden;
    .preloaderMixin();
  }

  .pull-to-refresh-arrow {
    width: 20px;
    height: 20px;
    background: no-repeat center;
    background-image: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 26 40'><polygon points='9,22 9,0 17,0 17,22 26,22 13.5,40 0,22' fill='%238c8c8c'/></svg>");
    background-size: 10.4px 16px;
    z-index: 10;
    transform: rotate(0deg) translate3d(0, 0, 0);
    transition-duration: 300ms;
    margin-left: -20px;
  }
}

.fk-scroll {
  position: absolute;
  top: -@layer-height;
  right: 0;
  bottom: 0;
  left: 0;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  &.no-refresh {
    top: 0;
  }

  &.touching .scroll-inner {
    transition-duration: 0ms;
  }
  &:not(.refreshing) {
    .pull-to-refresh-layer .preloader {
      animation: none;
    }
  }
  &.refreshing {
    .pull-to-refresh-arrow {
      visibility: hidden;
      transition-duration: 0ms;
    }
    .preloader {
      visibility: visible;
    }
  }
  &.pull-up {
    .pull-to-refresh-arrow {
      transform: rotate(180deg) translate3d(0, 0, 0);
    }
  }
}

.scroll-inner {
  position: absolute;
  /* top: -$layer-height; */
  top: 0;
  width: 100%;
  transition-duration: 300ms;
}

.label-down, .label-up, .label-refresh {
  display: none;
  text-align: center;
}

.pull-down .label-down,
.pull-up .label-up,
.refreshing .label-refresh {
  display: block;
  width: 5.5em;
}

.pull-to-refresh-layer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.infinite-layer {
  height: @layer-height;
  display: flex;
  align-items: center;
  justify-content: center;
  color: @color-text-gray;
}

.infinite-preloader {
  .preloaderMixin();
}

.label-loading {
  display: block;
  width: 5.5em;
  text-align: center;
}
