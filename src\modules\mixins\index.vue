<template>
  <div class="portal-module-content crm-module-wrap" :class="classNames"></div>
</template>

<script lang="ts">
import { getCrmModuleComponent, getCrmCssComponent, parseParams } from '../../base/utils';

const Component: VueComponent = {
  inheritAttrs: false,
  props: ['module', 'component', 'subComponent'],

  data() {
    return {
      source: 'crm',
      dirname: 'page',
    };
  },

  computed: {
    classNames() {
      const dirname = this.dirname;
      const component = this.subComponent || this.component;
      const apiName = this.getObjectApiName('apiName');
      const pathClsName = this.module.replace('/', '-');
      const apiNameClsName = `${this.source}-${dirname.slice(0, 1)}-${apiName?.toLocaleLowerCase()}`;
      const crmStyleCompatible = `${this.source}-${dirname} ${this.source}-${dirname.slice(0, 1)}-${component}`;
      return `portal-${pathClsName} ${crmStyleCompatible} ${apiNameClsName}`;
    },
    jsdeps() {
      const filename = [
        `${Fx.ROOT_PATH}/${this.source}${Fx.PATH_SUFFIX}/modules`,
        this.dirname,
        this.module || 'list',
        this.subComponent || this.component || 'list',
      ].join('/');
      return `${filename}.js`;
    },
  },

  mounted() {
    this.renderComponent();
  },

  destroyed() {
    if (this.inse) {
      if (_.isFunction(this.inse.destroy)) {
        this.inse.destroy();
      }
      if (_.isFunction(this.inse.$destroy)) {
        this.inse.$destroy();
      }
      this.inse = null;
    }
  },

  methods: {
    getComponent() {
      return this.module;
    },

    getComponentOptions() {
      return {
        dirname: this.dirname,
      };
    },

    renderEmpty() {
      const empty: any = document.createElement('p');
      const text = document.createTextNode($t('该页面不存在！'));

      empty.style = 'padding: 48px;font-size: 20px;text-align: center;';
      empty.appendChild(text);
      this.$el.appendChild(empty);
    },

    getCtorOptions() {
      return {
        wrapper: $(this.$el),
        jsPath: this.jsdeps,
        modClassName: this.classNames,
        // listOptions: { // TODO 参数在755版本之前暂时不生效，755底层列表页修改
        //   tableOptions: {
        //     searchTerm: null, // 表头移除场景
        //   },
        // },
      };
    },

    getModuleCtor(): Promise<any> {
      const componentName = this.getComponent();
      const componentOptions = this.getComponentOptions();
      console.log(componentName, componentOptions);
      return Promise.all([
        getCrmCssComponent(this.dirname),
        getCrmModuleComponent(componentName, componentOptions),
      ]).then((value: any[]) => {
        console.log(value);
        return value[1];
      }).catch((err) => {
        console.error(err);
      });
    },

    renderComponent(): Promise<any> {
      return this.getModuleCtor().then((Ctor: any) => {
        if (Ctor) {
          const options = this.getCtorOptions();
          const params = parseParams();
          // this.inse = new Ctor(options);
          // this.inse.render([this.getObjectApiName(), params[1]]);
          const ctor = new Ctor(options);
          if (ctor instanceof Promise) {
            ctor.then((Ctor2) => {
              this.inse = new Ctor2(options);
              this.inse.render([this.getObjectApiName(), params[1]]);
            });
          } else {
            this.inse = ctor;
            this.inse.render([this.getObjectApiName(), params[1]]);
          }
        } else {
          this.renderEmpty();
        }
      });
    },
  },
};

export default Component;
</script>

<style lang="less">
.portal-module-content {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .crm-module-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  // 产品的卡片模式盒子
  .map-box {
    position: relative;
    width: 100%;
    z-index: 10;
  }
}
</style>
