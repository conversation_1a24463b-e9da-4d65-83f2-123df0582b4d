// @vue/component
import CardOrderItem from './card-order-item/card-order-item.vue';

export default Vue.extend({
  name: 'CardOrder',

  components: { CardOrderItem },

  mixins: [],

  props: {
    items: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      selectItem: null,
    };
  },

  computed: {},

  watch: {},

  created() {},

  methods: {
    getSelectItem() {
      const me = this as any;
      return me.selectItem;
    },

    selectChange(item: any) {
      const me = this as any;
      if (me.selectItem) {
        if (me.selectItem.fieldName === item.fieldName) {
          me.toggleItem(item);
        } else {
          me.selectItem.status = 0;
          this.toggleItem(item);
          me.selectItem = item;
        }
      } else {
        me.toggleItem(item);
        me.selectItem = item;
      }
      me.$emit('change', me.selectItem);
    },

    toggleItem(item: any) {
      if (item.status === 0) {
        item.status = 1;
      } else if (item.status === 1) {
        item.status = 2;
      } else {
        item.status = 1;
      }
    },

    selectDefaultOrder() {
      const me = this as any;
      if (me.selectItem === null) return;
      me.selectItem.status = 0;
      me.selectItem = null;
      me.$emit('change', me.selectItem);
    },
  },
});
